// 全局API配置文件
const API_CONFIG = {
  // 基础配置
  BASE_URL: 'http://localhost:8080/api',
  TIMEOUT: 10000,
  
  // 各端API端口配置
  PORTS: {
    USER: 8081,      // 用户端API端口
    MERCHANT: 8082,  // 商家端API端口
    ADMIN: 8080      // 管理端API端口
  },
  
  // API端点配置
  ENDPOINTS: {
    // 用户端API
    USER: {
      BASE: 'http://localhost:8081/api',
      AUTH: {
        WX_LOGIN: '/user/wx-login',
        GET_INFO: '/user/info',
        UPDATE_INFO: '/user/update',
        BIND_PHONE: '/user/bind-phone',
        LOGOUT: '/user/logout'
      },
      SERVICES: {
        LIST: '/services',
        DETAIL: '/services/{id}',
        CATEGORIES: '/services/categories',
        SEARCH: '/services/search',
        RECOMMEND: '/services/recommend',
        FAVORITES: '/services/favorites',
        FAVORITE: '/services/{id}/favorite'
      },
      ORDERS: {
        LIST: '/orders',
        DETAIL: '/orders/{id}',
        CREATE: '/orders',
        CANCEL: '/orders/{id}/cancel',
        PAY: '/orders/{id}/pay',
        CONFIRM: '/orders/{id}/confirm',
        EVALUATE: '/orders/{id}/evaluate'
      },
      ADDRESS: {
        LIST: '/addresses',
        DETAIL: '/addresses/{id}',
        CREATE: '/addresses',
        UPDATE: '/addresses/{id}',
        DELETE: '/addresses/{id}',
        SET_DEFAULT: '/addresses/{id}/default'
      },
      MERCHANTS: {
        LIST: '/merchants',
        DETAIL: '/merchants/{id}',
        SERVICES: '/merchants/{id}/services',
        NEARBY: '/merchants/nearby'
      }
    },
    
    // 商家端API
    MERCHANT: {
      BASE: 'http://localhost:8082/api',
      AUTH: {
        LOGIN: '/merchant/login',
        LOGOUT: '/merchant/logout',
        INFO: '/merchant/info',
        UPDATE_INFO: '/merchant/update'
      },
      ORDERS: {
        LIST: '/merchant/orders',
        DETAIL: '/merchant/orders/{id}',
        ACCEPT: '/merchant/orders/{id}/accept',
        REJECT: '/merchant/orders/{id}/reject',
        START: '/merchant/orders/{id}/start',
        COMPLETE: '/merchant/orders/{id}/complete',
        STATS: '/merchant/orders/stats'
      },
      SERVICES: {
        LIST: '/merchant/services',
        DETAIL: '/merchant/services/{id}',
        CREATE: '/merchant/services',
        UPDATE: '/merchant/services/{id}',
        DELETE: '/merchant/services/{id}',
        TOGGLE_STATUS: '/merchant/services/{id}/toggle-status',
        STATS: '/merchant/services/stats'
      },
      EARNINGS: {
        OVERVIEW: '/merchant/earnings/overview',
        LIST: '/merchant/earnings/list',
        WITHDRAW: '/merchant/earnings/withdraw',
        WITHDRAW_RECORDS: '/merchant/earnings/withdraw-records',
        STATS: '/merchant/earnings/stats'
      },
      DASHBOARD: {
        OVERVIEW: '/merchant/dashboard/overview',
        STATS: '/merchant/dashboard/stats'
      }
    },
    
    // 管理端API
    ADMIN: {
      BASE: 'http://localhost:8080/api',
      AUTH: {
        LOGIN: '/admin/login',
        LOGOUT: '/admin/logout',
        INFO: '/admin/info'
      },
      USERS: {
        LIST: '/admin/users',
        DETAIL: '/admin/users/{id}',
        UPDATE_STATUS: '/admin/users/{id}/status',
        STATS: '/admin/users/stats'
      },
      MERCHANTS: {
        LIST: '/admin/merchants',
        DETAIL: '/admin/merchants/{id}',
        AUDIT: '/admin/merchants/{id}/audit',
        UPDATE_STATUS: '/admin/merchants/{id}/status',
        STATS: '/admin/merchants/stats'
      },
      ORDERS: {
        LIST: '/admin/orders',
        DETAIL: '/admin/orders/{id}',
        INTERVENE: '/admin/orders/{id}/intervene',
        REFUND: '/admin/orders/{id}/refund',
        STATS: '/admin/orders/stats'
      },
      DASHBOARD: {
        OVERVIEW: '/admin/dashboard/overview',
        STATS: '/admin/dashboard/stats'
      }
    },
    
    // 公共API
    COMMON: {
      UPLOAD: '/common/upload',
      BANNERS: '/common/banners',
      REGIONS: '/common/regions',
      SMS: '/common/sms/send',
      CAPTCHA: '/common/captcha'
    }
  },
  
  // 请求头配置
  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  
  // 状态码配置
  STATUS_CODES: {
    SUCCESS: 200,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    SERVER_ERROR: 500
  },
  
  // 错误消息配置
  ERROR_MESSAGES: {
    NETWORK_ERROR: '网络连接失败，请检查网络设置',
    TIMEOUT_ERROR: '请求超时，请重试',
    SERVER_ERROR: '服务器错误，请稍后重试',
    UNAUTHORIZED: '登录已过期，请重新登录',
    FORBIDDEN: '没有权限访问',
    NOT_FOUND: '请求的资源不存在'
  }
};

// 获取完整的API URL
function getApiUrl(endpoint, params = {}) {
  let url = endpoint;
  
  // 替换路径参数
  Object.keys(params).forEach(key => {
    url = url.replace(`{${key}}`, params[key]);
  });
  
  return url;
}

// 获取基础URL
function getBaseUrl(type = 'USER') {
  switch (type.toUpperCase()) {
    case 'USER':
      return API_CONFIG.ENDPOINTS.USER.BASE;
    case 'MERCHANT':
      return API_CONFIG.ENDPOINTS.MERCHANT.BASE;
    case 'ADMIN':
      return API_CONFIG.ENDPOINTS.ADMIN.BASE;
    default:
      return API_CONFIG.BASE_URL;
  }
}

// 导出配置
module.exports = {
  API_CONFIG,
  getApiUrl,
  getBaseUrl
};

// 如果是小程序环境，也支持ES6导出
if (typeof module === 'undefined') {
  window.API_CONFIG = API_CONFIG;
  window.getApiUrl = getApiUrl;
  window.getBaseUrl = getBaseUrl;
}
