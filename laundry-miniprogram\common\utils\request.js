// 统一请求工具类
const { API_CONFIG, getBaseUrl } = require('../config/api-config.js');

class RequestManager {
  constructor(appType = 'USER') {
    this.appType = appType;
    this.baseURL = getBaseUrl(appType);
    this.timeout = API_CONFIG.TIMEOUT;
    this.interceptors = {
      request: [],
      response: []
    };
    
    // 设置默认请求拦截器
    this.setupDefaultInterceptors();
  }
  
  // 设置默认拦截器
  setupDefaultInterceptors() {
    // 请求拦截器
    this.addRequestInterceptor((config) => {
      // 添加token
      const token = this.getToken();
      if (token) {
        config.header = config.header || {};
        config.header.Authorization = `Bearer ${token}`;
      }
      
      // 添加默认头部
      config.header = {
        ...API_CONFIG.HEADERS,
        ...config.header
      };
      
      // 添加时间戳防止缓存
      if (config.method === 'GET') {
        config.data = config.data || {};
        config.data._t = Date.now();
      }
      
      console.log(`[${this.appType}] 请求:`, config.url, config);
      return config;
    });
    
    // 响应拦截器
    this.addResponseInterceptor(
      (response) => {
        console.log(`[${this.appType}] 响应:`, response);
        
        // 统一处理响应数据
        if (response.statusCode === 200) {
          const data = response.data;
          
          // 后端统一返回格式: { success: boolean, data: any, message: string, code: number }
          if (data && typeof data === 'object') {
            if (data.success === false) {
              // 业务错误
              return Promise.reject({
                type: 'BUSINESS_ERROR',
                message: data.message || '操作失败',
                code: data.code,
                data: data.data
              });
            }
            
            // 成功响应，返回data字段
            return data.data || data;
          }
          
          return data;
        } else {
          // HTTP错误
          return Promise.reject({
            type: 'HTTP_ERROR',
            message: this.getErrorMessage(response.statusCode),
            statusCode: response.statusCode,
            response
          });
        }
      },
      (error) => {
        console.error(`[${this.appType}] 请求错误:`, error);
        
        // 网络错误处理
        if (error.errMsg) {
          if (error.errMsg.includes('timeout')) {
            error.type = 'TIMEOUT_ERROR';
            error.message = API_CONFIG.ERROR_MESSAGES.TIMEOUT_ERROR;
          } else if (error.errMsg.includes('fail')) {
            error.type = 'NETWORK_ERROR';
            error.message = API_CONFIG.ERROR_MESSAGES.NETWORK_ERROR;
          }
        }
        
        return Promise.reject(error);
      }
    );
  }
  
  // 获取token
  getToken() {
    try {
      return wx.getStorageSync(`${this.appType.toLowerCase()}_token`);
    } catch (error) {
      console.error('获取token失败:', error);
      return null;
    }
  }
  
  // 设置token
  setToken(token) {
    try {
      wx.setStorageSync(`${this.appType.toLowerCase()}_token`, token);
    } catch (error) {
      console.error('设置token失败:', error);
    }
  }
  
  // 清除token
  clearToken() {
    try {
      wx.removeStorageSync(`${this.appType.toLowerCase()}_token`);
    } catch (error) {
      console.error('清除token失败:', error);
    }
  }
  
  // 添加请求拦截器
  addRequestInterceptor(interceptor) {
    this.interceptors.request.push(interceptor);
  }
  
  // 添加响应拦截器
  addResponseInterceptor(fulfilled, rejected) {
    this.interceptors.response.push({ fulfilled, rejected });
  }
  
  // 执行请求拦截器
  executeRequestInterceptors(config) {
    let result = config;
    for (const interceptor of this.interceptors.request) {
      result = interceptor(result);
    }
    return result;
  }
  
  // 执行响应拦截器
  executeResponseInterceptors(response, isError = false) {
    let result = isError ? Promise.reject(response) : Promise.resolve(response);
    
    for (const interceptor of this.interceptors.response) {
      if (isError && interceptor.rejected) {
        result = result.catch(interceptor.rejected);
      } else if (!isError && interceptor.fulfilled) {
        result = result.then(interceptor.fulfilled);
      }
    }
    
    return result;
  }
  
  // 获取错误消息
  getErrorMessage(statusCode) {
    switch (statusCode) {
      case 401:
        return API_CONFIG.ERROR_MESSAGES.UNAUTHORIZED;
      case 403:
        return API_CONFIG.ERROR_MESSAGES.FORBIDDEN;
      case 404:
        return API_CONFIG.ERROR_MESSAGES.NOT_FOUND;
      case 500:
        return API_CONFIG.ERROR_MESSAGES.SERVER_ERROR;
      default:
        return API_CONFIG.ERROR_MESSAGES.NETWORK_ERROR;
    }
  }
  
  // 基础请求方法
  request(config) {
    return new Promise((resolve, reject) => {
      // 处理URL
      let url = config.url;
      if (!url.startsWith('http')) {
        url = this.baseURL + url;
      }
      
      // 执行请求拦截器
      const requestConfig = this.executeRequestInterceptors({
        url,
        method: config.method || 'GET',
        data: config.data,
        header: config.header,
        timeout: config.timeout || this.timeout,
        ...config
      });
      
      // 发起请求
      wx.request({
        ...requestConfig,
        success: (response) => {
          this.executeResponseInterceptors(response)
            .then(resolve)
            .catch(reject);
        },
        fail: (error) => {
          this.executeResponseInterceptors(error, true)
            .then(resolve)
            .catch(reject);
        }
      });
    });
  }
  
  // GET请求
  get(url, params, config = {}) {
    return this.request({
      url,
      method: 'GET',
      data: params,
      ...config
    });
  }
  
  // POST请求
  post(url, data, config = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...config
    });
  }
  
  // PUT请求
  put(url, data, config = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...config
    });
  }
  
  // DELETE请求
  delete(url, config = {}) {
    return this.request({
      url,
      method: 'DELETE',
      ...config
    });
  }
  
  // 文件上传
  upload(url, filePath, formData = {}, config = {}) {
    return new Promise((resolve, reject) => {
      // 添加token
      const token = this.getToken();
      const header = { ...config.header };
      if (token) {
        header.Authorization = `Bearer ${token}`;
      }
      
      // 处理URL
      if (!url.startsWith('http')) {
        url = this.baseURL + url;
      }
      
      wx.uploadFile({
        url,
        filePath,
        name: config.name || 'file',
        formData,
        header,
        success: (response) => {
          try {
            const data = JSON.parse(response.data);
            if (data.success) {
              resolve(data.data);
            } else {
              reject({
                type: 'BUSINESS_ERROR',
                message: data.message || '上传失败'
              });
            }
          } catch (error) {
            reject({
              type: 'PARSE_ERROR',
              message: '响应数据解析失败'
            });
          }
        },
        fail: (error) => {
          reject({
            type: 'UPLOAD_ERROR',
            message: '文件上传失败',
            error
          });
        }
      });
    });
  }
}

// 创建不同端的请求实例
const userRequest = new RequestManager('USER');
const merchantRequest = new RequestManager('MERCHANT');
const adminRequest = new RequestManager('ADMIN');

// 导出
module.exports = {
  RequestManager,
  userRequest,
  merchantRequest,
  adminRequest
};

// 如果是小程序环境，也支持全局访问
if (typeof module === 'undefined') {
  window.RequestManager = RequestManager;
  window.userRequest = userRequest;
  window.merchantRequest = merchantRequest;
  window.adminRequest = adminRequest;
}
