/* 管理端全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'Segoe UI', <PERSON>l, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 管理端主色调 */
.btn-primary {
  background-color: #1890ff;
  color: #fff;
}

.btn-primary:active {
  background-color: #096dd9;
}

.text-primary { 
  color: #1890ff; 
}

.bg-primary {
  background-color: #1890ff;
}

/* 数据概览样式 */
.overview-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
}

.overview-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #323233;
  margin-bottom: 30rpx;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
}

.overview-item {
  text-align: center;
  padding: 30rpx 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12rpx;
  color: #fff;
}

.overview-item:nth-child(2) {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.overview-item:nth-child(3) {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.overview-item:nth-child(4) {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.overview-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.overview-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 快捷操作样式 */
.quick-actions {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
  margin-top: 20rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.action-item:active {
  background-color: #e9ecef;
  transform: scale(0.95);
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 15rpx;
}

.action-text {
  font-size: 24rpx;
  color: #323233;
  text-align: center;
}

/* 待处理事项样式 */
.pending-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
}

.pending-list {
  margin-top: 20rpx;
}

.pending-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #ebedf0;
  border-radius: 12rpx;
  margin-bottom: 15rpx;
}

.pending-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pending-icon.complaint {
  background-color: #fff2e8;
}

.pending-icon.complaint::before {
  content: '!';
  color: #ff976a;
  font-weight: bold;
  font-size: 32rpx;
}

.pending-icon.audit {
  background-color: #e6f7ff;
}

.pending-icon.audit::before {
  content: '✓';
  color: #1890ff;
  font-weight: bold;
  font-size: 24rpx;
}

.pending-icon.financial {
  background-color: #f6ffed;
}

.pending-icon.financial::before {
  content: '¥';
  color: #52c41a;
  font-weight: bold;
  font-size: 28rpx;
}

.pending-content {
  flex: 1;
}

.pending-title {
  display: block;
  font-size: 28rpx;
  color: #323233;
  margin-bottom: 8rpx;
}

.pending-desc {
  font-size: 24rpx;
  color: #969799;
}

.pending-count {
  background-color: #ff4757;
  color: #fff;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  min-width: 40rpx;
  text-align: center;
}

/* 最近订单样式 */
.recent-orders {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
}

.order-list {
  margin-top: 20rpx;
}

.order-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border: 1rpx solid #ebedf0;
  border-radius: 8rpx;
  margin-bottom: 15rpx;
}

.order-info {
  flex: 1;
}

.order-number {
  display: block;
  font-size: 26rpx;
  color: #323233;
  margin-bottom: 8rpx;
}

.order-service {
  display: block;
  font-size: 28rpx;
  color: #323233;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.order-time {
  font-size: 22rpx;
  color: #969799;
}

.order-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  margin-right: 20rpx;
}

.order-status.pending {
  background-color: #fff7e6;
  color: #ff976a;
}

.order-status.processing {
  background-color: #e6f7ff;
  color: #1890ff;
}

.order-status.completed {
  background-color: #f6ffed;
  color: #52c41a;
}

.order-status.cancelled {
  background-color: #f5f5f5;
  color: #969799;
}

.order-amount {
  font-size: 32rpx;
  color: #1890ff;
  font-weight: bold;
}

/* 通用样式 */
.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #323233;
  margin-bottom: 20rpx;
}

.more-btn {
  font-size: 24rpx;
  color: #1890ff;
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.status-indicator.online {
  background-color: #52c41a;
}

.status-indicator.offline {
  background-color: #d9d9d9;
}

.status-indicator.busy {
  background-color: #ff976a;
}

/* 数据卡片动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.overview-item {
  animation: fadeInUp 0.6s ease-out;
}

.overview-item:nth-child(2) {
  animation-delay: 0.1s;
}

.overview-item:nth-child(3) {
  animation-delay: 0.2s;
}

.overview-item:nth-child(4) {
  animation-delay: 0.3s;
}
