const app = getApp();
const { announcementAPI } = require('../../utils/api.js');

Page({
  data: {
    announcements: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    showAddModal: false,
    showEditModal: false,
    currentAnnouncement: null,
    formData: {
      title: '',
      content: '',
      type: 'SYSTEM',
      priority: 'NORMAL',
      targetType: 'ALL',
      publishTime: '',
      expireTime: ''
    },
    typeOptions: [
      { value: 'SYSTEM', label: '系统公告' },
      { value: 'ACTIVITY', label: '活动公告' },
      { value: 'MAINTENANCE', label: '维护公告' },
      { value: 'PROMOTION', label: '推广公告' }
    ],
    priorityOptions: [
      { value: 'LOW', label: '低' },
      { value: 'NORMAL', label: '普通' },
      { value: 'HIGH', label: '高' },
      { value: 'URGENT', label: '紧急' }
    ],
    targetOptions: [
      { value: 'ALL', label: '全部用户' },
      { value: 'USER', label: '普通用户' },
      { value: 'MERCHANT', label: '商家用户' },
      { value: 'VIP', label: 'VIP用户' }
    ]
  },

  onLoad() {
    this.loadAnnouncements();
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
  },

  // 重置并加载
  resetAndLoad() {
    this.setData({
      announcements: [],
      page: 1,
      hasMore: true
    });
    this.loadAnnouncements();
  },

  // 加载公告列表
  async loadAnnouncements() {
    if (this.data.loading || !this.data.hasMore) return;

    try {
      this.setData({ loading: true });

      const params = {
        page: this.data.page,
        pageSize: this.data.pageSize
      };

      const result = await announcementAPI.getAnnouncements(params);
      const newAnnouncements = result.list || result || [];
      const announcements = this.data.page === 1 ? newAnnouncements : [...this.data.announcements, ...newAnnouncements];

      this.setData({
        announcements,
        hasMore: newAnnouncements.length === this.data.pageSize,
        page: this.data.page + 1,
        loading: false
      });

    } catch (error) {
      console.error('加载公告列表失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    }
  },

  // 显示添加弹窗
  onShowAddModal() {
    this.setData({
      showAddModal: true,
      formData: {
        title: '',
        content: '',
        type: 'SYSTEM',
        priority: 'NORMAL',
        targetType: 'ALL',
        publishTime: '',
        expireTime: ''
      }
    });
  },

  // 关闭添加弹窗
  onCloseAddModal() {
    this.setData({
      showAddModal: false
    });
  },

  // 显示编辑弹窗
  onShowEditModal(e) {
    const announcement = e.currentTarget.dataset.item;
    this.setData({
      showEditModal: true,
      currentAnnouncement: announcement,
      formData: {
        title: announcement.title,
        content: announcement.content,
        type: announcement.type,
        priority: announcement.priority,
        targetType: announcement.targetType,
        publishTime: announcement.publishTime,
        expireTime: announcement.expireTime
      }
    });
  },

  // 关闭编辑弹窗
  onCloseEditModal() {
    this.setData({
      showEditModal: false,
      currentAnnouncement: null
    });
  },

  // 表单输入
  onFormInput(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 选择器变更
  onPickerChange(e) {
    const field = e.currentTarget.dataset.field;
    const options = e.currentTarget.dataset.options;
    const index = e.detail.value;
    const value = this.data[options][index].value;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 日期选择
  onDateChange(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 提交添加
  async onSubmitAdd() {
    if (!this.validateForm()) return;

    try {
      wx.showLoading({
        title: '发布中...'
      });

      await announcementAPI.createAnnouncement(this.data.formData);

      wx.hideLoading();
      wx.showToast({
        title: '发布成功',
        icon: 'success'
      });

      this.setData({
        showAddModal: false
      });

      // 刷新列表
      this.resetAndLoad();

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '发布失败',
        icon: 'none'
      });
    }
  },

  // 提交编辑
  async onSubmitEdit() {
    if (!this.validateForm()) return;

    try {
      wx.showLoading({
        title: '更新中...'
      });

      await announcementAPI.updateAnnouncement(this.data.currentAnnouncement.id, this.data.formData);

      wx.hideLoading();
      wx.showToast({
        title: '更新成功',
        icon: 'success'
      });

      this.setData({
        showEditModal: false,
        currentAnnouncement: null
      });

      // 刷新列表
      this.resetAndLoad();

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '更新失败',
        icon: 'none'
      });
    }
  },

  // 表单验证
  validateForm() {
    const { title, content } = this.data.formData;
    
    if (!title.trim()) {
      wx.showToast({
        title: '请输入公告标题',
        icon: 'none'
      });
      return false;
    }
    
    if (!content.trim()) {
      wx.showToast({
        title: '请输入公告内容',
        icon: 'none'
      });
      return false;
    }
    
    return true;
  },

  // 删除公告
  onDeleteAnnouncement(e) {
    const announcement = e.currentTarget.dataset.item;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条公告吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({
              title: '删除中...'
            });

            await announcementAPI.deleteAnnouncement(announcement.id);

            wx.hideLoading();
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });

            // 从列表中移除
            const announcements = this.data.announcements.filter(item => item.id !== announcement.id);
            this.setData({ announcements });

          } catch (error) {
            wx.hideLoading();
            wx.showToast({
              title: error.message || '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 切换发布状态
  async onToggleStatus(e) {
    const announcement = e.currentTarget.dataset.item;
    const newStatus = announcement.status === 'PUBLISHED' ? 'DRAFT' : 'PUBLISHED';
    
    try {
      wx.showLoading({
        title: '更新中...'
      });

      await announcementAPI.updateAnnouncementStatus(announcement.id, newStatus);

      wx.hideLoading();
      wx.showToast({
        title: '状态更新成功',
        icon: 'success'
      });

      // 更新本地数据
      const announcements = this.data.announcements.map(item => 
        item.id === announcement.id ? { ...item, status: newStatus } : item
      );
      this.setData({ announcements });

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '更新失败',
        icon: 'none'
      });
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.resetAndLoad();
    wx.stopPullDownRefresh();
  },

  // 触底加载
  onReachBottom() {
    this.loadAnnouncements();
  }
});
