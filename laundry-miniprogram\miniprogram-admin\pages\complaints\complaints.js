const app = getApp();
const { complaintAPI } = require('../../utils/api.js');

Page({
  data: {
    currentTab: 0,
    tabs: [
      { key: 'all', name: '全部投诉' },
      { key: 'pending', name: '待处理' },
      { key: 'processing', name: '处理中' },
      { key: 'resolved', name: '已解决' },
      { key: 'closed', name: '已关闭' }
    ],

    complaints: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 20,

    // 处理投诉相关
    showProcessModal: false,
    currentComplaint: null,
    processType: '',
    processNote: '',
    processOptions: [
      { value: 'mediate', label: '协调处理' },
      { value: 'refund', label: '退款处理' },
      { value: 'compensate', label: '赔偿处理' },
      { value: 'reject', label: '驳回投诉' }
    ],

    // 统计数据
    stats: {
      total: 0,
      pending: 0,
      processing: 0,
      resolved: 0,
      closed: 0
    }
  },

  onLoad() {
    this.loadStats();
    this.loadComplaints();
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
  },

  // Tab切换
  onTabChange(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentTab: index
    });
    this.resetAndLoad();
  },

  // 重置并加载
  resetAndLoad() {
    this.setData({
      complaints: [],
      page: 1,
      hasMore: true
    });
    this.loadComplaints();
  },

  // 加载统计数据
  async loadStats() {
    try {
      const stats = await complaintAPI.getComplaintStats();
      this.setData({ stats });
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  },

  // 加载投诉列表
  async loadComplaints() {
    if (this.data.loading || !this.data.hasMore) return;

    try {
      this.setData({ loading: true });

      const currentTab = this.data.tabs[this.data.currentTab];
      const params = {
        page: this.data.page,
        pageSize: this.data.pageSize,
        status: currentTab.key === 'all' ? undefined : currentTab.key
      };

      const result = await complaintAPI.getComplaints(params);
      const newComplaints = result.list || result || [];
      const complaints = this.data.page === 1 ? newComplaints : [...this.data.complaints, ...newComplaints];

      this.setData({
        complaints,
        hasMore: newComplaints.length === this.data.pageSize,
        page: this.data.page + 1,
        loading: false
      });

    } catch (error) {
      console.error('加载投诉列表失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    }
  },

  // 投诉详情
  onComplaintDetail(e) {
    const complaint = e.currentTarget.dataset.item;
    wx.navigateTo({
      url: `/pages/complaint-detail/complaint-detail?id=${complaint.id}`
    });
  },

  // 处理投诉
  onProcessComplaint(e) {
    const complaint = e.currentTarget.dataset.item;
    this.setData({
      showProcessModal: true,
      currentComplaint: complaint,
      processType: '',
      processNote: ''
    });
  },

  // 关闭处理弹窗
  onCloseProcessModal() {
    this.setData({
      showProcessModal: false,
      currentComplaint: null,
      processType: '',
      processNote: ''
    });
  },

  // 处理类型选择
  onProcessTypeChange(e) {
    const index = e.detail.value;
    const processType = this.data.processOptions[index].value;
    this.setData({
      processType
    });
  },

  // 处理备注输入
  onProcessNoteInput(e) {
    this.setData({
      processNote: e.detail.value
    });
  },

  // 提交处理
  async onSubmitProcess() {
    const { currentComplaint, processType, processNote } = this.data;

    if (!processType) {
      wx.showToast({
        title: '请选择处理方式',
        icon: 'none'
      });
      return;
    }

    if (!processNote.trim()) {
      wx.showToast({
        title: '请输入处理说明',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({
        title: '处理中...'
      });

      await complaintAPI.processComplaint(currentComplaint.id, {
        type: processType,
        note: processNote.trim()
      });

      wx.hideLoading();
      wx.showToast({
        title: '处理成功',
        icon: 'success'
      });

      this.setData({
        showProcessModal: false
      });

      // 刷新数据
      this.loadStats();
      this.resetAndLoad();

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '处理失败',
        icon: 'none'
      });
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadStats();
    this.resetAndLoad();
    wx.stopPullDownRefresh();
  },

  // 触底加载
  onReachBottom() {
    this.loadComplaints();
  }
});