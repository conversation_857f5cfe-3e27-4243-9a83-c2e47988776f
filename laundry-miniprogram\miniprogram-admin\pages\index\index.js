const app = getApp();
const { dashboardAPI, userAPI, merchantAPI, orderAPI } = require('../../utils/api.js');

Page({
  data: {
    adminInfo: {},
    dashboardStats: {
      totalUsers: 0,
      totalMerchants: 0,
      totalOrders: 0,
      todayOrders: 0,
      totalRevenue: 0,
      todayRevenue: 0,
      pendingMerchants: 0,
      activeUsers: 0
    },
    quickActions: [
      {
        icon: 'user',
        title: '用户管理',
        desc: '查看和管理用户',
        url: '/pages/users/users',
        color: '#1890ff'
      },
      {
        icon: 'shop',
        title: '商家管理',
        desc: '审核和管理商家',
        url: '/pages/merchants/merchants',
        color: '#52c41a'
      },
      {
        icon: 'order',
        title: '订单管理',
        desc: '查看和处理订单',
        url: '/pages/orders/orders',
        color: '#fa8c16'
      },
      {
        icon: 'chart',
        title: '数据统计',
        desc: '查看平台数据',
        url: '/pages/statistics/statistics',
        color: '#722ed1'
      },
      {
        icon: 'complaint',
        title: '投诉处理',
        desc: '处理用户投诉',
        url: '/pages/complaints/complaints',
        color: '#f5222d'
      },
      {
        icon: 'setting',
        title: '系统设置',
        desc: '配置系统参数',
        url: '/pages/settings/settings',
        color: '#13c2c2'
      }
    ],
    recentActivities: [],
    loading: true
  },

  onLoad(options) {
    this.loadDashboardData();
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    // 刷新数据
    this.loadDashboardData();
  },

  // 加载仪表盘数据
  loadDashboardData() {
    this.setData({
      loading: true
    });

    Promise.all([
      this.loadDashboardOverview(),
      this.loadAdminInfo()
    ]).finally(() => {
      this.setData({
        loading: false
      });
    });
  },

  // 加载仪表盘概览
  loadDashboardOverview() {
    return dashboardAPI.getDashboardOverview().then(res => {
      this.setData({
        dashboardStats: {
          ...this.data.dashboardStats,
          ...res
        }
      });
    }).catch(err => {
      console.error('加载仪表盘概览失败:', err);
    });
  },

  // 加载管理员信息
  loadAdminInfo() {
    return app.getAdminInfo().then(adminInfo => {
      this.setData({
        adminInfo
      });
    }).catch(err => {
      console.error('加载管理员信息失败:', err);
    });
  },

  // 快捷操作点击
  onQuickActionTap(e) {
    const url = e.currentTarget.dataset.url;
    wx.navigateTo({
      url
    });
  },

  // 统计卡片点击
  onStatCardTap(e) {
    const type = e.currentTarget.dataset.type;
    let url = '';

    switch (type) {
      case 'users':
        url = '/pages/users/users';
        break;
      case 'merchants':
        url = '/pages/merchants/merchants';
        break;
      case 'orders':
        url = '/pages/orders/orders';
        break;
      case 'revenue':
        url = '/pages/financial/financial';
        break;
      case 'pending':
        url = '/pages/merchants/merchants?status=pending';
        break;
    }

    if (url) {
      wx.navigateTo({
        url
      });
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadDashboardData();
    wx.stopPullDownRefresh();
  }
});