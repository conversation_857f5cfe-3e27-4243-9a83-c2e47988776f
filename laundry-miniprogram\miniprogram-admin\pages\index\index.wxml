<view class="container">
  <!-- 数据概览 -->
  <view class="overview-section">
    <view class="overview-title">今日数据概览</view>
    <view class="overview-grid">
      <view class="overview-item">
        <text class="overview-number">{{todayData.orders}}</text>
        <text class="overview-label">今日订单</text>
      </view>
      <view class="overview-item">
        <text class="overview-number">¥{{todayData.revenue}}</text>
        <text class="overview-label">今日收入</text>
      </view>
      <view class="overview-item">
        <text class="overview-number">{{todayData.newUsers}}</text>
        <text class="overview-label">新增用户</text>
      </view>
      <view class="overview-item">
        <text class="overview-number">{{todayData.newMerchants}}</text>
        <text class="overview-label">新增商家</text>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <view class="section-title">快捷操作</view>
    <view class="actions-grid">
      <view class="action-item" bindtap="onOrderManagement">
        <image src="/images/order-manage.png" class="action-icon"></image>
        <text class="action-text">订单管理</text>
      </view>
      <view class="action-item" bindtap="onUserManagement">
        <image src="/images/user-manage.png" class="action-icon"></image>
        <text class="action-text">用户管理</text>
      </view>
      <view class="action-item" bindtap="onMerchantManagement">
        <image src="/images/merchant-manage.png" class="action-icon"></image>
        <text class="action-text">商家管理</text>
      </view>
      <view class="action-item" bindtap="onComplaintManagement">
        <image src="/images/complaint-manage.png" class="action-icon"></image>
        <text class="action-text">投诉处理</text>
      </view>
      <view class="action-item" bindtap="onFinancialManagement">
        <image src="/images/financial-manage.png" class="action-icon"></image>
        <text class="action-text">财务管理</text>
      </view>
      <view class="action-item" bindtap="onSystemSettings">
        <image src="/images/settings.png" class="action-icon"></image>
        <text class="action-text">系统设置</text>
      </view>
    </view>
  </view>

  <!-- 待处理事项 -->
  <view class="pending-section">
    <view class="section-title">待处理事项</view>
    <view class="pending-list">
      <view class="pending-item" wx:for="{{pendingItems}}" wx:key="id" bindtap="onPendingItemTap" data-item="{{item}}">
        <view class="pending-icon {{item.type}}"></view>
        <view class="pending-content">
          <text class="pending-title">{{item.title}}</text>
          <text class="pending-desc">{{item.description}}</text>
        </view>
        <view class="pending-count">{{item.count}}</view>
      </view>
    </view>
  </view>

  <!-- 最近订单 -->
  <view class="recent-orders">
    <view class="section-title">
      <text>最近订单</text>
      <text class="more-btn" bindtap="onMoreOrders">查看更多</text>
    </view>
    <view class="order-list">
      <view class="order-item" wx:for="{{recentOrders}}" wx:key="id" bindtap="onOrderTap" data-id="{{item.id}}">
        <view class="order-info">
          <text class="order-number">订单号：{{item.orderNumber}}</text>
          <text class="order-service">{{item.serviceName}}</text>
          <text class="order-time">{{item.createTime}}</text>
        </view>
        <view class="order-status {{item.status}}">{{item.statusText}}</view>
        <text class="order-amount">¥{{item.amount}}</text>
      </view>
    </view>
  </view>
</view> 
