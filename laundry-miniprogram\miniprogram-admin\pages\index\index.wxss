/* 管理端数据概览页面样式 */
.container {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
}

/* 数据概览 */
.overview-section {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  color: #ffffff;
}

.overview-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  text-align: center;
}

.overview-grid {
  display: flex;
  justify-content: space-between;
}

.overview-item {
  text-align: center;
  flex: 1;
  padding: 20rpx 10rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  margin: 0 8rpx;
  backdrop-filter: blur(10rpx);
}

.overview-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: #ffffff;
}

.overview-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 快捷操作 */
.quick-actions {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.more-btn {
  font-size: 26rpx;
  color: #1890ff;
  font-weight: normal;
}

.actions-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.action-item {
  width: calc(33.333% - 14rpx);
  text-align: center;
  padding: 30rpx 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  position: relative;
}

.action-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 16rpx;
}

.action-text {
  display: block;
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 待处理事项 */
.pending-section {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.pending-list {
  margin-top: 20rpx;
}

.pending-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
  transition: all 0.3s ease;
}

.pending-item:last-child {
  border-bottom: none;
}

.pending-item:active {
  background: #f8f9fa;
  margin: 0 -20rpx;
  padding-left: 20rpx;
  padding-right: 20rpx;
  border-radius: 12rpx;
}

.pending-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.pending-icon::before {
  content: '';
  width: 32rpx;
  height: 32rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.pending-icon.merchant::before {
  background-image: url('/images/merchant-pending.png');
}

.pending-icon.merchant {
  background: rgba(250, 140, 22, 0.1);
}

.pending-icon.complaint::before {
  background-image: url('/images/complaint-pending.png');
}

.pending-icon.complaint {
  background: rgba(255, 77, 79, 0.1);
}

.pending-icon.refund::before {
  background-image: url('/images/refund-pending.png');
}

.pending-icon.refund {
  background: rgba(24, 144, 255, 0.1);
}

.pending-content {
  flex: 1;
  margin-right: 20rpx;
}

.pending-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.pending-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.pending-count {
  background: #ff4d4f;
  color: #ffffff;
  font-size: 24rpx;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  min-width: 60rpx;
  text-align: center;
}

/* 最近订单 */
.recent-orders {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.order-list {
  margin-top: 20rpx;
}

.order-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
  transition: all 0.3s ease;
}

.order-item:last-child {
  border-bottom: none;
}

.order-item:active {
  background: #f8f9fa;
  margin: 0 -20rpx;
  padding-left: 20rpx;
  padding-right: 20rpx;
  border-radius: 12rpx;
}

.order-info {
  flex: 1;
  margin-right: 20rpx;
}

.order-number {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.order-service {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.order-time {
  display: block;
  font-size: 24rpx;
  color: #999;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  margin-right: 20rpx;
}

.order-status.pending {
  background: rgba(250, 140, 22, 0.1);
  color: #fa8c16;
}

.order-status.processing {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.order-status.completed {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.order-status.cancelled {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.order-amount {
  font-size: 28rpx;
  color: #1890ff;
  font-weight: bold;
  min-width: 120rpx;
  text-align: right;
}

/* 动画效果 */
.overview-section {
  animation: slideDown 0.5s ease-out;
}

.quick-actions,
.pending-section,
.recent-orders {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 15rpx;
  }
  
  .overview-section,
  .quick-actions,
  .pending-section,
  .recent-orders {
    padding: 30rpx;
    margin-bottom: 15rpx;
  }
  
  .action-item {
    width: calc(50% - 10rpx);
    padding: 25rpx 15rpx;
  }
  
  .overview-item {
    padding: 15rpx 8rpx;
    margin: 0 6rpx;
  }
  
  .overview-number {
    font-size: 32rpx;
  }
  
  .overview-title {
    font-size: 28rpx;
  }
}
