// 管理端登录页面
const app = getApp();

Page({
  data: {
    username: '',
    password: '',
    captcha: '',
    showPassword: false,
    showCaptcha: false,
    captchaUrl: '',
    rememberPassword: false,
    loginLoading: false,
    canLogin: false,
    showAuthModal: false,
    authMessage: '',
    loginAttempts: 0
  },

  onLoad(options) {
    // 检查是否已登录
    if (app.globalData.isLoggedIn && app.globalData.userType === 'ADMIN') {
      wx.switchTab({
        url: '/pages/index/index'
      });
      return;
    }

    // 加载记住的密码
    this.loadRememberedPassword();

    // 初始化验证码
    this.initCaptcha();
  },

  // 加载记住的密码
  loadRememberedPassword() {
    try {
      const savedUsername = wx.getStorageSync('admin_username');
      const savedPassword = wx.getStorageSync('admin_password');
      const rememberPassword = wx.getStorageSync('admin_remember_password');

      if (rememberPassword && savedUsername && savedPassword) {
        this.setData({
          username: savedUsername,
          password: savedPassword,
          rememberPassword: true
        });
        this.checkCanLogin();
      }
    } catch (error) {
      console.error('加载记住密码失败:', error);
    }
  },

  // 初始化验证码
  initCaptcha() {
    // 检查登录失败次数
    const attempts = wx.getStorageSync('admin_login_attempts') || 0;
    if (attempts >= 3) {
      this.setData({
        showCaptcha: true
      });
      this.refreshCaptcha();
    }
  },

  // 刷新验证码
  refreshCaptcha() {
    const timestamp = Date.now();
    this.setData({
      captchaUrl: `/api/captcha?t=${timestamp}`
    });
  },

  // 用户名输入
  onUsernameInput(e) {
    this.setData({
      username: e.detail.value
    });
    this.checkCanLogin();
  },

  // 密码输入
  onPasswordInput(e) {
    this.setData({
      password: e.detail.value
    });
    this.checkCanLogin();
  },

  // 验证码输入
  onCaptchaInput(e) {
    this.setData({
      captcha: e.detail.value
    });
    this.checkCanLogin();
  },

  // 切换密码显示
  togglePassword() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  // 切换记住密码
  toggleRemember() {
    this.setData({
      rememberPassword: !this.data.rememberPassword
    });
  },

  // 检查是否可以登录
  checkCanLogin() {
    const { username, password, captcha, showCaptcha } = this.data;
    let canLogin = username.trim().length >= 3 && password.length >= 6;

    if (showCaptcha) {
      canLogin = canLogin && captcha.length === 4;
    }

    this.setData({
      canLogin
    });
  },

  // 登录
  onLogin() {
    if (!this.data.canLogin || this.data.loginLoading) {
      return;
    }

    const { username, password, captcha, rememberPassword, showCaptcha } = this.data;

    // 管理员登录需要额外确认
    this.showAuthConfirm('您正在以管理员身份登录系统，请确认您有相应权限？', () => {
      this.performLogin();
    });
  },

  // 执行登录
  performLogin() {
    const { username, password, captcha, rememberPassword, showCaptcha } = this.data;

    this.setData({
      loginLoading: true
    });

    // 调用登录API
    app.request({
      url: '/auth/admin/login',
      method: 'POST',
      data: {
        username: username.trim(),
        password: password,
        captcha: showCaptcha ? captcha : undefined,
        userType: 'ADMIN'
      }
    }).then(res => {
      if (res.success) {
        // 登录成功
        app.login(res.data.userInfo, res.data.token, 'ADMIN');

        // 清除登录失败次数
        wx.removeStorageSync('admin_login_attempts');

        // 保存记住的密码
        if (rememberPassword) {
          this.savePassword();
        } else {
          this.clearSavedPassword();
        }

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });

        // 记录登录日志
        this.logLoginActivity(res.data.userInfo);

        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }, 1500);

      } else {
        // 登录失败
        this.handleLoginFailure(res.message);
      }
    }).catch(err => {
      this.handleLoginFailure(err.message || '网络错误，请重试');
    }).finally(() => {
      this.setData({
        loginLoading: false
      });
    });
  },

  // 处理登录失败
  handleLoginFailure(message) {
    wx.showToast({
      title: message || '登录失败',
      icon: 'none'
    });

    // 增加失败次数
    let attempts = wx.getStorageSync('admin_login_attempts') || 0;
    attempts++;
    wx.setStorageSync('admin_login_attempts', attempts);

    // 超过3次显示验证码
    if (attempts >= 3 && !this.data.showCaptcha) {
      this.setData({
        showCaptcha: true
      });
      this.refreshCaptcha();
      wx.showToast({
        title: '登录失败次数过多，请输入验证码',
        icon: 'none',
        duration: 3000
      });
    }

    // 清空验证码
    if (this.data.showCaptcha) {
      this.setData({
        captcha: ''
      });
      this.refreshCaptcha();
    }
  },

  // 记录登录活动
  logLoginActivity(userInfo) {
    app.request({
      url: '/admin/log/login',
      method: 'POST',
      data: {
        adminId: userInfo.id,
        loginTime: new Date().toISOString(),
        loginIP: 'miniprogram',
        userAgent: 'WeChat MiniProgram'
      }
    }).catch(err => {
      console.error('记录登录日志失败:', err);
    });
  },

  // 显示权限确认弹窗
  showAuthConfirm(message, callback) {
    this.setData({
      showAuthModal: true,
      authMessage: message
    });
    this.authCallback = callback;
  },

  // 隐藏权限确认弹窗
  hideAuthModal() {
    this.setData({
      showAuthModal: false
    });
    this.authCallback = null;
  },

  // 确认权限
  confirmAuth() {
    this.hideAuthModal();
    if (this.authCallback) {
      this.authCallback();
    }
  },

  // 保存密码
  savePassword() {
    try {
      wx.setStorageSync('admin_username', this.data.username);
      wx.setStorageSync('admin_password', this.data.password);
      wx.setStorageSync('admin_remember_password', true);
    } catch (error) {
      console.error('保存密码失败:', error);
    }
  },

  // 清除保存的密码
  clearSavedPassword() {
    try {
      wx.removeStorageSync('admin_username');
      wx.removeStorageSync('admin_password');
      wx.removeStorageSync('admin_remember_password');
    } catch (error) {
      console.error('清除密码失败:', error);
    }
  },

  // 忘记密码
  onForgotPassword() {
    wx.showModal({
      title: '忘记密码',
      content: '请联系系统管理员重置密码\n\n技术支持：400-123-4567\n邮箱：<EMAIL>',
      showCancel: false,
      confirmText: '我知道了'
    });
  }
});