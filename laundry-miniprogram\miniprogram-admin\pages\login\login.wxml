<!--管理端登录页面-->
<view class="container">
  <!-- 顶部logo区域 -->
  <view class="header">
    <image class="logo" src="/images/admin-logo.png" mode="aspectFit"></image>
    <text class="title">管理后台</text>
    <text class="subtitle">洗护服务平台管理系统</text>
  </view>

  <!-- 登录表单 -->
  <view class="login-form">
    <view class="form-title">
      <text class="title-text">管理员登录</text>
      <text class="subtitle-text">请使用管理员账号登录</text>
    </view>

    <!-- 账号密码登录 -->
    <view class="form-content">
      <view class="input-group">
        <view class="input-label">
          <image class="label-icon" src="/images/admin-icon.png"></image>
          <text class="label-text">管理员账号</text>
        </view>
        <input
          class="input-field"
          placeholder="请输入管理员账号"
          value="{{username}}"
          bindinput="onUsernameInput"
          type="text"
          maxlength="20"
        />
      </view>

      <view class="input-group">
        <view class="input-label">
          <image class="label-icon" src="/images/lock-icon.png"></image>
          <text class="label-text">登录密码</text>
        </view>
        <view class="password-input">
          <input
            class="input-field"
            placeholder="请输入登录密码"
            value="{{password}}"
            bindinput="onPasswordInput"
            password="{{!showPassword}}"
            maxlength="20"
          />
          <view class="password-toggle" bindtap="togglePassword">
            <image
              class="toggle-icon"
              src="{{showPassword ? '/images/eye-open.png' : '/images/eye-close.png'}}"
            ></image>
          </view>
        </view>
      </view>

      <!-- 验证码 -->
      <view class="input-group" wx:if="{{showCaptcha}}">
        <view class="input-label">
          <image class="label-icon" src="/images/captcha-icon.png"></image>
          <text class="label-text">验证码</text>
        </view>
        <view class="captcha-input">
          <input
            class="input-field captcha-field"
            placeholder="请输入验证码"
            value="{{captcha}}"
            bindinput="onCaptchaInput"
            type="text"
            maxlength="4"
          />
          <view class="captcha-image" bindtap="refreshCaptcha">
            <image class="captcha-img" src="{{captchaUrl}}" mode="aspectFit"></image>
            <text class="refresh-text">点击刷新</text>
          </view>
        </view>
      </view>

      <!-- 记住密码 -->
      <view class="form-options">
        <view class="remember-password" bindtap="toggleRemember">
          <checkbox-group>
            <checkbox value="remember" checked="{{rememberPassword}}" color="#1890ff"/>
          </checkbox-group>
          <text class="option-text">记住密码</text>
        </view>
        <text class="forgot-password" bindtap="onForgotPassword">忘记密码？</text>
      </view>

      <!-- 登录按钮 -->
      <button
        class="login-btn {{canLogin ? 'active' : 'disabled'}}"
        bindtap="onLogin"
        loading="{{loginLoading}}"
        disabled="{{!canLogin || loginLoading}}"
      >
        {{loginLoading ? '验证中...' : '登录'}}
      </button>

      <!-- 安全提示 -->
      <view class="security-tips">
        <view class="tip-item">
          <image class="tip-icon" src="/images/security-icon.png"></image>
          <text class="tip-text">请确保在安全环境下登录</text>
        </view>
        <view class="tip-item">
          <image class="tip-icon" src="/images/time-icon.png"></image>
          <text class="tip-text">登录有效期24小时</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="footer">
    <view class="system-info">
      <text class="system-name">洗护服务管理平台</text>
      <text class="system-version">版本 v1.0.0</text>
    </view>

    <view class="contact-info">
      <text class="contact-text">技术支持：400-123-4567</text>
      <text class="contact-email">邮箱：<EMAIL></text>
    </view>
  </view>

  <!-- 权限验证弹窗 -->
  <view class="auth-modal" wx:if="{{showAuthModal}}">
    <view class="auth-content">
      <image class="auth-icon" src="/images/auth-warning.png"></image>
      <text class="auth-title">权限验证</text>
      <text class="auth-desc">{{authMessage}}</text>
      <view class="auth-actions">
        <button class="auth-btn cancel" bindtap="hideAuthModal">取消</button>
        <button class="auth-btn confirm" bindtap="confirmAuth">确认</button>
      </view>
    </view>
  </view>
</view>