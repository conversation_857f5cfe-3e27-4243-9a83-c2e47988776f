/* 管理端登录页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  padding: 0 40rpx;
  display: flex;
  flex-direction: column;
}

/* 顶部logo区域 */
.header {
  text-align: center;
  padding: 80rpx 0 60rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 登录表单 */
.login-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.form-title {
  text-align: center;
  margin-bottom: 60rpx;
}

.title-text {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle-text {
  display: block;
  font-size: 26rpx;
  color: #666;
}

/* 表单内容 */
.form-content {
  width: 100%;
}

.input-group {
  margin-bottom: 40rpx;
}

.input-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.label-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.input-field {
  width: 100%;
  height: 88rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 30rpx;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.input-field:focus {
  border-color: #1890ff;
  background: #ffffff;
  box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.1);
}

.input-field::placeholder {
  color: #999;
}

/* 密码输入框 */
.password-input {
  position: relative;
  display: flex;
  align-items: center;
}

.password-toggle {
  position: absolute;
  right: 30rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

/* 验证码输入框 */
.captcha-input {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.captcha-field {
  flex: 1;
}

.captcha-image {
  width: 200rpx;
  height: 88rpx;
  background: #f0f0f0;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #e9ecef;
  position: relative;
}

.captcha-img {
  width: 100%;
  height: 60rpx;
}

.refresh-text {
  font-size: 20rpx;
  color: #999;
  margin-top: 4rpx;
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 50rpx;
}

.remember-password {
  display: flex;
  align-items: center;
}

.option-text {
  font-size: 26rpx;
  color: #666;
  margin-left: 10rpx;
}

.forgot-password {
  font-size: 26rpx;
  color: #1890ff;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
  transition: all 0.3s ease;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-btn.active {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: #ffffff;
  box-shadow: 0 8rpx 20rpx rgba(24, 144, 255, 0.3);
}

.login-btn.disabled {
  background: #cccccc;
  color: #999999;
}

.login-btn.active:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(24, 144, 255, 0.3);
}

/* 安全提示 */
.security-tips {
  margin-top: 40rpx;
  padding: 30rpx;
  background: #f0f8ff;
  border-radius: 12rpx;
  border-left: 6rpx solid #1890ff;
}

.tip-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 12rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #666;
}

/* 底部信息 */
.footer {
  margin-top: auto;
  padding-bottom: 60rpx;
}

.system-info {
  text-align: center;
  margin-bottom: 30rpx;
}

.system-name {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  margin-bottom: 8rpx;
}

.system-version {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}

.contact-info {
  text-align: center;
}

.contact-text,
.contact-email {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 6rpx;
}

/* 权限验证弹窗 */
.auth-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.auth-content {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin: 0 40rpx;
  text-align: center;
  max-width: 600rpx;
}

.auth-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.auth-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.auth-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.auth-actions {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.auth-btn {
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
  min-width: 120rpx;
}

.auth-btn.cancel {
  background: #f5f5f5;
  color: #666;
}

.auth-btn.confirm {
  background: #1890ff;
  color: #ffffff;
}

/* 动画效果 */
.login-form {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.header {
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-height: 600px) {
  .header {
    padding: 60rpx 0 40rpx;
  }
  
  .logo {
    width: 100rpx;
    height: 100rpx;
  }
  
  .title {
    font-size: 40rpx;
  }
  
  .login-form {
    padding: 50rpx 30rpx;
  }
}
