// 管理端商家管理页面
const app = getApp();
const { merchantAPI } = require('../../utils/api.js');

Page({
  data: {
    searchKeyword: '',
    showFilter: false,
    statusFilter: 'all',
    typeFilter: 'all',
    merchants: [],
    stats: {
      total: 0,
      active: 0,
      pending: 0,
      newToday: 0
    },
    statusOptions: [
      { label: '全部', value: 'all' },
      { label: '待审核', value: 'PENDING' },
      { label: '营业中', value: 'ACTIVE' },
      { label: '已禁用', value: 'DISABLED' },
      { label: '已拒绝', value: 'REJECTED' }
    ],
    typeOptions: [
      { label: '全部', value: 'all' },
      { label: '个人', value: 'INDIVIDUAL' },
      { label: '企业', value: 'ENTERPRISE' },
      { label: '连锁', value: 'CHAIN' }
    ],
    refreshing: false,
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    showAuditModal: false,
    currentMerchantId: null,
    auditResult: '',
    auditRemark: '',
    showCredentialModal: false,
    credentialUrl: '',
    credentialType: ''
  },

  onLoad() {
    this.loadMerchants();
    this.loadStats();
  },

  onPullDownRefresh() {
    this.onRefresh();
  },

  // 下拉刷新
  onRefresh() {
    this.setData({
      refreshing: true,
      page: 1,
      hasMore: true
    });

    this.loadMerchants().finally(() => {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onLoadMore() {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }

    this.setData({
      page: this.data.page + 1
    });

    this.loadMerchants(true);
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 搜索确认
  onSearch() {
    this.setData({
      page: 1,
      hasMore: true
    });
    this.loadMerchants();
  },

  // 切换筛选面板
  toggleFilter() {
    this.setData({
      showFilter: !this.data.showFilter
    });
  },

  // 状态筛选
  onStatusFilter(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      statusFilter: value
    });
  },

  // 类型筛选
  onTypeFilter(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      typeFilter: value
    });
  },

  // 重置筛选
  onResetFilter() {
    this.setData({
      statusFilter: 'all',
      typeFilter: 'all'
    });
  },

  // 应用筛选
  onApplyFilter() {
    this.setData({
      showFilter: false,
      page: 1,
      hasMore: true
    });
    this.loadMerchants();
  },

  // 加载商家列表
  async loadMerchants(append = false) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const { page, pageSize, searchKeyword, statusFilter, typeFilter } = this.data;

      const params = {
        page,
        pageSize,
        keyword: searchKeyword,
        status: statusFilter === 'all' ? undefined : statusFilter,
        type: typeFilter === 'all' ? undefined : typeFilter
      };

      const response = await merchantAPI.getMerchants(params);
      const merchants = response.list || response || [];
      const newMerchants = append ? [...this.data.merchants, ...merchants] : merchants;

      this.setData({
        merchants: newMerchants,
        hasMore: merchants.length === pageSize,
        loading: false
      });

    } catch (error) {
      console.error('加载商家列表失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  // 加载统计数据
  async loadStats() {
    try {
      const stats = await merchantAPI.getMerchantStats();
      this.setData({ stats });
    } catch (error) {
      console.error('加载统计数据失败:', error);
      // 使用默认统计数据
      this.setData({
        stats: {
          total: 0,
          active: 0,
          pending: 0,
          newToday: 0
        }
      });
    }
  },



  // 查看商家详情
  onMerchantDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/merchant-detail/merchant-detail?id=${id}`
    });
  },

  // 查看商家
  onViewMerchant(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/merchant-detail/merchant-detail?id=${id}`
    });
  },

  // 审核商家
  onAuditMerchant(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({
      showAuditModal: true,
      currentMerchantId: id,
      auditResult: '',
      auditRemark: ''
    });
  },

  // 隐藏审核弹窗
  hideAuditModal() {
    this.setData({
      showAuditModal: false,
      currentMerchantId: null,
      auditResult: '',
      auditRemark: ''
    });
  },

  // 审核结果选择
  onAuditResultChange(e) {
    const result = e.currentTarget.dataset.result;
    this.setData({
      auditResult: result
    });
  },

  // 审核备注输入
  onAuditRemarkInput(e) {
    this.setData({
      auditRemark: e.detail.value
    });
  },

  // 确认审核
  onConfirmAudit() {
    if (!this.data.auditResult) {
      return;
    }

    const { currentMerchantId, auditResult, auditRemark } = this.data;
    const resultText = auditResult === 'APPROVED' ? '通过' : '拒绝';

    wx.showModal({
      title: '确认审核',
      content: `确定要${resultText}这个商家的申请吗？`,
      success: (res) => {
        if (res.confirm) {
          this.submitAudit(currentMerchantId, auditResult, auditRemark);
        }
      }
    });
  },

  // 提交审核
  async submitAudit(merchantId, result, remark) {
    try {
      wx.showLoading({
        title: '提交中...'
      });

      await merchantAPI.auditMerchant(merchantId, {
        result,
        remark
      });

      // 更新本地数据
      const merchants = this.data.merchants.map(merchant => {
        if (merchant.id === merchantId) {
          return {
            ...merchant,
            status: result,
            statusText: result === 'APPROVED' ? '营业中' : '已拒绝'
          };
        }
        return merchant;
      });

      this.setData({ merchants });

      wx.hideLoading();
      wx.showToast({
        title: '审核完成',
        icon: 'success'
      });

      this.hideAuditModal();
      this.loadStats();

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '审核失败',
        icon: 'none'
      });
    }
  },

  // 切换商家状态
  onToggleMerchantStatus(e) {
    const { id, status } = e.currentTarget.dataset;
    const newStatus = status === 'ACTIVE' ? 'DISABLED' : 'ACTIVE';
    const actionText = newStatus === 'ACTIVE' ? '启用' : '禁用';

    wx.showModal({
      title: '确认操作',
      content: `确定要${actionText}这个商家吗？`,
      success: (res) => {
        if (res.confirm) {
          this.updateMerchantStatus(id, newStatus);
        }
      }
    });
  },

  // 更新商家状态
  async updateMerchantStatus(id, status) {
    try {
      wx.showLoading({
        title: status === 'ACTIVE' ? '启用中...' : '禁用中...'
      });

      await merchantAPI.updateMerchantStatus(id, status);

      // 更新本地数据
      const merchants = this.data.merchants.map(merchant => {
        if (merchant.id === id) {
          return {
            ...merchant,
            status,
            statusText: status === 'ACTIVE' ? '营业中' : '已禁用'
          };
        }
        return merchant;
      });

      this.setData({ merchants });

      wx.hideLoading();
      wx.showToast({
        title: status === 'ACTIVE' ? '启用成功' : '禁用成功',
        icon: 'success'
      });

      // 刷新统计数据
      this.loadStats();

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      });
    }
  },

  // 发送消息
  onSendMessage(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/send-message/send-message?merchantId=${id}`
    });
  },

  // 查看资质
  onViewCredential(e) {
    const { url, type } = e.currentTarget.dataset;
    this.setData({
      showCredentialModal: true,
      credentialUrl: url,
      credentialType: type
    });
  },

  // 隐藏资质查看弹窗
  hideCredentialModal() {
    this.setData({
      showCredentialModal: false,
      credentialUrl: '',
      credentialType: ''
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  }
});