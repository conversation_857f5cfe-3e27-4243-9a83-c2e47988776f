<!--管理端商家管理页面-->
<view class="container">
  <!-- 搜索和筛选 -->
  <view class="search-filter">
    <view class="search-box">
      <input
        class="search-input"
        placeholder="搜索商家名称/联系人"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
      />
      <image class="search-icon" src="/images/search-icon.png"></image>
    </view>
    <button class="filter-btn" bindtap="toggleFilter">
      <image class="filter-icon" src="/images/filter-icon.png"></image>
      <text>筛选</text>
    </button>
  </view>

  <!-- 筛选面板 -->
  <view class="filter-panel" wx:if="{{showFilter}}">
    <view class="filter-section">
      <text class="filter-title">审核状态</text>
      <view class="filter-tags">
        <view
          class="filter-tag {{statusFilter === item.value ? 'active' : ''}}"
          wx:for="{{statusOptions}}"
          wx:key="value"
          bindtap="onStatusFilter"
          data-value="{{item.value}}"
        >
          {{item.label}}
        </view>
      </view>
    </view>

    <view class="filter-section">
      <text class="filter-title">商家类型</text>
      <view class="filter-tags">
        <view
          class="filter-tag {{typeFilter === item.value ? 'active' : ''}}"
          wx:for="{{typeOptions}}"
          wx:key="value"
          bindtap="onTypeFilter"
          data-value="{{item.value}}"
        >
          {{item.label}}
        </view>
      </view>
    </view>

    <view class="filter-actions">
      <button class="reset-btn" bindtap="onResetFilter">重置</button>
      <button class="apply-btn" bindtap="onApplyFilter">应用</button>
    </view>
  </view>

  <!-- 商家统计 -->
  <view class="merchant-stats">
    <view class="stat-item">
      <text class="stat-number">{{stats.total}}</text>
      <text class="stat-label">总商家</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{stats.active}}</text>
      <text class="stat-label">营业中</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{stats.pending}}</text>
      <text class="stat-label">待审核</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{stats.newToday}}</text>
      <text class="stat-label">今日新增</text>
    </view>
  </view>

  <!-- 商家列表 -->
  <scroll-view
    class="merchant-list"
    scroll-y
    refresher-enabled
    refresher-triggered="{{refreshing}}"
    bindrefresherrefresh="onRefresh"
    bindscrolltolower="onLoadMore"
  >
    <view class="merchant-item" wx:for="{{merchants}}" wx:key="id" bindtap="onMerchantDetail" data-id="{{item.id}}">
      <!-- 商家头部信息 -->
      <view class="merchant-header">
        <image class="merchant-logo" src="{{item.logo}}" mode="aspectFill"></image>
        <view class="merchant-info">
          <view class="merchant-name-row">
            <text class="merchant-name">{{item.name}}</text>
            <view class="merchant-type {{item.type}}">
              <text class="type-text">{{item.typeText}}</text>
            </view>
          </view>
          <text class="merchant-contact">联系人：{{item.contactName}}</text>
          <text class="merchant-phone">{{item.contactPhone}}</text>
          <text class="merchant-register-time">注册时间：{{item.registerTime}}</text>
        </view>
        <view class="merchant-status {{item.status}}">
          <text class="status-text">{{item.statusText}}</text>
        </view>
      </view>

      <!-- 商家地址 -->
      <view class="merchant-address">
        <image class="location-icon" src="/images/location-icon.png"></image>
        <text class="address-text">{{item.address}}</text>
      </view>

      <!-- 商家数据 -->
      <view class="merchant-data">
        <view class="data-item">
          <text class="data-label">服务数</text>
          <text class="data-value">{{item.serviceCount}}</text>
        </view>
        <view class="data-item">
          <text class="data-label">订单数</text>
          <text class="data-value">{{item.orderCount}}</text>
        </view>
        <view class="data-item">
          <text class="data-label">评分</text>
          <text class="data-value">{{item.rating}}</text>
        </view>
        <view class="data-item">
          <text class="data-label">营业额</text>
          <text class="data-value">¥{{item.revenue}}</text>
        </view>
      </view>

      <!-- 资质信息 -->
      <view class="merchant-credentials" wx:if="{{item.businessLicense || item.idCard}}">
        <text class="credentials-label">资质文件：</text>
        <view class="credentials-files">
          <view class="credential-file" wx:if="{{item.businessLicense}}" bindtap="onViewCredential" data-url="{{item.businessLicense}}" data-type="营业执照">
            <image class="file-icon" src="/images/license-icon.png"></image>
            <text class="file-text">营业执照</text>
          </view>
          <view class="credential-file" wx:if="{{item.idCard}}" bindtap="onViewCredential" data-url="{{item.idCard}}" data-type="身份证">
            <image class="file-icon" src="/images/id-icon.png"></image>
            <text class="file-text">身份证</text>
          </view>
        </view>
      </view>

      <!-- 最近活动 -->
      <view class="merchant-activity" wx:if="{{item.lastActivity}}">
        <text class="activity-label">最近活动：</text>
        <text class="activity-text">{{item.lastActivity}}</text>
        <text class="activity-time">{{item.lastActivityTime}}</text>
      </view>

      <!-- 操作按钮 -->
      <view class="merchant-actions" catchtap="stopPropagation">
        <button class="action-btn view" bindtap="onViewMerchant" data-id="{{item.id}}">
          查看详情
        </button>
        <button
          class="action-btn audit"
          wx:if="{{item.status === 'PENDING'}}"
          bindtap="onAuditMerchant"
          data-id="{{item.id}}"
        >
          审核
        </button>
        <button
          class="action-btn {{item.status === 'ACTIVE' ? 'disable' : 'enable'}}"
          wx:if="{{item.status !== 'PENDING'}}"
          bindtap="onToggleMerchantStatus"
          data-id="{{item.id}}"
          data-status="{{item.status}}"
        >
          {{item.status === 'ACTIVE' ? '禁用' : '启用'}}
        </button>
        <button class="action-btn message" bindtap="onSendMessage" data-id="{{item.id}}">
          发消息
        </button>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore}}">
      <text class="load-text">{{loading ? '加载中...' : '上拉加载更多'}}</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!merchants.length && !loading}}">
      <image class="empty-image" src="/images/empty-merchants.png"></image>
      <text class="empty-text">暂无商家数据</text>
    </view>
  </scroll-view>
</view>

<!-- 审核弹窗 -->
<view class="audit-modal" wx:if="{{showAuditModal}}" bindtap="hideAuditModal">
  <view class="audit-modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">商家审核</text>
      <image class="close-btn" src="/images/close.png" bindtap="hideAuditModal"></image>
    </view>
    <view class="modal-body">
      <view class="audit-form">
        <view class="form-item">
          <text class="form-label">审核结果</text>
          <view class="audit-options">
            <view
              class="audit-option {{auditResult === 'APPROVED' ? 'active' : ''}}"
              bindtap="onAuditResultChange"
              data-result="APPROVED"
            >
              <image class="option-icon" src="/images/approve-icon.png"></image>
              <text class="option-text">通过</text>
            </view>
            <view
              class="audit-option {{auditResult === 'REJECTED' ? 'active' : ''}}"
              bindtap="onAuditResultChange"
              data-result="REJECTED"
            >
              <image class="option-icon" src="/images/reject-icon.png"></image>
              <text class="option-text">拒绝</text>
            </view>
          </view>
        </view>
        <view class="form-item">
          <text class="form-label">审核备注</text>
          <textarea
            class="audit-remark"
            placeholder="请输入审核备注（选填）"
            value="{{auditRemark}}"
            bindinput="onAuditRemarkInput"
            maxlength="200"
          ></textarea>
          <text class="char-count">{{auditRemark.length}}/200</text>
        </view>
      </view>
    </view>
    <view class="modal-footer">
      <button class="cancel-btn" bindtap="hideAuditModal">取消</button>
      <button class="confirm-btn" bindtap="onConfirmAudit" disabled="{{!auditResult}}">确认审核</button>
    </view>
  </view>
</view>

<!-- 资质查看弹窗 -->
<view class="credential-modal" wx:if="{{showCredentialModal}}" bindtap="hideCredentialModal">
  <view class="credential-modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">{{credentialType}}</text>
      <image class="close-btn" src="/images/close.png" bindtap="hideCredentialModal"></image>
    </view>
    <view class="modal-body">
      <image class="credential-image" src="{{credentialUrl}}" mode="aspectFit"></image>
    </view>
  </view>
</view>