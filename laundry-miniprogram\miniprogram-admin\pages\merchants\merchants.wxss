/* 管理端商家管理页面样式 */
.container {
  background: #f5f5f5;
  min-height: 100vh;
}

/* 搜索和筛选 */
.search-filter {
  background: #ffffff;
  padding: 20rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-box {
  flex: 1;
  position: relative;
  background: #f8f9fa;
  border-radius: 40rpx;
  padding: 0 40rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  height: 100%;
}

.search-input::placeholder {
  color: #999;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 20rpx;
}

.filter-btn {
  background: #1890ff;
  color: #ffffff;
  padding: 20rpx 30rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  border: none;
}

.filter-icon {
  width: 28rpx;
  height: 28rpx;
}

/* 筛选面板 */
.filter-panel {
  background: #ffffff;
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filter-section {
  margin-bottom: 40rpx;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.filter-tag {
  padding: 16rpx 32rpx;
  background: #f8f9fa;
  color: #666;
  border-radius: 40rpx;
  font-size: 26rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.filter-tag.active {
  background: #1890ff;
  color: #ffffff;
  border-color: #1890ff;
}

.filter-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.reset-btn,
.apply-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.reset-btn {
  background: #f8f9fa;
  color: #666;
}

.apply-btn {
  background: #1890ff;
  color: #ffffff;
}

/* 商家统计 */
.merchant-stats {
  background: #ffffff;
  padding: 30rpx;
  margin: 20rpx;
  border-radius: 20rpx;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 商家列表 */
.merchant-list {
  height: calc(100vh - 300rpx);
  padding: 20rpx;
}

.merchant-item {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.merchant-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 商家头部 */
.merchant-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.merchant-logo {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.merchant-info {
  flex: 1;
  margin-right: 20rpx;
}

.merchant-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.merchant-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 16rpx;
}

.merchant-type {
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: bold;
}

.merchant-type.INDIVIDUAL {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.merchant-type.ENTERPRISE {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.merchant-type.CHAIN {
  background: rgba(250, 140, 22, 0.1);
  color: #fa8c16;
}

.merchant-contact,
.merchant-phone,
.merchant-register-time {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.merchant-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  flex-shrink: 0;
}

.merchant-status.PENDING {
  background: rgba(250, 140, 22, 0.1);
  color: #fa8c16;
}

.merchant-status.ACTIVE {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.merchant-status.DISABLED {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.merchant-status.REJECTED {
  background: rgba(140, 140, 140, 0.1);
  color: #8c8c8c;
}

/* 商家地址 */
.merchant-address {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.location-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 12rpx;
}

.address-text {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

/* 商家数据 */
.merchant-data {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f0f8ff;
  border-radius: 12rpx;
}

.data-item {
  text-align: center;
  flex: 1;
}

.data-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.data-value {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

/* 资质信息 */
.merchant-credentials {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #fff7e6;
  border-radius: 12rpx;
  border-left: 4rpx solid #faad14;
}

.credentials-label {
  display: block;
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.credentials-files {
  display: flex;
  gap: 20rpx;
}

.credential-file {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background: #ffffff;
  border-radius: 8rpx;
  border: 2rpx solid #faad14;
  transition: all 0.3s ease;
}

.credential-file:active {
  background: #fafafa;
  transform: scale(0.98);
}

.file-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.file-text {
  font-size: 24rpx;
  color: #fa8c16;
  font-weight: 500;
}

/* 商家活动 */
.merchant-activity {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 16rpx 20rpx;
  background: #f0f8ff;
  border-radius: 12rpx;
  border-left: 4rpx solid #1890ff;
}

.activity-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 12rpx;
}

.activity-text {
  font-size: 26rpx;
  color: #333;
  margin-right: 12rpx;
}

.activity-time {
  font-size: 22rpx;
  color: #999;
  margin-left: auto;
}

/* 商家操作 */
.merchant-actions {
  display: flex;
  gap: 20rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #f5f5f5;
}

.action-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.view {
  background: #1890ff;
  color: #ffffff;
}

.action-btn.audit {
  background: #faad14;
  color: #ffffff;
}

.action-btn.enable {
  background: #52c41a;
  color: #ffffff;
}

.action-btn.disable {
  background: #ff4d4f;
  color: #ffffff;
}

.action-btn.message {
  background: #722ed1;
  color: #ffffff;
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx 0;
  font-size: 28rpx;
  color: #999;
}

/* 弹窗样式 */
.audit-modal,
.credential-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.audit-modal-content,
.credential-modal-content {
  background: #ffffff;
  border-radius: 20rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  width: 40rpx;
  height: 40rpx;
}

.modal-body {
  padding: 40rpx;
}

/* 审核表单 */
.audit-form {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.audit-options {
  display: flex;
  gap: 20rpx;
}

.audit-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.audit-option.active {
  background: #f0f8ff;
  border-color: #1890ff;
}

.option-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 16rpx;
}

.option-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.audit-remark {
  min-height: 200rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.audit-remark:focus {
  border-color: #1890ff;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 40rpx 40rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666;
}

.confirm-btn {
  background: #1890ff;
  color: #ffffff;
}

.confirm-btn:disabled {
  background: #cccccc;
  color: #999999;
}

/* 资质查看 */
.credential-image {
  width: 100%;
  max-height: 60vh;
}

/* 动画效果 */
.merchant-item {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 375px) {
  .merchant-list {
    padding: 15rpx;
  }
  
  .merchant-item {
    padding: 25rpx;
    margin-bottom: 15rpx;
  }
  
  .merchant-logo {
    width: 80rpx;
    height: 80rpx;
    border-radius: 40rpx;
  }
  
  .merchant-name {
    font-size: 28rpx;
  }
  
  .merchant-data {
    padding: 15rpx;
  }
  
  .action-btn {
    height: 60rpx;
    font-size: 24rpx;
  }
}
