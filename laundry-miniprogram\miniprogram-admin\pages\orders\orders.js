// 管理端订单监控页面
const app = getApp();
const { orderAPI, dashboardAPI } = require('../../utils/api.js');

Page({
  data: {
    searchKeyword: '',
    showFilter: false,
    statusFilter: 'all',
    timeFilter: 'all',
    orders: [],
    stats: {
      total: 0,
      processing: 0,
      completed: 0,
      totalAmount: '0.00'
    },
    statusOptions: [
      { label: '全部', value: 'all' },
      { label: '待接单', value: 'PENDING_ACCEPT' },
      { label: '待支付', value: 'PENDING_PAYMENT' },
      { label: '进行中', value: 'PROCESSING' },
      { label: '已完成', value: 'COMPLETED' },
      { label: '已取消', value: 'CANCELLED' }
    ],
    timeOptions: [
      { label: '全部', value: 'all' },
      { label: '今日', value: 'today' },
      { label: '本周', value: 'week' },
      { label: '本月', value: 'month' }
    ],
    refreshing: false,
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    showInterveneModal: false,
    currentOrderId: null,
    interveneType: '',
    interveneRemark: '',
    interveneTypes: [
      { label: '联系用户', value: 'CONTACT_USER' },
      { label: '联系商家', value: 'CONTACT_MERCHANT' },
      { label: '协调处理', value: 'COORDINATE' },
      { label: '强制完成', value: 'FORCE_COMPLETE' },
      { label: '强制取消', value: 'FORCE_CANCEL' }
    ]
  },

  onLoad() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    this.loadOrders();
    this.loadStats();
  },

  onPullDownRefresh() {
    this.onRefresh();
  },

  // 下拉刷新
  onRefresh() {
    this.setData({
      refreshing: true,
      page: 1,
      hasMore: true
    });

    this.loadOrders().finally(() => {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onLoadMore() {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }

    this.setData({
      page: this.data.page + 1
    });

    this.loadOrders(true);
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 搜索确认
  onSearch() {
    this.setData({
      page: 1,
      hasMore: true
    });
    this.loadOrders();
  },

  // 切换筛选面板
  toggleFilter() {
    this.setData({
      showFilter: !this.data.showFilter
    });
  },

  // 状态筛选
  onStatusFilter(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      statusFilter: value
    });
  },

  // 时间筛选
  onTimeFilter(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      timeFilter: value
    });
  },

  // 重置筛选
  onResetFilter() {
    this.setData({
      statusFilter: 'all',
      timeFilter: 'all'
    });
  },

  // 应用筛选
  onApplyFilter() {
    this.setData({
      showFilter: false,
      page: 1,
      hasMore: true
    });
    this.loadOrders();
  },

  // 加载订单列表
  async loadOrders(append = false) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const { page, pageSize, searchKeyword, statusFilter, timeFilter } = this.data;

      const params = {
        page,
        pageSize,
        keyword: searchKeyword,
        status: statusFilter === 'all' ? undefined : statusFilter,
        timeRange: timeFilter === 'all' ? undefined : timeFilter
      };

      const response = await orderAPI.getOrders(params);
      const orders = response.list || response || [];

      const newOrders = append ? [...this.data.orders, ...orders] : orders;

      this.setData({
        orders: newOrders,
        hasMore: orders.length === pageSize,
        loading: false
      });

    } catch (error) {
      console.error('加载订单列表失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  // 加载统计数据
  async loadStats() {
    try {
      const stats = await dashboardAPI.getOrderStats();
      this.setData({
        stats: {
          total: stats.total || 0,
          processing: stats.processing || 0,
          completed: stats.completed || 0,
          totalAmount: stats.totalAmount || '0.00'
        }
      });
    } catch (error) {
      console.error('加载统计数据失败:', error);
      // 使用默认数据
      this.setData({
        stats: {
          total: 0,
          processing: 0,
          completed: 0,
          totalAmount: '0.00'
        }
      });
    }
  },



  // 查看订单详情
  onOrderDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?id=${id}`
    });
  },

  // 查看订单
  onViewOrder(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?id=${id}`
    });
  },

  // 介入处理
  onInterveneOrder(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({
      showInterveneModal: true,
      currentOrderId: id,
      interveneType: '',
      interveneRemark: ''
    });
  },

  // 隐藏介入处理弹窗
  hideInterveneModal() {
    this.setData({
      showInterveneModal: false,
      currentOrderId: null,
      interveneType: '',
      interveneRemark: ''
    });
  },

  // 介入类型选择
  onInterveneTypeChange(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      interveneType: type
    });
  },

  // 介入说明输入
  onInterveneRemarkInput(e) {
    this.setData({
      interveneRemark: e.detail.value
    });
  },

  // 确认介入处理
  onConfirmIntervene() {
    if (!this.data.interveneType) {
      return;
    }

    const { currentOrderId, interveneType, interveneRemark } = this.data;
    const typeText = this.data.interveneTypes.find(t => t.value === interveneType)?.label;

    wx.showModal({
      title: '确认介入',
      content: `确定要进行"${typeText}"处理吗？`,
      success: (res) => {
        if (res.confirm) {
          this.submitIntervene(currentOrderId, interveneType, interveneRemark);
        }
      }
    });
  },

  // 提交介入处理
  async submitIntervene(orderId, type, remark) {
    try {
      wx.showLoading({
        title: '处理中...'
      });

      await orderAPI.interveneOrder(orderId, {
        type,
        remark
      });

      wx.hideLoading();
      wx.showToast({
        title: '介入处理成功',
        icon: 'success'
      });

      this.hideInterveneModal();
      this.loadOrders();
      this.loadStats();

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '处理失败',
        icon: 'none'
      });
    }
  },

  // 退款处理
  onRefundOrder(e) {
    const id = e.currentTarget.dataset.id;

    wx.showModal({
      title: '退款处理',
      content: '确定要为此订单进行退款处理吗？',
      success: (res) => {
        if (res.confirm) {
          this.processRefund(id);
        }
      }
    });
  },

  // 处理退款
  async processRefund(orderId) {
    try {
      wx.showLoading({
        title: '处理中...'
      });

      await orderAPI.processRefund(orderId);

      wx.hideLoading();
      wx.showToast({
        title: '退款处理成功',
        icon: 'success'
      });

      this.loadOrders();
      this.loadStats();

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '退款失败',
        icon: 'none'
      });
    }
  },

  // 处理异常
  onHandleException(e) {
    const id = e.currentTarget.dataset.id;
    this.onInterveneOrder(e);
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  }
});