// 管理端订单监控页面
const app = getApp();

Page({
  data: {
    searchKeyword: '',
    showFilter: false,
    statusFilter: 'all',
    timeFilter: 'all',
    orders: [],
    stats: {
      total: 0,
      processing: 0,
      completed: 0,
      totalAmount: '0.00'
    },
    statusOptions: [
      { label: '全部', value: 'all' },
      { label: '待接单', value: 'PENDING_ACCEPT' },
      { label: '待支付', value: 'PENDING_PAYMENT' },
      { label: '进行中', value: 'PROCESSING' },
      { label: '已完成', value: 'COMPLETED' },
      { label: '已取消', value: 'CANCELLED' }
    ],
    timeOptions: [
      { label: '全部', value: 'all' },
      { label: '今日', value: 'today' },
      { label: '本周', value: 'week' },
      { label: '本月', value: 'month' }
    ],
    refreshing: false,
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    showInterveneModal: false,
    currentOrderId: null,
    interveneType: '',
    interveneRemark: '',
    interveneTypes: [
      { label: '联系用户', value: 'CONTACT_USER' },
      { label: '联系商家', value: 'CONTACT_MERCHANT' },
      { label: '协调处理', value: 'COORDINATE' },
      { label: '强制完成', value: 'FORCE_COMPLETE' },
      { label: '强制取消', value: 'FORCE_CANCEL' }
    ]
  },

  onLoad() {
    this.loadOrders();
    this.loadStats();
  },

  onPullDownRefresh() {
    this.onRefresh();
  },

  // 下拉刷新
  onRefresh() {
    this.setData({
      refreshing: true,
      page: 1,
      hasMore: true
    });

    this.loadOrders().finally(() => {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onLoadMore() {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }

    this.setData({
      page: this.data.page + 1
    });

    this.loadOrders(true);
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 搜索确认
  onSearch() {
    this.setData({
      page: 1,
      hasMore: true
    });
    this.loadOrders();
  },

  // 切换筛选面板
  toggleFilter() {
    this.setData({
      showFilter: !this.data.showFilter
    });
  },

  // 状态筛选
  onStatusFilter(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      statusFilter: value
    });
  },

  // 时间筛选
  onTimeFilter(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      timeFilter: value
    });
  },

  // 重置筛选
  onResetFilter() {
    this.setData({
      statusFilter: 'all',
      timeFilter: 'all'
    });
  },

  // 应用筛选
  onApplyFilter() {
    this.setData({
      showFilter: false,
      page: 1,
      hasMore: true
    });
    this.loadOrders();
  },

  // 加载订单列表
  async loadOrders(append = false) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const { page, pageSize, searchKeyword, statusFilter, timeFilter } = this.data;

      // 模拟API调用
      const response = await this.getMockOrders(page, pageSize, searchKeyword, statusFilter, timeFilter);

      const newOrders = append ?
        [...this.data.orders, ...response.data] :
        response.data;

      this.setData({
        orders: newOrders,
        hasMore: response.hasMore,
        loading: false
      });

    } catch (error) {
      console.error('加载订单列表失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  // 加载统计数据
  async loadStats() {
    try {
      // 模拟API调用
      const stats = await this.getMockStats();
      this.setData({ stats });
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  },

  // 模拟获取订单数据
  getMockOrders(page, pageSize, keyword, statusFilter, timeFilter) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const allOrders = [
          {
            id: 1,
            orderNumber: 'LY202412150001',
            createTime: '2024-12-15 14:30',
            status: 'PROCESSING',
            statusText: '进行中',
            userName: '张先生',
            userAvatar: '/images/user1.png',
            merchantName: '专业洗护中心',
            merchantLogo: '/images/merchant1.png',
            serviceName: '普通衣物洗涤',
            serviceImage: '/images/service1.jpg',
            serviceSpec: '上门取送',
            servicePrice: '15.00',
            quantity: 3,
            totalAmount: '45.00',
            contactName: '张先生',
            contactPhone: '138****0001',
            address: '北京市朝阳区建国路88号',
            progress: ['下单', '接单', '取件', '清洗', '配送'],
            currentStep: 2,
            canIntervene: true,
            canRefund: false
          },
          {
            id: 2,
            orderNumber: 'LY202412150002',
            createTime: '2024-12-15 10:15',
            status: 'PENDING_ACCEPT',
            statusText: '待接单',
            userName: '李女士',
            userAvatar: '/images/user2.png',
            merchantName: '快洁洗衣店',
            merchantLogo: '/images/merchant2.png',
            serviceName: '高档衣物护理',
            serviceImage: '/images/service2.jpg',
            serviceSpec: '到店服务',
            servicePrice: '35.00',
            quantity: 2,
            totalAmount: '70.00',
            contactName: '李女士',
            contactPhone: '139****0002',
            address: '上海市浦东新区陆家嘴路123号',
            progress: ['下单', '接单', '服务', '完成'],
            currentStep: 0,
            canIntervene: true,
            canRefund: true,
            exception: {
              type: '超时未接单',
              description: '订单已超过30分钟未被商家接单'
            }
          },
          {
            id: 3,
            orderNumber: 'LY202412140005',
            createTime: '2024-12-14 16:45',
            status: 'COMPLETED',
            statusText: '已完成',
            userName: '王先生',
            userAvatar: '/images/user3.png',
            merchantName: '绿色洗护连锁',
            merchantLogo: '/images/merchant3.png',
            serviceName: '运动鞋清洗',
            serviceImage: '/images/service3.jpg',
            serviceSpec: '上门取送',
            servicePrice: '25.00',
            quantity: 1,
            totalAmount: '25.00',
            contactName: '王先生',
            contactPhone: '137****0003',
            address: '广州市天河区珠江新城花城大道66号',
            progress: ['下单', '接单', '取件', '清洗', '配送', '完成'],
            currentStep: 5,
            canIntervene: false,
            canRefund: false
          }
        ];

        // 根据筛选条件过滤
        let filteredOrders = allOrders;

        if (statusFilter !== 'all') {
          filteredOrders = filteredOrders.filter(order => order.status === statusFilter);
        }

        // 根据关键词搜索
        if (keyword) {
          filteredOrders = filteredOrders.filter(order =>
            order.orderNumber.includes(keyword) ||
            order.userName.includes(keyword) ||
            order.merchantName.includes(keyword)
          );
        }

        // 分页
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        const data = filteredOrders.slice(start, end);
        const hasMore = end < filteredOrders.length;

        resolve({ data, hasMore });
      }, 500);
    });
  },

  // 模拟获取统计数据
  getMockStats() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          total: 1256,
          processing: 89,
          completed: 1089,
          totalAmount: '156,780.00'
        });
      }, 300);
    });
  },

  // 查看订单详情
  onOrderDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?id=${id}`
    });
  },

  // 查看订单
  onViewOrder(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?id=${id}`
    });
  },

  // 介入处理
  onInterveneOrder(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({
      showInterveneModal: true,
      currentOrderId: id,
      interveneType: '',
      interveneRemark: ''
    });
  },

  // 隐藏介入处理弹窗
  hideInterveneModal() {
    this.setData({
      showInterveneModal: false,
      currentOrderId: null,
      interveneType: '',
      interveneRemark: ''
    });
  },

  // 介入类型选择
  onInterveneTypeChange(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      interveneType: type
    });
  },

  // 介入说明输入
  onInterveneRemarkInput(e) {
    this.setData({
      interveneRemark: e.detail.value
    });
  },

  // 确认介入处理
  onConfirmIntervene() {
    if (!this.data.interveneType) {
      return;
    }

    const { currentOrderId, interveneType, interveneRemark } = this.data;
    const typeText = this.data.interveneTypes.find(t => t.value === interveneType)?.label;

    wx.showModal({
      title: '确认介入',
      content: `确定要进行"${typeText}"处理吗？`,
      success: (res) => {
        if (res.confirm) {
          this.submitIntervene(currentOrderId, interveneType, interveneRemark);
        }
      }
    });
  },

  // 提交介入处理
  async submitIntervene(orderId, type, remark) {
    try {
      wx.showLoading({
        title: '处理中...'
      });

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      wx.hideLoading();
      wx.showToast({
        title: '介入处理成功',
        icon: 'success'
      });

      this.hideInterveneModal();
      this.loadOrders();
      this.loadStats();

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '处理失败',
        icon: 'none'
      });
    }
  },

  // 退款处理
  onRefundOrder(e) {
    const id = e.currentTarget.dataset.id;

    wx.showModal({
      title: '退款处理',
      content: '确定要为此订单进行退款处理吗？',
      success: (res) => {
        if (res.confirm) {
          this.processRefund(id);
        }
      }
    });
  },

  // 处理退款
  async processRefund(orderId) {
    try {
      wx.showLoading({
        title: '处理中...'
      });

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      wx.hideLoading();
      wx.showToast({
        title: '退款处理成功',
        icon: 'success'
      });

      this.loadOrders();
      this.loadStats();

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '退款失败',
        icon: 'none'
      });
    }
  },

  // 处理异常
  onHandleException(e) {
    const id = e.currentTarget.dataset.id;
    this.onInterveneOrder(e);
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  }
});