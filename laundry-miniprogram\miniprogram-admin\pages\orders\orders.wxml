<!--管理端订单监控页面-->
<view class="container">
  <!-- 搜索和筛选 -->
  <view class="search-filter">
    <view class="search-box">
      <input
        class="search-input"
        placeholder="搜索订单号/用户/商家"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
      />
      <image class="search-icon" src="/images/search-icon.png"></image>
    </view>
    <button class="filter-btn" bindtap="toggleFilter">
      <image class="filter-icon" src="/images/filter-icon.png"></image>
      <text>筛选</text>
    </button>
  </view>

  <!-- 筛选面板 -->
  <view class="filter-panel" wx:if="{{showFilter}}">
    <view class="filter-section">
      <text class="filter-title">订单状态</text>
      <view class="filter-tags">
        <view
          class="filter-tag {{statusFilter === item.value ? 'active' : ''}}"
          wx:for="{{statusOptions}}"
          wx:key="value"
          bindtap="onStatusFilter"
          data-value="{{item.value}}"
        >
          {{item.label}}
        </view>
      </view>
    </view>

    <view class="filter-section">
      <text class="filter-title">时间范围</text>
      <view class="filter-tags">
        <view
          class="filter-tag {{timeFilter === item.value ? 'active' : ''}}"
          wx:for="{{timeOptions}}"
          wx:key="value"
          bindtap="onTimeFilter"
          data-value="{{item.value}}"
        >
          {{item.label}}
        </view>
      </view>
    </view>

    <view class="filter-actions">
      <button class="reset-btn" bindtap="onResetFilter">重置</button>
      <button class="apply-btn" bindtap="onApplyFilter">应用</button>
    </view>
  </view>

  <!-- 订单统计 -->
  <view class="order-stats">
    <view class="stat-item">
      <text class="stat-number">{{stats.total}}</text>
      <text class="stat-label">总订单</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{stats.processing}}</text>
      <text class="stat-label">处理中</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{stats.completed}}</text>
      <text class="stat-label">已完成</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">¥{{stats.totalAmount}}</text>
      <text class="stat-label">总金额</text>
    </view>
  </view>

  <!-- 订单列表 -->
  <scroll-view
    class="order-list"
    scroll-y
    refresher-enabled
    refresher-triggered="{{refreshing}}"
    bindrefresherrefresh="onRefresh"
    bindscrolltolower="onLoadMore"
  >
    <view class="order-item" wx:for="{{orders}}" wx:key="id" bindtap="onOrderDetail" data-id="{{item.id}}">
      <!-- 订单头部 -->
      <view class="order-header">
        <view class="order-info">
          <text class="order-number">{{item.orderNumber}}</text>
          <text class="order-time">{{item.createTime}}</text>
        </view>
        <view class="order-status {{item.status}}">
          <text class="status-text">{{item.statusText}}</text>
        </view>
      </view>

      <!-- 用户和商家信息 -->
      <view class="order-parties">
        <view class="party-info">
          <image class="party-avatar" src="{{item.userAvatar}}" mode="aspectFill"></image>
          <view class="party-details">
            <text class="party-name">{{item.userName}}</text>
            <text class="party-label">用户</text>
          </view>
        </view>
        <image class="arrow-icon" src="/images/arrow-right.png"></image>
        <view class="party-info">
          <image class="party-avatar" src="{{item.merchantLogo}}" mode="aspectFill"></image>
          <view class="party-details">
            <text class="party-name">{{item.merchantName}}</text>
            <text class="party-label">商家</text>
          </view>
        </view>
      </view>

      <!-- 服务信息 -->
      <view class="service-info">
        <image class="service-image" src="{{item.serviceImage}}" mode="aspectFill"></image>
        <view class="service-details">
          <text class="service-name">{{item.serviceName}}</text>
          <text class="service-spec">{{item.serviceSpec}}</text>
          <view class="service-price-qty">
            <text class="service-price">¥{{item.servicePrice}}</text>
            <text class="service-qty">x{{item.quantity}}</text>
          </view>
        </view>
      </view>

      <!-- 地址信息 -->
      <view class="address-info">
        <image class="location-icon" src="/images/location-icon.png"></image>
        <view class="address-details">
          <text class="address-contact">{{item.contactName}} {{item.contactPhone}}</text>
          <text class="address-text">{{item.address}}</text>
        </view>
      </view>

      <!-- 订单金额 -->
      <view class="order-amount">
        <text class="amount-label">订单金额</text>
        <text class="amount-value">¥{{item.totalAmount}}</text>
      </view>

      <!-- 订单进度 -->
      <view class="order-progress" wx:if="{{item.progress}}">
        <text class="progress-label">订单进度：</text>
        <view class="progress-steps">
          <view
            class="progress-step {{index <= item.currentStep ? 'completed' : ''}}"
            wx:for="{{item.progress}}"
            wx:key="*this"
            wx:for-index="index"
          >
            <view class="step-dot"></view>
            <text class="step-text">{{item}}</text>
          </view>
        </view>
      </view>

      <!-- 异常信息 -->
      <view class="order-exception" wx:if="{{item.exception}}">
        <image class="warning-icon" src="/images/warning-icon.png"></image>
        <view class="exception-details">
          <text class="exception-type">{{item.exception.type}}</text>
          <text class="exception-desc">{{item.exception.description}}</text>
        </view>
        <button class="handle-btn" bindtap="onHandleException" data-id="{{item.id}}" catchtap="stopPropagation">
          处理
        </button>
      </view>

      <!-- 操作按钮 -->
      <view class="order-actions" catchtap="stopPropagation">
        <button class="action-btn view" bindtap="onViewOrder" data-id="{{item.id}}">
          查看详情
        </button>
        <button
          class="action-btn intervene"
          wx:if="{{item.canIntervene}}"
          bindtap="onInterveneOrder"
          data-id="{{item.id}}"
        >
          介入处理
        </button>
        <button
          class="action-btn refund"
          wx:if="{{item.canRefund}}"
          bindtap="onRefundOrder"
          data-id="{{item.id}}"
        >
          退款处理
        </button>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore}}">
      <text class="load-text">{{loading ? '加载中...' : '上拉加载更多'}}</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!orders.length && !loading}}">
      <image class="empty-image" src="/images/empty-orders.png"></image>
      <text class="empty-text">暂无订单数据</text>
    </view>
  </scroll-view>
</view>

<!-- 介入处理弹窗 -->
<view class="intervene-modal" wx:if="{{showInterveneModal}}" bindtap="hideInterveneModal">
  <view class="intervene-modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">介入处理</text>
      <image class="close-btn" src="/images/close.png" bindtap="hideInterveneModal"></image>
    </view>
    <view class="modal-body">
      <view class="intervene-form">
        <view class="form-item">
          <text class="form-label">处理方式</text>
          <view class="intervene-options">
            <view
              class="intervene-option {{interveneType === item.value ? 'active' : ''}}"
              wx:for="{{interveneTypes}}"
              wx:key="value"
              bindtap="onInterveneTypeChange"
              data-type="{{item.value}}"
            >
              <text class="option-text">{{item.label}}</text>
            </view>
          </view>
        </view>
        <view class="form-item">
          <text class="form-label">处理说明</text>
          <textarea
            class="intervene-remark"
            placeholder="请输入处理说明"
            value="{{interveneRemark}}"
            bindinput="onInterveneRemarkInput"
            maxlength="200"
          ></textarea>
          <text class="char-count">{{interveneRemark.length}}/200</text>
        </view>
      </view>
    </view>
    <view class="modal-footer">
      <button class="cancel-btn" bindtap="hideInterveneModal">取消</button>
      <button class="confirm-btn" bindtap="onConfirmIntervene" disabled="{{!interveneType}}">确认处理</button>
    </view>
  </view>
</view>