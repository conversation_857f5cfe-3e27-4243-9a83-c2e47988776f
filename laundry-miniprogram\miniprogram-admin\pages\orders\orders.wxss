/* 管理端订单监控页面样式 */
.container {
  background: #f5f5f5;
  min-height: 100vh;
}

/* 搜索和筛选 */
.search-filter {
  background: #ffffff;
  padding: 20rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-box {
  flex: 1;
  position: relative;
  background: #f8f9fa;
  border-radius: 40rpx;
  padding: 0 40rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  height: 100%;
}

.search-input::placeholder {
  color: #999;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 20rpx;
}

.filter-btn {
  background: #1890ff;
  color: #ffffff;
  padding: 20rpx 30rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  border: none;
}

.filter-icon {
  width: 28rpx;
  height: 28rpx;
}

/* 筛选面板 */
.filter-panel {
  background: #ffffff;
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filter-section {
  margin-bottom: 40rpx;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.filter-tag {
  padding: 16rpx 32rpx;
  background: #f8f9fa;
  color: #666;
  border-radius: 40rpx;
  font-size: 26rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.filter-tag.active {
  background: #1890ff;
  color: #ffffff;
  border-color: #1890ff;
}

.filter-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.reset-btn,
.apply-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.reset-btn {
  background: #f8f9fa;
  color: #666;
}

.apply-btn {
  background: #1890ff;
  color: #ffffff;
}

/* 订单统计 */
.order-stats {
  background: #ffffff;
  padding: 30rpx;
  margin: 20rpx;
  border-radius: 20rpx;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 订单列表 */
.order-list {
  height: calc(100vh - 300rpx);
  padding: 20rpx;
}

.order-item {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
}

.order-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.order-info {
  flex: 1;
}

.order-number {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.order-status.PENDING_ACCEPT {
  background: rgba(250, 140, 22, 0.1);
  color: #fa8c16;
}

.order-status.PENDING_PAYMENT {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.order-status.PROCESSING {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.order-status.COMPLETED {
  background: rgba(82, 196, 26, 0.2);
  color: #389e0d;
}

.order-status.CANCELLED {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

/* 用户和商家信息 */
.order-parties {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.party-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.party-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin-right: 16rpx;
}

.party-details {
  flex: 1;
}

.party-name {
  display: block;
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.party-label {
  font-size: 22rpx;
  color: #999;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  margin: 0 20rpx;
}

/* 服务信息 */
.service-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f0f8ff;
  border-radius: 12rpx;
}

.service-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.service-details {
  flex: 1;
}

.service-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.service-spec {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.service-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-price {
  font-size: 28rpx;
  color: #1890ff;
  font-weight: bold;
}

.service-qty {
  font-size: 24rpx;
  color: #999;
}

/* 地址信息 */
.address-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #fff7e6;
  border-radius: 12rpx;
  border-left: 4rpx solid #faad14;
}

.location-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.address-details {
  flex: 1;
}

.address-contact {
  display: block;
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.address-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 订单金额 */
.order-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f6ffed;
  border-radius: 12rpx;
  border-left: 4rpx solid #52c41a;
}

.amount-label {
  font-size: 28rpx;
  color: #333;
}

.amount-value {
  font-size: 32rpx;
  color: #52c41a;
  font-weight: bold;
}

/* 订单进度 */
.order-progress {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f0f8ff;
  border-radius: 12rpx;
  border-left: 4rpx solid #1890ff;
}

.progress-label {
  display: block;
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.progress-steps {
  display: flex;
  align-items: center;
  gap: 20rpx;
  overflow-x: auto;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80rpx;
  position: relative;
}

.step-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 10rpx;
  background: #d9d9d9;
  margin-bottom: 8rpx;
  transition: all 0.3s ease;
}

.progress-step.completed .step-dot {
  background: #1890ff;
}

.step-text {
  font-size: 22rpx;
  color: #666;
  text-align: center;
}

.progress-step.completed .step-text {
  color: #1890ff;
  font-weight: 500;
}

/* 异常信息 */
.order-exception {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #fff2f0;
  border-radius: 12rpx;
  border-left: 4rpx solid #ff4d4f;
}

.warning-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.exception-details {
  flex: 1;
  margin-right: 20rpx;
}

.exception-type {
  display: block;
  font-size: 26rpx;
  color: #ff4d4f;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.exception-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.handle-btn {
  background: #ff4d4f;
  color: #ffffff;
  padding: 16rpx 24rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  border: none;
}

/* 订单操作 */
.order-actions {
  display: flex;
  gap: 20rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #f5f5f5;
}

.action-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.view {
  background: #1890ff;
  color: #ffffff;
}

.action-btn.intervene {
  background: #faad14;
  color: #ffffff;
}

.action-btn.refund {
  background: #ff4d4f;
  color: #ffffff;
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx 0;
  font-size: 28rpx;
  color: #999;
}

/* 介入处理弹窗 */
.intervene-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.intervene-modal-content {
  background: #ffffff;
  border-radius: 20rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  width: 40rpx;
  height: 40rpx;
}

.modal-body {
  padding: 40rpx;
}

/* 介入表单 */
.intervene-form {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.intervene-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.intervene-option {
  padding: 20rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.intervene-option.active {
  background: #f0f8ff;
  border-color: #1890ff;
}

.option-text {
  font-size: 28rpx;
  color: #333;
}

.intervene-remark {
  min-height: 200rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.intervene-remark:focus {
  border-color: #1890ff;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 40rpx 40rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666;
}

.confirm-btn {
  background: #1890ff;
  color: #ffffff;
}

.confirm-btn:disabled {
  background: #cccccc;
  color: #999999;
}

/* 动画效果 */
.order-item {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 375px) {
  .order-list {
    padding: 15rpx;
  }
  
  .order-item {
    padding: 25rpx;
    margin-bottom: 15rpx;
  }
  
  .party-avatar {
    width: 50rpx;
    height: 50rpx;
    border-radius: 25rpx;
  }
  
  .service-image {
    width: 80rpx;
    height: 80rpx;
  }
  
  .action-btn {
    height: 60rpx;
    font-size: 24rpx;
  }
}
