// 管理端个人中心页面
const app = getApp();
const { adminAPI, dashboardAPI } = require('../../utils/api');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 管理员信息
    adminInfo: {
      id: null,
      username: '',
      realName: '',
      phone: '',
      email: '',
      avatar: '',
      role: '',
      roleText: '',
      department: '',
      status: '',
      statusText: ''
    },

    // 今日数据统计
    todayStats: {
      newUsers: 0,
      userChange: 0,
      newOrders: 0,
      orderChange: 0,
      newMerchants: 0,
      merchantChange: 0,
      revenue: '0.00',
      revenueChange: 0
    },

    // 待处理事项统计
    pendingStats: {
      merchants: 0,
      complaints: 0,
      abnormalOrders: 0
    },

    // 总体统计
    stats: {
      totalUsers: 0,
      totalMerchants: 0,
      totalOrders: 0
    },

    loading: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadAdminInfo();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    // 每次显示页面时刷新数据
    this.loadTodayStats();
    this.loadPendingStats();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadAdminInfo();
    this.loadTodayStats();
    this.loadPendingStats();
    this.loadOverallStats();

    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 加载管理员信息
   */
  async loadAdminInfo() {
    try {
      this.setData({ loading: true });

      // 从本地存储获取管理员信息
      const adminInfo = app.globalData.adminInfo || wx.getStorageSync('adminInfo');
      if (adminInfo) {
        this.setData({
          adminInfo: {
            ...this.data.adminInfo,
            ...adminInfo,
            roleText: this.getRoleText(adminInfo.role),
            statusText: adminInfo.status === 'ACTIVE' ? '在线' : '离线'
          }
        });
      }

      // 从服务器获取最新信息
      const serverAdminInfo = await adminAPI.getProfile();
      this.setData({
        adminInfo: {
          ...serverAdminInfo,
          roleText: this.getRoleText(serverAdminInfo.role),
          statusText: serverAdminInfo.status === 'ACTIVE' ? '在线' : '离线'
        }
      });

      // 更新全局和本地存储
      app.globalData.adminInfo = serverAdminInfo;
      wx.setStorageSync('adminInfo', serverAdminInfo);

      // 加载其他数据
      this.loadTodayStats();
      this.loadPendingStats();
      this.loadOverallStats();

    } catch (error) {
      console.error('加载管理员信息失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 加载今日数据统计
   */
  async loadTodayStats() {
    try {
      const todayStats = await dashboardAPI.getTodayStats();
      this.setData({
        todayStats: {
          newUsers: todayStats.newUsers || 0,
          userChange: todayStats.userChange || 0,
          newOrders: todayStats.newOrders || 0,
          orderChange: todayStats.orderChange || 0,
          newMerchants: todayStats.newMerchants || 0,
          merchantChange: todayStats.merchantChange || 0,
          revenue: todayStats.revenue || '0.00',
          revenueChange: todayStats.revenueChange || 0
        }
      });
    } catch (error) {
      console.error('加载今日统计失败:', error);
      // 使用默认数据
      this.setData({
        todayStats: {
          newUsers: 0,
          userChange: 0,
          newOrders: 0,
          orderChange: 0,
          newMerchants: 0,
          merchantChange: 0,
          revenue: '0.00',
          revenueChange: 0
        }
      });
    }
  },

  /**
   * 加载待处理事项统计
   */
  async loadPendingStats() {
    try {
      const pendingStats = await dashboardAPI.getPendingStats();
      this.setData({
        pendingStats: {
          merchants: pendingStats.merchants || 0,
          complaints: pendingStats.complaints || 0,
          abnormalOrders: pendingStats.abnormalOrders || 0
        }
      });
    } catch (error) {
      console.error('加载待处理事项失败:', error);
      // 使用默认数据
      this.setData({
        pendingStats: {
          merchants: 0,
          complaints: 0,
          abnormalOrders: 0
        }
      });
    }
  },

  /**
   * 加载总体统计
   */
  async loadOverallStats() {
    try {
      const stats = await dashboardAPI.getDashboardOverview();
      this.setData({
        stats: {
          totalUsers: stats.totalUsers || 0,
          totalMerchants: stats.totalMerchants || 0,
          totalOrders: stats.totalOrders || 0
        }
      });
    } catch (error) {
      console.error('加载总体统计失败:', error);
      // 使用默认数据
      this.setData({
        stats: {
          totalUsers: 0,
          totalMerchants: 0,
          totalOrders: 0
        }
      });
    }
  },

  /**
   * 获取角色文本
   */
  getRoleText(role) {
    const roleMap = {
      'SUPER_ADMIN': '超级管理员',
      'ADMIN': '系统管理员',
      'OPERATOR': '运营专员'
    };
    return roleMap[role] || '管理员';
  },

  /**
   * 页面导航
   */
  onNavigate(e) {
    const url = e.currentTarget.dataset.url;
    if (url) {
      wx.navigateTo({
        url: url,
        fail: () => {
          wx.showToast({
            title: '页面开发中',
            icon: 'none'
          });
        }
      });
    }
  },

  /**
   * 查看用户
   */
  onViewUsers() {
    wx.navigateTo({
      url: '/pages/users/users'
    });
  },

  /**
   * 查看订单
   */
  onViewOrders() {
    wx.navigateTo({
      url: '/pages/orders/orders'
    });
  },

  /**
   * 查看商家
   */
  onViewMerchants() {
    wx.navigateTo({
      url: '/pages/merchants/merchants'
    });
  },

  /**
   * 查看收入
   */
  onViewRevenue() {
    wx.navigateTo({
      url: '/pages/financial/financial'
    });
  },

  /**
   * 查看所有待处理事项
   */
  onViewAllPending() {
    wx.navigateTo({
      url: '/pages/pending/pending'
    });
  },

  /**
   * 查看操作日志
   */
  onViewLogs() {
    wx.navigateTo({
      url: '/pages/logs/logs'
    });
  },

  /**
   * 数据备份
   */
  async onBackupData() {
    wx.showModal({
      title: '数据备份',
      content: '确定要进行数据备份吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({ title: '备份中...' });

            await adminAPI.backupData();

            wx.hideLoading();
            wx.showToast({
              title: '备份成功',
              icon: 'success'
            });
          } catch (error) {
            wx.hideLoading();
            wx.showToast({
              title: error.message || '备份失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 退出登录
   */
  onLogout() {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储
          wx.clearStorageSync();

          // 跳转到登录页
          wx.reLaunch({
            url: '/pages/login/login'
          });

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  }
})