<!-- 管理端个人中心页面 -->
<view class="container">
  <!-- 管理员信息头部 -->
  <view class="admin-header">
    <view class="admin-info">
      <image src="{{ adminInfo.avatar || '/images/default-admin.png' }}" class="admin-avatar"></image>
      <view class="admin-details">
        <text class="admin-name">{{ adminInfo.realName || '管理员' }}</text>
        <text class="admin-role">{{ adminInfo.roleText || '系统管理员' }}</text>
        <text class="admin-department">{{ adminInfo.department || '运营部' }}</text>
      </view>
      <view class="admin-status">
        <text class="status-badge {{ adminInfo.status }}">{{ adminInfo.statusText || '在线' }}</text>
      </view>
    </view>
  </view>

  <!-- 数据概览 -->
  <view class="overview-section">
    <view class="overview-title">今日数据概览</view>
    <view class="overview-grid">
      <view class="overview-item" bindtap="onViewUsers">
        <text class="overview-number">{{ todayStats.newUsers || 0 }}</text>
        <text class="overview-label">新增用户</text>
        <text class="overview-change {{ todayStats.userChange >= 0 ? 'positive' : 'negative' }}">
          {{ todayStats.userChange >= 0 ? '+' : '' }}{{ todayStats.userChange || 0 }}%
        </text>
      </view>
      <view class="overview-item" bindtap="onViewOrders">
        <text class="overview-number">{{ todayStats.newOrders || 0 }}</text>
        <text class="overview-label">新增订单</text>
        <text class="overview-change {{ todayStats.orderChange >= 0 ? 'positive' : 'negative' }}">
          {{ todayStats.orderChange >= 0 ? '+' : '' }}{{ todayStats.orderChange || 0 }}%
        </text>
      </view>
      <view class="overview-item" bindtap="onViewMerchants">
        <text class="overview-number">{{ todayStats.newMerchants || 0 }}</text>
        <text class="overview-label">新增商家</text>
        <text class="overview-change {{ todayStats.merchantChange >= 0 ? 'positive' : 'negative' }}">
          {{ todayStats.merchantChange >= 0 ? '+' : '' }}{{ todayStats.merchantChange || 0 }}%
        </text>
      </view>
      <view class="overview-item" bindtap="onViewRevenue">
        <text class="overview-number">¥{{ todayStats.revenue || '0.00' }}</text>
        <text class="overview-label">今日收入</text>
        <text class="overview-change {{ todayStats.revenueChange >= 0 ? 'positive' : 'negative' }}">
          {{ todayStats.revenueChange >= 0 ? '+' : '' }}{{ todayStats.revenueChange || 0 }}%
        </text>
      </view>
    </view>
  </view>

  <!-- 待处理事项 -->
  <view class="pending-section">
    <view class="section-header">
      <text class="section-title">待处理事项</text>
      <text class="view-all" bindtap="onViewAllPending">查看全部</text>
    </view>
    <view class="pending-list">
      <view class="pending-item" bindtap="onNavigate" data-url="/pages/merchants/merchants?status=pending">
        <view class="pending-icon merchant">
          <image src="/images/icons/merchant-pending.png"></image>
        </view>
        <view class="pending-content">
          <text class="pending-title">待审核商家</text>
          <text class="pending-desc">{{ pendingStats.merchants || 0 }}个商家等待审核</text>
        </view>
        <view class="pending-count">{{ pendingStats.merchants || 0 }}</view>
      </view>
      <view class="pending-item" bindtap="onNavigate" data-url="/pages/complaints/complaints?status=pending">
        <view class="pending-icon complaint">
          <image src="/images/icons/complaint-pending.png"></image>
        </view>
        <view class="pending-content">
          <text class="pending-title">待处理投诉</text>
          <text class="pending-desc">{{ pendingStats.complaints || 0 }}个投诉需要处理</text>
        </view>
        <view class="pending-count">{{ pendingStats.complaints || 0 }}</view>
      </view>
      <view class="pending-item" bindtap="onNavigate" data-url="/pages/orders/orders?status=abnormal">
        <view class="pending-icon order">
          <image src="/images/icons/order-abnormal.png"></image>
        </view>
        <view class="pending-content">
          <text class="pending-title">异常订单</text>
          <text class="pending-desc">{{ pendingStats.abnormalOrders || 0 }}个订单状态异常</text>
        </view>
        <view class="pending-count">{{ pendingStats.abnormalOrders || 0 }}</view>
      </view>
    </view>
  </view>

  <!-- 管理功能菜单 -->
  <view class="menu-section">
    <!-- 用户管理 -->
    <view class="menu-group">
      <view class="group-title">用户管理</view>
      <view class="menu-item" bindtap="onNavigate" data-url="/pages/users/users">
        <view class="menu-icon">
          <image src="/images/icons/users.png"></image>
        </view>
        <text class="menu-text">用户管理</text>
        <view class="menu-badge" wx:if="{{ stats.totalUsers }}">{{ stats.totalUsers }}</view>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="onNavigate" data-url="/pages/merchants/merchants">
        <view class="menu-icon">
          <image src="/images/icons/merchants.png"></image>
        </view>
        <text class="menu-text">商家管理</text>
        <view class="menu-badge" wx:if="{{ stats.totalMerchants }}">{{ stats.totalMerchants }}</view>
        <view class="menu-arrow">></view>
      </view>
    </view>

    <!-- 订单管理 -->
    <view class="menu-group">
      <view class="group-title">订单管理</view>
      <view class="menu-item" bindtap="onNavigate" data-url="/pages/orders/orders">
        <view class="menu-icon">
          <image src="/images/icons/orders.png"></image>
        </view>
        <text class="menu-text">订单监控</text>
        <view class="menu-badge" wx:if="{{ stats.totalOrders }}">{{ stats.totalOrders }}</view>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="onNavigate" data-url="/pages/complaints/complaints">
        <view class="menu-icon">
          <image src="/images/icons/complaints.png"></image>
        </view>
        <text class="menu-text">投诉处理</text>
        <view class="menu-badge warning" wx:if="{{ pendingStats.complaints }}">{{ pendingStats.complaints }}</view>
        <view class="menu-arrow">></view>
      </view>
    </view>

    <!-- 财务管理 -->
    <view class="menu-group">
      <view class="group-title">财务管理</view>
      <view class="menu-item" bindtap="onNavigate" data-url="/pages/financial/financial">
        <view class="menu-icon">
          <image src="/images/icons/financial.png"></image>
        </view>
        <text class="menu-text">财务管理</text>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="onNavigate" data-url="/pages/statistics/statistics">
        <view class="menu-icon">
          <image src="/images/icons/statistics.png"></image>
        </view>
        <text class="menu-text">数据统计</text>
        <view class="menu-arrow">></view>
      </view>
    </view>

    <!-- 系统设置 -->
    <view class="menu-group">
      <view class="group-title">系统设置</view>
      <view class="menu-item" bindtap="onNavigate" data-url="/pages/settings/settings">
        <view class="menu-icon">
          <image src="/images/icons/settings.png"></image>
        </view>
        <text class="menu-text">系统设置</text>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="onViewLogs">
        <view class="menu-icon">
          <image src="/images/icons/logs.png"></image>
        </view>
        <text class="menu-text">操作日志</text>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="onBackupData">
        <view class="menu-icon">
          <image src="/images/icons/backup.png"></image>
        </view>
        <text class="menu-text">数据备份</text>
        <view class="menu-arrow">></view>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section">
    <button class="logout-btn" bindtap="onLogout">退出登录</button>
  </view>
</view>