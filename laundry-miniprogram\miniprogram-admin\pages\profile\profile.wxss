/* 管理端个人中心样式 */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 管理员信息头部 */
.admin-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx 30rpx;
  color: white;
}

.admin-info {
  display: flex;
  align-items: center;
  position: relative;
}

.admin-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  margin-right: 30rpx;
}

.admin-details {
  flex: 1;
}

.admin-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.admin-role {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 8rpx;
}

.admin-department {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

.admin-status {
  position: absolute;
  top: 0;
  right: 0;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  background: rgba(76, 175, 80, 0.8);
}

.status-badge.INACTIVE {
  background: rgba(158, 158, 158, 0.8);
}

/* 数据概览 */
.overview-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.overview-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.overview-item {
  text-align: center;
  padding: 30rpx 20rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1rpx solid #dee2e6;
}

.overview-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 8rpx;
}

.overview-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.overview-change {
  font-size: 22rpx;
  font-weight: bold;
}

.overview-change.positive {
  color: #28a745;
}

.overview-change.negative {
  color: #dc3545;
}

/* 待处理事项 */
.pending-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.view-all {
  font-size: 26rpx;
  color: #667eea;
}

.pending-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.pending-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 6rpx solid #667eea;
}

.pending-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pending-icon.merchant {
  background: rgba(255, 193, 7, 0.1);
}

.pending-icon.complaint {
  background: rgba(220, 53, 69, 0.1);
}

.pending-icon.order {
  background: rgba(255, 107, 53, 0.1);
}

.pending-icon image {
  width: 32rpx;
  height: 32rpx;
}

.pending-content {
  flex: 1;
}

.pending-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 6rpx;
}

.pending-desc {
  font-size: 24rpx;
  color: #666;
}

.pending-count {
  font-size: 32rpx;
  font-weight: bold;
  color: #dc3545;
  min-width: 60rpx;
  text-align: center;
}

/* 功能菜单 */
.menu-section {
  margin: 20rpx;
}

.menu-group {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.group-title {
  padding: 30rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  background: #fafafa;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
}

.menu-icon image {
  width: 100%;
  height: 100%;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.menu-badge {
  background: #667eea;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 32rpx;
  text-align: center;
  margin-right: 16rpx;
}

.menu-badge.warning {
  background: #dc3545;
}

.menu-arrow {
  font-size: 28rpx;
  color: #ccc;
}

/* 退出登录 */
.logout-section {
  margin: 40rpx 20rpx 60rpx;
}

.logout-btn {
  width: 100%;
  background: white;
  color: #dc3545;
  border: 2rpx solid #dc3545;
  border-radius: 50rpx;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.logout-btn:active {
  background: #dc3545;
  color: white;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .overview-grid {
    grid-template-columns: 1fr;
    gap: 15rpx;
  }
  
  .overview-item {
    padding: 20rpx 15rpx;
  }
  
  .overview-number {
    font-size: 32rpx;
  }
}
