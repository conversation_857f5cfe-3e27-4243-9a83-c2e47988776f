const app = getApp();
const { settingsAPI, systemAPI } = require('../../utils/api.js');

Page({
  data: {
    currentTab: 0,
    tabs: [
      { key: 'basic', name: '基础设置' },
      { key: 'payment', name: '支付设置' },
      { key: 'notification', name: '通知设置' },
      { key: 'security', name: '安全设置' }
    ],

    // 基础设置
    basicSettings: {
      platformName: '',
      platformLogo: '',
      contactPhone: '',
      contactEmail: '',
      businessHours: '',
      serviceAreas: [],
      platformDescription: ''
    },

    // 支付设置
    paymentSettings: {
      wechatPayEnabled: false,
      alipayEnabled: false,
      balancePayEnabled: true,
      platformCommissionRate: 0,
      withdrawMinAmount: 100,
      withdrawMaxAmount: 50000,
      withdrawFeeRate: 0.001
    },

    // 通知设置
    notificationSettings: {
      smsEnabled: false,
      emailEnabled: false,
      pushEnabled: true,
      orderNotification: true,
      paymentNotification: true,
      systemNotification: true
    },

    // 安全设置
    securitySettings: {
      loginTimeout: 7200,
      passwordComplexity: 'medium',
      twoFactorAuth: false,
      ipWhitelist: [],
      operationLog: true
    },

    loading: true,
    saving: false
  },

  onLoad() {
    this.loadSettings();
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
  },

  // Tab切换
  onTabChange(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentTab: index
    });
  },

  // 加载设置
  async loadSettings() {
    try {
      this.setData({ loading: true });

      const [basicSettings, paymentSettings, notificationSettings, securitySettings] = await Promise.all([
        settingsAPI.getBasicSettings(),
        settingsAPI.getPaymentSettings(),
        settingsAPI.getNotificationSettings(),
        settingsAPI.getSecuritySettings()
      ]);

      this.setData({
        basicSettings,
        paymentSettings,
        notificationSettings,
        securitySettings,
        loading: false
      });

    } catch (error) {
      console.error('加载设置失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    }
  },

  // 基础设置输入
  onBasicInput(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`basicSettings.${field}`]: value
    });
  },

  // 上传平台Logo
  onUploadLogo() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        try {
          wx.showLoading({
            title: '上传中...'
          });

          const logoUrl = await systemAPI.uploadFile(res.tempFilePaths[0]);

          this.setData({
            'basicSettings.platformLogo': logoUrl
          });

          wx.hideLoading();
          wx.showToast({
            title: '上传成功',
            icon: 'success'
          });

        } catch (error) {
          wx.hideLoading();
          wx.showToast({
            title: error.message || '上传失败',
            icon: 'none'
          });
        }
      }
    });
  },

  // 支付设置开关
  onPaymentSwitch(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`paymentSettings.${field}`]: value
    });
  },

  // 支付设置输入
  onPaymentInput(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`paymentSettings.${field}`]: value
    });
  },

  // 通知设置开关
  onNotificationSwitch(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`notificationSettings.${field}`]: value
    });
  },

  // 安全设置输入
  onSecurityInput(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`securitySettings.${field}`]: value
    });
  },

  // 安全设置开关
  onSecuritySwitch(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`securitySettings.${field}`]: value
    });
  },

  // 保存设置
  async onSaveSettings() {
    if (this.data.saving) return;

    try {
      this.setData({ saving: true });

      wx.showLoading({
        title: '保存中...'
      });

      const currentTab = this.data.tabs[this.data.currentTab];

      switch (currentTab.key) {
        case 'basic':
          await settingsAPI.updateBasicSettings(this.data.basicSettings);
          break;
        case 'payment':
          await settingsAPI.updatePaymentSettings(this.data.paymentSettings);
          break;
        case 'notification':
          await settingsAPI.updateNotificationSettings(this.data.notificationSettings);
          break;
        case 'security':
          await settingsAPI.updateSecuritySettings(this.data.securitySettings);
          break;
      }

      wx.hideLoading();
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '保存失败',
        icon: 'none'
      });
    } finally {
      this.setData({ saving: false });
    }
  },

  // 重置设置
  onResetSettings() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置当前设置吗？此操作不可恢复。',
      confirmColor: '#ff4d4f',
      success: (res) => {
        if (res.confirm) {
          this.loadSettings();
        }
      }
    });
  },

  // 系统维护
  onSystemMaintenance() {
    wx.navigateTo({
      url: '/pages/maintenance/maintenance'
    });
  },

  // 数据备份
  onDataBackup() {
    wx.navigateTo({
      url: '/pages/backup/backup'
    });
  },

  // 系统日志
  onSystemLog() {
    wx.navigateTo({
      url: '/pages/system-log/system-log'
    });
  }
});