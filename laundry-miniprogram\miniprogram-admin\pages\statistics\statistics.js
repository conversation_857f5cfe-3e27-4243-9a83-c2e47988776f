// 管理端数据统计页面
const app = getApp();
const { dashboardAPI } = require('../../utils/api.js');

Page({
  data: {
    // 概览数据
    overview: {
      totalUsers: 0,
      totalMerchants: 0,
      totalOrders: 0,
      totalRevenue: 0,
      userTrend: 0,
      merchantTrend: 0,
      orderTrend: 0,
      revenueTrend: 0
    },

    // 时间周期
    timePeriods: [
      { label: '今日', value: 'today' },
      { label: '本周', value: 'week' },
      { label: '本月', value: 'month' },
      { label: '本年', value: 'year' }
    ],
    currentPeriod: 'month',
    currentPeriodText: '本月',

    // 详细统计
    details: {
      newUsers: 0,
      activeUsers: 0,
      retentionRate: 0,
      newOrders: 0,
      completedOrders: 0,
      completionRate: 0,
      newMerchants: 0,
      activeMerchants: 0,
      approvalRate: 0,
      totalRevenue: 0,
      platformRevenue: 0,
      merchantRevenue: 0
    },

    // 图表数据
    chartData: {
      orderChart: null,
      userChart: null,
      merchantChart: null
    },

    // 加载状态
    loading: true
  },

  onLoad() {
    this.initPage();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadStatistics();
  },

  onReady() {
    // 页面渲染完成后初始化图表
    this.initCharts();
  },

  // 初始化页面
  async initPage() {
    try {
      this.setData({ loading: true });

      // 加载统计数据
      await this.loadStatistics();

      // 初始化图表
      setTimeout(() => {
        this.initCharts();
      }, 500);

    } catch (error) {
      console.error('页面初始化失败:', error);
      app.showError('页面加载失败，请重试');
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载统计数据
  async loadStatistics() {
    try {
      // 并行加载数据
      const [overviewData, detailsData] = await Promise.all([
        this.loadOverviewData(),
        this.loadDetailsData()
      ]);

      this.setData({
        overview: overviewData,
        details: detailsData
      });

      // 更新图表
      this.updateCharts();

    } catch (error) {
      console.error('加载统计数据失败:', error);
      app.showError('数据加载失败');
    }
  },

  // 加载概览数据
  async loadOverviewData() {
    try {
      const data = await dashboardAPI.getDashboardOverview(this.data.currentPeriod);
      return {
        totalUsers: data.totalUsers || 0,
        totalMerchants: data.totalMerchants || 0,
        totalOrders: data.totalOrders || 0,
        totalRevenue: data.totalRevenue || 0,
        userTrend: data.userTrend || 0,
        merchantTrend: data.merchantTrend || 0,
        orderTrend: data.orderTrend || 0,
        revenueTrend: data.revenueTrend || 0
      };
    } catch (error) {
      console.error('加载概览数据失败:', error);
      // 返回空数据
      return {
        totalUsers: 0,
        totalMerchants: 0,
        totalOrders: 0,
        totalRevenue: 0,
        userTrend: 0,
        merchantTrend: 0,
        orderTrend: 0,
        revenueTrend: 0
      };
    }
  },

  // 加载详细数据
  async loadDetailsData() {
    try {
      const data = await dashboardAPI.getDashboardStats(this.data.currentPeriod);
      return {
        newUsers: data.newUsers || 0,
        activeUsers: data.activeUsers || 0,
        retentionRate: data.retentionRate || 0,
        newOrders: data.newOrders || 0,
        completedOrders: data.completedOrders || 0,
        completionRate: data.completionRate || 0,
        newMerchants: data.newMerchants || 0,
        activeMerchants: data.activeMerchants || 0,
        approvalRate: data.approvalRate || 0,
        totalRevenue: data.totalRevenue || 0,
        platformRevenue: data.platformRevenue || 0,
        merchantRevenue: data.merchantRevenue || 0
      };
    } catch (error) {
      console.error('加载详细数据失败:', error);
      // 返回空数据
      return {
        newUsers: 0,
        activeUsers: 0,
        retentionRate: 0,
        newOrders: 0,
        completedOrders: 0,
        completionRate: 0,
        newMerchants: 0,
        activeMerchants: 0,
        approvalRate: 0,
        totalRevenue: 0,
        platformRevenue: 0,
        merchantRevenue: 0
      };
    }
  },

  // 时间周期切换
  onPeriodChange(e) {
    const period = e.currentTarget.dataset.period;
    if (period === this.data.currentPeriod) return;

    const periodText = this.data.timePeriods.find(p => p.value === period)?.label || '本月';

    this.setData({
      currentPeriod: period,
      currentPeriodText: periodText
    });

    // 重新加载数据
    this.loadStatistics();
  },

  // 初始化图表
  initCharts() {
    this.initOrderChart();
    this.initUserChart();
    this.initMerchantChart();
  },

  // 初始化订单趋势图
  initOrderChart() {
    const ctx = wx.createCanvasContext('orderChart', this);

    // 模拟数据
    const days = ['1日', '5日', '10日', '15日', '20日', '25日', '30日'];
    const orders = [120, 180, 150, 220, 280, 320, 350];
    const revenue = [12000, 18000, 15000, 22000, 28000, 32000, 35000];

    this.drawLineChart(ctx, {
      labels: days,
      datasets: [
        { data: orders, color: '#1890ff', label: '订单数量' },
        { data: revenue.map(v => v / 100), color: '#52c41a', label: '营收(百元)' }
      ]
    });
  },

  // 初始化用户增长图
  initUserChart() {
    const ctx = wx.createCanvasContext('userChart', this);

    // 模拟数据
    const days = ['1日', '5日', '10日', '15日', '20日', '25日', '30日'];
    const newUsers = [45, 62, 58, 78, 85, 92, 105];
    const activeUsers = [850, 920, 880, 1050, 1150, 1280, 1350];

    this.drawLineChart(ctx, {
      labels: days,
      datasets: [
        { data: newUsers, color: '#722ed1', label: '新增用户' },
        { data: activeUsers.map(v => v / 10), color: '#fa8c16', label: '活跃用户(十人)' }
      ]
    });
  },

  // 初始化商家分布图
  initMerchantChart() {
    const ctx = wx.createCanvasContext('merchantChart', this);

    // 模拟数据
    const data = [
      { label: '已认证', value: 420, color: '#52c41a' },
      { label: '待审核', value: 35, color: '#fa8c16' },
      { label: '已拒绝', value: 25, color: '#ff4d4f' },
      { label: '已暂停', value: 20, color: '#d9d9d9' }
    ];

    this.drawPieChart(ctx, data);
  },

  // 绘制折线图
  drawLineChart(ctx, config) {
    const { labels, datasets } = config;
    const width = 300;
    const height = 200;
    const padding = 40;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;

    // 清空画布
    ctx.clearRect(0, 0, width, height);

    // 绘制坐标轴
    ctx.setStrokeStyle('#e8e8e8');
    ctx.setLineWidth(1);

    // X轴
    ctx.beginPath();
    ctx.moveTo(padding, height - padding);
    ctx.lineTo(width - padding, height - padding);
    ctx.stroke();

    // Y轴
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, height - padding);
    ctx.stroke();

    // 绘制数据线
    datasets.forEach((dataset, index) => {
      const { data, color } = dataset;
      const maxValue = Math.max(...data);
      const stepX = chartWidth / (labels.length - 1);

      ctx.setStrokeStyle(color);
      ctx.setLineWidth(2);
      ctx.beginPath();

      data.forEach((value, i) => {
        const x = padding + i * stepX;
        const y = height - padding - (value / maxValue) * chartHeight;

        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });

      ctx.stroke();
    });

    // 绘制标签
    ctx.setFillStyle('#666');
    ctx.setFontSize(12);
    labels.forEach((label, i) => {
      const x = padding + i * (chartWidth / (labels.length - 1));
      ctx.fillText(label, x - 10, height - 10);
    });

    ctx.draw();
  },

  // 绘制饼图
  drawPieChart(ctx, data) {
    const width = 300;
    const height = 200;
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = 60;

    // 清空画布
    ctx.clearRect(0, 0, width, height);

    const total = data.reduce((sum, item) => sum + item.value, 0);
    let currentAngle = -Math.PI / 2;

    data.forEach((item, index) => {
      const angle = (item.value / total) * 2 * Math.PI;

      // 绘制扇形
      ctx.setFillStyle(item.color);
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + angle);
      ctx.closePath();
      ctx.fill();

      // 绘制标签
      const labelAngle = currentAngle + angle / 2;
      const labelX = centerX + Math.cos(labelAngle) * (radius + 20);
      const labelY = centerY + Math.sin(labelAngle) * (radius + 20);

      ctx.setFillStyle('#333');
      ctx.setFontSize(12);
      ctx.fillText(item.label, labelX - 15, labelY);

      currentAngle += angle;
    });

    ctx.draw();
  },

  // 更新图表
  updateCharts() {
    setTimeout(() => {
      this.initCharts();
    }, 100);
  },

  // 图表触摸事件
  onChartTouch(e) {
    // 处理图表交互
    console.log('图表触摸事件:', e);
  }
});