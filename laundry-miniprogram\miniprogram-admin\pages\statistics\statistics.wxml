<!-- 管理端数据统计页面 -->
<view class="container">
  <!-- 顶部概览卡片 -->
  <view class="overview-section">
    <view class="overview-grid">
      <view class="overview-card">
        <view class="card-icon user-icon">
          <text class="iconfont icon-user"></text>
        </view>
        <view class="card-content">
          <text class="card-number">{{ overview.totalUsers || 0 }}</text>
          <text class="card-label">总用户数</text>
          <text class="card-trend {{ overview.userTrend >= 0 ? 'up' : 'down' }}">
            {{ overview.userTrend >= 0 ? '+' : '' }}{{ overview.userTrend || 0 }}%
          </text>
        </view>
      </view>

      <view class="overview-card">
        <view class="card-icon merchant-icon">
          <text class="iconfont icon-shop"></text>
        </view>
        <view class="card-content">
          <text class="card-number">{{ overview.totalMerchants || 0 }}</text>
          <text class="card-label">总商家数</text>
          <text class="card-trend {{ overview.merchantTrend >= 0 ? 'up' : 'down' }}">
            {{ overview.merchantTrend >= 0 ? '+' : '' }}{{ overview.merchantTrend || 0 }}%
          </text>
        </view>
      </view>

      <view class="overview-card">
        <view class="card-icon order-icon">
          <text class="iconfont icon-order"></text>
        </view>
        <view class="card-content">
          <text class="card-number">{{ overview.totalOrders || 0 }}</text>
          <text class="card-label">总订单数</text>
          <text class="card-trend {{ overview.orderTrend >= 0 ? 'up' : 'down' }}">
            {{ overview.orderTrend >= 0 ? '+' : '' }}{{ overview.orderTrend || 0 }}%
          </text>
        </view>
      </view>

      <view class="overview-card">
        <view class="card-icon revenue-icon">
          <text class="iconfont icon-money"></text>
        </view>
        <view class="card-content">
          <text class="card-number">¥{{ overview.totalRevenue || 0 }}</text>
          <text class="card-label">总营收</text>
          <text class="card-trend {{ overview.revenueTrend >= 0 ? 'up' : 'down' }}">
            {{ overview.revenueTrend >= 0 ? '+' : '' }}{{ overview.revenueTrend || 0 }}%
          </text>
        </view>
      </view>
    </view>
  </view>

  <!-- 时间筛选 -->
  <view class="filter-section">
    <view class="filter-tabs">
      <view
        wx:for="{{ timePeriods }}"
        wx:key="value"
        class="filter-tab {{ currentPeriod === item.value ? 'active' : '' }}"
        bindtap="onPeriodChange"
        data-period="{{ item.value }}"
      >
        {{ item.label }}
      </view>
    </view>
  </view>

  <!-- 图表区域 -->
  <view class="charts-section">
    <!-- 订单趋势图 -->
    <view class="chart-card">
      <view class="chart-header">
        <text class="chart-title">订单趋势</text>
        <view class="chart-legend">
          <view class="legend-item">
            <view class="legend-dot order-dot"></view>
            <text class="legend-text">订单数量</text>
          </view>
          <view class="legend-item">
            <view class="legend-dot revenue-dot"></view>
            <text class="legend-text">营收金额</text>
          </view>
        </view>
      </view>
      <view class="chart-container">
        <canvas
          canvas-id="orderChart"
          class="chart-canvas"
          bindtouchstart="onChartTouch"
          bindtouchmove="onChartTouch"
          bindtouchend="onChartTouch"
        ></canvas>
      </view>
    </view>

    <!-- 用户增长图 -->
    <view class="chart-card">
      <view class="chart-header">
        <text class="chart-title">用户增长</text>
        <view class="chart-legend">
          <view class="legend-item">
            <view class="legend-dot user-dot"></view>
            <text class="legend-text">新增用户</text>
          </view>
          <view class="legend-item">
            <view class="legend-dot active-dot"></view>
            <text class="legend-text">活跃用户</text>
          </view>
        </view>
      </view>
      <view class="chart-container">
        <canvas
          canvas-id="userChart"
          class="chart-canvas"
          bindtouchstart="onChartTouch"
          bindtouchmove="onChartTouch"
          bindtouchend="onChartTouch"
        ></canvas>
      </view>
    </view>

    <!-- 商家分布图 -->
    <view class="chart-card">
      <view class="chart-header">
        <text class="chart-title">商家状态分布</text>
      </view>
      <view class="chart-container">
        <canvas
          canvas-id="merchantChart"
          class="chart-canvas"
          bindtouchstart="onChartTouch"
          bindtouchmove="onChartTouch"
          bindtouchend="onChartTouch"
        ></canvas>
      </view>
    </view>
  </view>

  <!-- 详细统计 -->
  <view class="details-section">
    <view class="details-header">
      <text class="details-title">详细统计</text>
      <text class="details-subtitle">{{ currentPeriodText }}数据</text>
    </view>

    <view class="details-grid">
      <!-- 用户统计 -->
      <view class="detail-card">
        <view class="detail-header">
          <text class="detail-title">用户统计</text>
          <text class="detail-icon">👥</text>
        </view>
        <view class="detail-list">
          <view class="detail-item">
            <text class="detail-label">新增用户</text>
            <text class="detail-value">{{ details.newUsers || 0 }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">活跃用户</text>
            <text class="detail-value">{{ details.activeUsers || 0 }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">留存率</text>
            <text class="detail-value">{{ details.retentionRate || 0 }}%</text>
          </view>
        </view>
      </view>

      <!-- 订单统计 -->
      <view class="detail-card">
        <view class="detail-header">
          <text class="detail-title">订单统计</text>
          <text class="detail-icon">📋</text>
        </view>
        <view class="detail-list">
          <view class="detail-item">
            <text class="detail-label">新增订单</text>
            <text class="detail-value">{{ details.newOrders || 0 }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">完成订单</text>
            <text class="detail-value">{{ details.completedOrders || 0 }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">完成率</text>
            <text class="detail-value">{{ details.completionRate || 0 }}%</text>
          </view>
        </view>
      </view>

      <!-- 商家统计 -->
      <view class="detail-card">
        <view class="detail-header">
          <text class="detail-title">商家统计</text>
          <text class="detail-icon">🏪</text>
        </view>
        <view class="detail-list">
          <view class="detail-item">
            <text class="detail-label">新增商家</text>
            <text class="detail-value">{{ details.newMerchants || 0 }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">活跃商家</text>
            <text class="detail-value">{{ details.activeMerchants || 0 }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">通过率</text>
            <text class="detail-value">{{ details.approvalRate || 0 }}%</text>
          </view>
        </view>
      </view>

      <!-- 财务统计 -->
      <view class="detail-card">
        <view class="detail-header">
          <text class="detail-title">财务统计</text>
          <text class="detail-icon">💰</text>
        </view>
        <view class="detail-list">
          <view class="detail-item">
            <text class="detail-label">总营收</text>
            <text class="detail-value">¥{{ details.totalRevenue || 0 }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">平台收入</text>
            <text class="detail-value">¥{{ details.platformRevenue || 0 }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">商家收入</text>
            <text class="detail-value">¥{{ details.merchantRevenue || 0 }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{ loading }}" class="loading-overlay">
    <view class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">数据加载中...</text>
    </view>
  </view>
</view>