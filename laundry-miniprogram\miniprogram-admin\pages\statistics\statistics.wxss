/* 管理端数据统计页面样式 */
.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 概览区域 */
.overview-section {
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.overview-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.card-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  font-size: 36rpx;
  color: #ffffff;
}

.user-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.merchant-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.order-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.revenue-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.card-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.card-trend {
  font-size: 22rpx;
  font-weight: bold;
}

.card-trend.up {
  color: #52c41a;
}

.card-trend.down {
  color: #ff4d4f;
}

/* 筛选区域 */
.filter-section {
  background: #ffffff;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
}

.filter-tabs {
  display: flex;
  padding: 20rpx 0;
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  font-size: 28rpx;
  color: #666;
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s ease;
}

.filter-tab.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
  font-weight: bold;
}

/* 图表区域 */
.charts-section {
  padding: 0 30rpx;
  margin-bottom: 20rpx;
}

.chart-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.chart-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.chart-legend {
  display: flex;
  gap: 20rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.legend-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
}

.order-dot {
  background: #1890ff;
}

.revenue-dot {
  background: #52c41a;
}

.user-dot {
  background: #722ed1;
}

.active-dot {
  background: #fa8c16;
}

.legend-text {
  font-size: 24rpx;
  color: #666;
}

.chart-container {
  width: 100%;
  height: 400rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-canvas {
  width: 600rpx;
  height: 400rpx;
}

/* 详细统计区域 */
.details-section {
  padding: 0 30rpx;
}

.details-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.details-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.details-subtitle {
  font-size: 26rpx;
  color: #666;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.detail-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.detail-icon {
  font-size: 32rpx;
}

.detail-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
}

.detail-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .overview-grid,
  .details-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-canvas {
    width: 500rpx;
    height: 300rpx;
  }
  
  .chart-container {
    height: 300rpx;
  }
}

/* 图标字体 */
.iconfont {
  font-family: "iconfont";
}

.icon-user::before {
  content: "👤";
}

.icon-shop::before {
  content: "🏪";
}

.icon-order::before {
  content: "📋";
}

.icon-money::before {
  content: "💰";
}
