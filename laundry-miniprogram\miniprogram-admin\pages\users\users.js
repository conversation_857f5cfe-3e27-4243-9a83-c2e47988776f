// 管理端用户管理页面
const app = getApp();

Page({
  data: {
    searchKeyword: '',
    showFilter: false,
    statusFilter: 'all',
    vipFilter: 'all',
    users: [],
    stats: {
      total: 0,
      active: 0,
      newToday: 0,
      vip: 0
    },
    statusOptions: [
      { label: '全部', value: 'all' },
      { label: '正常', value: 'ACTIVE' },
      { label: '禁用', value: 'DISABLED' },
      { label: '冻结', value: 'FROZEN' }
    ],
    vipOptions: [
      { label: '全部', value: 'all' },
      { label: '普通', value: 'NORMAL' },
      { label: '银卡', value: 'SILVER' },
      { label: '金卡', value: 'GOLD' },
      { label: '白金', value: 'PLATINUM' }
    ],
    refreshing: false,
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10
  },

  onLoad() {
    this.loadUsers();
    this.loadStats();
  },

  onPullDownRefresh() {
    this.onRefresh();
  },

  // 下拉刷新
  onRefresh() {
    this.setData({
      refreshing: true,
      page: 1,
      hasMore: true
    });

    this.loadUsers().finally(() => {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onLoadMore() {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }

    this.setData({
      page: this.data.page + 1
    });

    this.loadUsers(true);
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 搜索确认
  onSearch() {
    this.setData({
      page: 1,
      hasMore: true
    });
    this.loadUsers();
  },

  // 切换筛选面板
  toggleFilter() {
    this.setData({
      showFilter: !this.data.showFilter
    });
  },

  // 状态筛选
  onStatusFilter(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      statusFilter: value
    });
  },

  // VIP筛选
  onVipFilter(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      vipFilter: value
    });
  },

  // 重置筛选
  onResetFilter() {
    this.setData({
      statusFilter: 'all',
      vipFilter: 'all'
    });
  },

  // 应用筛选
  onApplyFilter() {
    this.setData({
      showFilter: false,
      page: 1,
      hasMore: true
    });
    this.loadUsers();
  },

  // 加载用户列表
  async loadUsers(append = false) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const { page, pageSize, searchKeyword, statusFilter, vipFilter } = this.data;

      // 模拟API调用
      const response = await this.getMockUsers(page, pageSize, searchKeyword, statusFilter, vipFilter);

      const newUsers = append ?
        [...this.data.users, ...response.data] :
        response.data;

      this.setData({
        users: newUsers,
        hasMore: response.hasMore,
        loading: false
      });

    } catch (error) {
      console.error('加载用户列表失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  // 加载统计数据
  async loadStats() {
    try {
      // 模拟API调用
      const stats = await this.getMockStats();
      this.setData({ stats });
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  },

  // 模拟获取用户数据
  getMockUsers(page, pageSize, keyword, statusFilter, vipFilter) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const allUsers = [
          {
            id: 1,
            nickname: '张先生',
            phone: '138****0001',
            avatar: '/images/avatar1.png',
            status: 'ACTIVE',
            statusText: '正常',
            vipLevel: 'GOLD',
            vipLevelText: '金卡',
            registerTime: '2024-01-15',
            orderCount: 25,
            totalAmount: 1580.00,
            points: 1200,
            balance: 50.00,
            lastActivity: '下单洗衣服务',
            lastActivityTime: '2小时前'
          },
          {
            id: 2,
            nickname: '李女士',
            phone: '139****0002',
            avatar: '/images/avatar2.png',
            status: 'ACTIVE',
            statusText: '正常',
            vipLevel: 'SILVER',
            vipLevelText: '银卡',
            registerTime: '2024-02-20',
            orderCount: 12,
            totalAmount: 680.00,
            points: 800,
            balance: 30.00,
            lastActivity: '评价订单',
            lastActivityTime: '1天前'
          },
          {
            id: 3,
            nickname: '王先生',
            phone: '137****0003',
            avatar: '/images/avatar3.png',
            status: 'DISABLED',
            statusText: '禁用',
            vipLevel: 'NORMAL',
            vipLevelText: '普通',
            registerTime: '2024-03-10',
            orderCount: 3,
            totalAmount: 150.00,
            points: 100,
            balance: 0.00,
            lastActivity: '登录系统',
            lastActivityTime: '1周前'
          }
        ];

        // 根据筛选条件过滤
        let filteredUsers = allUsers;

        if (statusFilter !== 'all') {
          filteredUsers = filteredUsers.filter(user => user.status === statusFilter);
        }

        if (vipFilter !== 'all') {
          filteredUsers = filteredUsers.filter(user => user.vipLevel === vipFilter);
        }

        // 根据关键词搜索
        if (keyword) {
          filteredUsers = filteredUsers.filter(user =>
            user.nickname.includes(keyword) || user.phone.includes(keyword)
          );
        }

        // 分页
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        const data = filteredUsers.slice(start, end);
        const hasMore = end < filteredUsers.length;

        resolve({ data, hasMore });
      }, 500);
    });
  },

  // 模拟获取统计数据
  getMockStats() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          total: 1256,
          active: 1180,
          newToday: 23,
          vip: 156
        });
      }, 300);
    });
  },

  // 查看用户详情
  onUserDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/user-detail/user-detail?id=${id}`
    });
  },

  // 查看用户
  onViewUser(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/user-detail/user-detail?id=${id}`
    });
  },

  // 切换用户状态
  onToggleUserStatus(e) {
    const { id, status } = e.currentTarget.dataset;
    const newStatus = status === 'ACTIVE' ? 'DISABLED' : 'ACTIVE';
    const actionText = newStatus === 'ACTIVE' ? '启用' : '禁用';

    wx.showModal({
      title: '确认操作',
      content: `确定要${actionText}这个用户吗？`,
      success: (res) => {
        if (res.confirm) {
          this.updateUserStatus(id, newStatus);
        }
      }
    });
  },

  // 更新用户状态
  async updateUserStatus(id, status) {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));

      // 更新本地数据
      const users = this.data.users.map(user => {
        if (user.id === id) {
          return {
            ...user,
            status,
            statusText: status === 'ACTIVE' ? '正常' : '禁用'
          };
        }
        return user;
      });

      this.setData({ users });

      wx.showToast({
        title: status === 'ACTIVE' ? '启用成功' : '禁用成功',
        icon: 'success'
      });

      // 刷新统计数据
      this.loadStats();

    } catch (error) {
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  // 发送消息
  onSendMessage(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/send-message/send-message?userId=${id}`
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  }
});