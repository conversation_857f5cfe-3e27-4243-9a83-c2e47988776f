// 管理端用户管理页面
const app = getApp();
const { userAPI } = require('../../utils/api.js');

Page({
  data: {
    searchKeyword: '',
    showFilter: false,
    statusFilter: 'all',
    vipFilter: 'all',
    users: [],
    stats: {
      total: 0,
      active: 0,
      newToday: 0,
      vip: 0
    },
    statusOptions: [
      { label: '全部', value: 'all' },
      { label: '正常', value: 'ACTIVE' },
      { label: '禁用', value: 'DISABLED' },
      { label: '冻结', value: 'FROZEN' }
    ],
    vipOptions: [
      { label: '全部', value: 'all' },
      { label: '普通', value: 'NORMAL' },
      { label: '银卡', value: 'SILVER' },
      { label: '金卡', value: 'GOLD' },
      { label: '白金', value: 'PLATINUM' }
    ],
    refreshing: false,
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10
  },

  onLoad() {
    this.loadUsers();
    this.loadStats();
  },

  onPullDownRefresh() {
    this.onRefresh();
  },

  // 下拉刷新
  onRefresh() {
    this.setData({
      refreshing: true,
      page: 1,
      hasMore: true
    });

    this.loadUsers().finally(() => {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onLoadMore() {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }

    this.setData({
      page: this.data.page + 1
    });

    this.loadUsers(true);
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 搜索确认
  onSearch() {
    this.setData({
      page: 1,
      hasMore: true
    });
    this.loadUsers();
  },

  // 切换筛选面板
  toggleFilter() {
    this.setData({
      showFilter: !this.data.showFilter
    });
  },

  // 状态筛选
  onStatusFilter(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      statusFilter: value
    });
  },

  // VIP筛选
  onVipFilter(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      vipFilter: value
    });
  },

  // 重置筛选
  onResetFilter() {
    this.setData({
      statusFilter: 'all',
      vipFilter: 'all'
    });
  },

  // 应用筛选
  onApplyFilter() {
    this.setData({
      showFilter: false,
      page: 1,
      hasMore: true
    });
    this.loadUsers();
  },

  // 加载用户列表
  async loadUsers(append = false) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const { page, pageSize, searchKeyword, statusFilter, vipFilter } = this.data;

      const params = {
        page,
        pageSize,
        keyword: searchKeyword,
        status: statusFilter === 'all' ? undefined : statusFilter,
        vipLevel: vipFilter === 'all' ? undefined : vipFilter
      };

      const response = await userAPI.getUsers(params);
      const users = response.list || response || [];
      const newUsers = append ? [...this.data.users, ...users] : users;

      this.setData({
        users: newUsers,
        hasMore: users.length === pageSize,
        loading: false
      });

    } catch (error) {
      console.error('加载用户列表失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  // 加载统计数据
  async loadStats() {
    try {
      const stats = await userAPI.getUserStats();
      this.setData({ stats });
    } catch (error) {
      console.error('加载统计数据失败:', error);
      // 使用默认统计数据
      this.setData({
        stats: {
          total: 0,
          active: 0,
          newToday: 0,
          vip: 0
        }
      });
    }
  },



  // 查看用户详情
  onUserDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/user-detail/user-detail?id=${id}`
    });
  },

  // 查看用户
  onViewUser(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/user-detail/user-detail?id=${id}`
    });
  },

  // 切换用户状态
  onToggleUserStatus(e) {
    const { id, status } = e.currentTarget.dataset;
    const newStatus = status === 'ACTIVE' ? 'DISABLED' : 'ACTIVE';
    const actionText = newStatus === 'ACTIVE' ? '启用' : '禁用';

    wx.showModal({
      title: '确认操作',
      content: `确定要${actionText}这个用户吗？`,
      success: (res) => {
        if (res.confirm) {
          this.updateUserStatus(id, newStatus);
        }
      }
    });
  },

  // 更新用户状态
  async updateUserStatus(id, status) {
    try {
      wx.showLoading({
        title: status === 'ACTIVE' ? '启用中...' : '禁用中...'
      });

      await userAPI.updateUserStatus(id, status);

      // 更新本地数据
      const users = this.data.users.map(user => {
        if (user.id === id) {
          return {
            ...user,
            status,
            statusText: status === 'ACTIVE' ? '正常' : '禁用'
          };
        }
        return user;
      });

      this.setData({ users });

      wx.hideLoading();
      wx.showToast({
        title: status === 'ACTIVE' ? '启用成功' : '禁用成功',
        icon: 'success'
      });

      // 刷新统计数据
      this.loadStats();

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      });
    }
  },

  // 发送消息
  onSendMessage(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/send-message/send-message?userId=${id}`
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  }
});