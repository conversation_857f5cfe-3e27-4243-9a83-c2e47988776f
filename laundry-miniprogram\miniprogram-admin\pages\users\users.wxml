<!--管理端用户管理页面-->
<view class="container">
  <!-- 搜索和筛选 -->
  <view class="search-filter">
    <view class="search-box">
      <input
        class="search-input"
        placeholder="搜索用户昵称/手机号"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
      />
      <image class="search-icon" src="/images/search-icon.png"></image>
    </view>
    <button class="filter-btn" bindtap="toggleFilter">
      <image class="filter-icon" src="/images/filter-icon.png"></image>
      <text>筛选</text>
    </button>
  </view>

  <!-- 筛选面板 -->
  <view class="filter-panel" wx:if="{{showFilter}}">
    <view class="filter-section">
      <text class="filter-title">用户状态</text>
      <view class="filter-tags">
        <view
          class="filter-tag {{statusFilter === item.value ? 'active' : ''}}"
          wx:for="{{statusOptions}}"
          wx:key="value"
          bindtap="onStatusFilter"
          data-value="{{item.value}}"
        >
          {{item.label}}
        </view>
      </view>
    </view>

    <view class="filter-section">
      <text class="filter-title">VIP等级</text>
      <view class="filter-tags">
        <view
          class="filter-tag {{vipFilter === item.value ? 'active' : ''}}"
          wx:for="{{vipOptions}}"
          wx:key="value"
          bindtap="onVipFilter"
          data-value="{{item.value}}"
        >
          {{item.label}}
        </view>
      </view>
    </view>

    <view class="filter-actions">
      <button class="reset-btn" bindtap="onResetFilter">重置</button>
      <button class="apply-btn" bindtap="onApplyFilter">应用</button>
    </view>
  </view>

  <!-- 用户统计 -->
  <view class="user-stats">
    <view class="stat-item">
      <text class="stat-number">{{stats.total}}</text>
      <text class="stat-label">总用户</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{stats.active}}</text>
      <text class="stat-label">活跃用户</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{stats.newToday}}</text>
      <text class="stat-label">今日新增</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{stats.vip}}</text>
      <text class="stat-label">VIP用户</text>
    </view>
  </view>

  <!-- 用户列表 -->
  <scroll-view
    class="user-list"
    scroll-y
    refresher-enabled
    refresher-triggered="{{refreshing}}"
    bindrefresherrefresh="onRefresh"
    bindscrolltolower="onLoadMore"
  >
    <view class="user-item" wx:for="{{users}}" wx:key="id" bindtap="onUserDetail" data-id="{{item.id}}">
      <!-- 用户头像和基本信息 -->
      <view class="user-header">
        <image class="user-avatar" src="{{item.avatar}}" mode="aspectFill"></image>
        <view class="user-info">
          <view class="user-name-row">
            <text class="user-nickname">{{item.nickname}}</text>
            <view class="user-vip" wx:if="{{item.vipLevel !== 'NORMAL'}}">
              <text class="vip-text">{{item.vipLevelText}}</text>
            </view>
          </view>
          <text class="user-phone">{{item.phone}}</text>
          <text class="user-register-time">注册时间：{{item.registerTime}}</text>
        </view>
        <view class="user-status {{item.status}}">
          <text class="status-text">{{item.statusText}}</text>
        </view>
      </view>

      <!-- 用户数据 -->
      <view class="user-data">
        <view class="data-item">
          <text class="data-label">订单数</text>
          <text class="data-value">{{item.orderCount}}</text>
        </view>
        <view class="data-item">
          <text class="data-label">消费金额</text>
          <text class="data-value">¥{{item.totalAmount}}</text>
        </view>
        <view class="data-item">
          <text class="data-label">积分</text>
          <text class="data-value">{{item.points}}</text>
        </view>
        <view class="data-item">
          <text class="data-label">余额</text>
          <text class="data-value">¥{{item.balance}}</text>
        </view>
      </view>

      <!-- 最近活动 -->
      <view class="user-activity" wx:if="{{item.lastActivity}}">
        <text class="activity-label">最近活动：</text>
        <text class="activity-text">{{item.lastActivity}}</text>
        <text class="activity-time">{{item.lastActivityTime}}</text>
      </view>

      <!-- 操作按钮 -->
      <view class="user-actions" catchtap="stopPropagation">
        <button class="action-btn view" bindtap="onViewUser" data-id="{{item.id}}">
          查看详情
        </button>
        <button
          class="action-btn {{item.status === 'ACTIVE' ? 'disable' : 'enable'}}"
          bindtap="onToggleUserStatus"
          data-id="{{item.id}}"
          data-status="{{item.status}}"
        >
          {{item.status === 'ACTIVE' ? '禁用' : '启用'}}
        </button>
        <button class="action-btn message" bindtap="onSendMessage" data-id="{{item.id}}">
          发消息
        </button>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore}}">
      <text class="load-text">{{loading ? '加载中...' : '上拉加载更多'}}</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!users.length && !loading}}">
      <image class="empty-image" src="/images/empty-users.png"></image>
      <text class="empty-text">暂无用户数据</text>
    </view>
  </scroll-view>
</view>