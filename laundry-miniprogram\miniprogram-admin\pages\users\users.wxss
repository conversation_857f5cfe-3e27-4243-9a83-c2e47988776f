/* 管理端用户管理页面样式 */
.container {
  background: #f5f5f5;
  min-height: 100vh;
}

/* 搜索和筛选 */
.search-filter {
  background: #ffffff;
  padding: 20rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-box {
  flex: 1;
  position: relative;
  background: #f8f9fa;
  border-radius: 40rpx;
  padding: 0 40rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  height: 100%;
}

.search-input::placeholder {
  color: #999;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 20rpx;
}

.filter-btn {
  background: #1890ff;
  color: #ffffff;
  padding: 20rpx 30rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  border: none;
}

.filter-icon {
  width: 28rpx;
  height: 28rpx;
}

/* 筛选面板 */
.filter-panel {
  background: #ffffff;
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filter-section {
  margin-bottom: 40rpx;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.filter-tag {
  padding: 16rpx 32rpx;
  background: #f8f9fa;
  color: #666;
  border-radius: 40rpx;
  font-size: 26rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.filter-tag.active {
  background: #1890ff;
  color: #ffffff;
  border-color: #1890ff;
}

.filter-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.reset-btn,
.apply-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.reset-btn {
  background: #f8f9fa;
  color: #666;
}

.apply-btn {
  background: #1890ff;
  color: #ffffff;
}

/* 用户统计 */
.user-stats {
  background: #ffffff;
  padding: 30rpx;
  margin: 20rpx;
  border-radius: 20rpx;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 用户列表 */
.user-list {
  height: calc(100vh - 300rpx);
  padding: 20rpx;
}

.user-item {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.user-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 用户头部 */
.user-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.user-info {
  flex: 1;
  margin-right: 20rpx;
}

.user-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.user-nickname {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 16rpx;
}

.user-vip {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #333;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: bold;
}

.user-phone {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.user-register-time {
  display: block;
  font-size: 24rpx;
  color: #999;
}

.user-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  flex-shrink: 0;
}

.user-status.ACTIVE {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.user-status.DISABLED {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.user-status.FROZEN {
  background: rgba(250, 140, 22, 0.1);
  color: #fa8c16;
}

/* 用户数据 */
.user-data {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.data-item {
  text-align: center;
  flex: 1;
}

.data-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.data-value {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

/* 用户活动 */
.user-activity {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 16rpx 20rpx;
  background: #f0f8ff;
  border-radius: 12rpx;
  border-left: 4rpx solid #1890ff;
}

.activity-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 12rpx;
}

.activity-text {
  font-size: 26rpx;
  color: #333;
  margin-right: 12rpx;
}

.activity-time {
  font-size: 22rpx;
  color: #999;
  margin-left: auto;
}

/* 用户操作 */
.user-actions {
  display: flex;
  gap: 20rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #f5f5f5;
}

.action-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.view {
  background: #1890ff;
  color: #ffffff;
}

.action-btn.enable {
  background: #52c41a;
  color: #ffffff;
}

.action-btn.disable {
  background: #ff4d4f;
  color: #ffffff;
}

.action-btn.message {
  background: #faad14;
  color: #ffffff;
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx 0;
  font-size: 28rpx;
  color: #999;
}

/* 动画效果 */
.user-item {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 375px) {
  .user-list {
    padding: 15rpx;
  }
  
  .user-item {
    padding: 25rpx;
    margin-bottom: 15rpx;
  }
  
  .user-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 40rpx;
  }
  
  .user-nickname {
    font-size: 28rpx;
  }
  
  .user-data {
    padding: 15rpx;
  }
  
  .action-btn {
    height: 60rpx;
    font-size: 24rpx;
  }
}
