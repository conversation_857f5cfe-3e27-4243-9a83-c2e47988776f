// 管理端小程序API接口封装
const { request, API_CONFIG, getApiUrl } = require('./request.js');

// 获取API端点
const ENDPOINTS = API_CONFIG.ENDPOINTS;

// 管理员认证相关API
const authAPI = {
  // 管理员登录
  login(loginData) {
    return request.post(ENDPOINTS.AUTH.LOGIN, loginData);
  },
  
  // 获取管理员信息
  getAdminInfo() {
    return request.get(ENDPOINTS.AUTH.INFO);
  },
  
  // 更新管理员信息
  updateAdminInfo(adminInfo) {
    return request.put(ENDPOINTS.AUTH.UPDATE_INFO, adminInfo);
  },
  
  // 退出登录
  logout() {
    return request.post(ENDPOINTS.AUTH.LOGOUT);
  }
};

// 用户管理相关API
const userAPI = {
  // 获取用户列表
  getUsers(params = {}) {
    return request.get(ENDPOINTS.USERS.LIST, params);
  },
  
  // 获取用户详情
  getUserDetail(userId) {
    const url = getApiUrl(ENDPOINTS.USERS.DETAIL, { id: userId });
    return request.get(url);
  },
  
  // 更新用户状态
  updateUserStatus(userId, status) {
    const url = getApiUrl(ENDPOINTS.USERS.UPDATE_STATUS, { id: userId });
    return request.post(url, { status });
  },
  
  // 获取用户统计
  getUserStats() {
    return request.get(ENDPOINTS.USERS.STATS);
  }
};

// 商家管理相关API
const merchantAPI = {
  // 获取商家列表
  getMerchants(params = {}) {
    return request.get(ENDPOINTS.MERCHANTS.LIST, params);
  },
  
  // 获取商家详情
  getMerchantDetail(merchantId) {
    const url = getApiUrl(ENDPOINTS.MERCHANTS.DETAIL, { id: merchantId });
    return request.get(url);
  },
  
  // 审核通过商家
  approveMerchant(merchantId, approveData) {
    const url = getApiUrl(ENDPOINTS.MERCHANTS.APPROVE, { id: merchantId });
    return request.post(url, approveData);
  },
  
  // 拒绝商家申请
  rejectMerchant(merchantId, rejectData) {
    const url = getApiUrl(ENDPOINTS.MERCHANTS.REJECT, { id: merchantId });
    return request.post(url, rejectData);
  },
  
  // 更新商家状态
  updateMerchantStatus(merchantId, status) {
    const url = getApiUrl(ENDPOINTS.MERCHANTS.UPDATE_STATUS, { id: merchantId });
    return request.post(url, { status });
  },
  
  // 获取商家统计
  getMerchantStats() {
    return request.get(ENDPOINTS.MERCHANTS.STATS);
  }
};

// 订单管理相关API
const orderAPI = {
  // 获取订单列表
  getOrders(params = {}) {
    return request.get(ENDPOINTS.ORDERS.LIST, params);
  },
  
  // 获取订单详情
  getOrderDetail(orderId) {
    const url = getApiUrl(ENDPOINTS.ORDERS.DETAIL, { id: orderId });
    return request.get(url);
  },
  
  // 订单干预
  interveneOrder(orderId, interventionData) {
    const url = getApiUrl(ENDPOINTS.ORDERS.INTERVENE, { id: orderId });
    return request.post(url, interventionData);
  },
  
  // 获取订单统计
  getOrderStats() {
    return request.get(ENDPOINTS.ORDERS.STATS);
  }
};

// 仪表盘相关API
const dashboardAPI = {
  // 获取仪表盘概览
  getDashboardOverview() {
    return request.get(ENDPOINTS.DASHBOARD.OVERVIEW);
  },
  
  // 获取仪表盘统计
  getDashboardStats() {
    return request.get(ENDPOINTS.DASHBOARD.STATS);
  },
  
  // 获取图表数据
  getChartData(chartType, params = {}) {
    return request.get(ENDPOINTS.DASHBOARD.CHARTS, {
      chartType,
      ...params
    });
  }
};

// 系统管理相关API
const systemAPI = {
  // 获取系统设置
  getSystemSettings() {
    return request.get(ENDPOINTS.SYSTEM.SETTINGS);
  },
  
  // 更新系统设置
  updateSystemSettings(settings) {
    return request.put(ENDPOINTS.SYSTEM.UPDATE_SETTINGS, settings);
  },
  
  // 获取系统日志
  getSystemLogs(params = {}) {
    return request.get(ENDPOINTS.SYSTEM.LOGS, params);
  }
};

// 公共API
const commonAPI = {
  // 上传文件
  uploadFile(filePath, formData = {}) {
    return request.upload(ENDPOINTS.COMMON.UPLOAD, filePath, formData);
  },
  
  // 获取地区数据
  getRegions(parentId = 0) {
    return request.get(ENDPOINTS.COMMON.REGIONS, { parentId });
  }
};

// 导出所有API
module.exports = {
  authAPI,
  userAPI,
  merchantAPI,
  orderAPI,
  dashboardAPI,
  systemAPI,
  commonAPI,
  
  // 兼容旧版本
  request: request.request.bind(request),
  get: request.get.bind(request),
  post: request.post.bind(request),
  put: request.put.bind(request),
  delete: request.delete.bind(request)
};
