/* 商家端全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'Segoe UI', Arial, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 商家端主色调 */
.btn-primary {
  background-color: #ff6b35;
  color: #fff;
}

.btn-primary:active {
  background-color: #e55a2b;
}

.text-primary { 
  color: #ff6b35; 
}

.bg-primary {
  background-color: #ff6b35;
}

/* 商家卡片样式 */
.merchant-card {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
}

.merchant-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.merchant-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.merchant-info {
  flex: 1;
}

.merchant-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.merchant-status {
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  background-color: rgba(255, 255, 255, 0.2);
  display: inline-block;
  margin-bottom: 10rpx;
}

.merchant-rating {
  font-size: 24rpx;
  opacity: 0.9;
}

.merchant-switch {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.switch-text {
  font-size: 24rpx;
  margin-top: 10rpx;
}

/* 数据展示样式 */
.today-data {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
}

.data-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #323233;
  margin-bottom: 30rpx;
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
}

.data-item {
  text-align: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.data-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #ff6b35;
  margin-bottom: 10rpx;
}

.data-label {
  font-size: 24rpx;
  color: #969799;
}

/* 快捷操作样式 */
.quick-actions {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
  margin-top: 20rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  position: relative;
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 15rpx;
}

.action-text {
  font-size: 24rpx;
  color: #323233;
  text-align: center;
}

.action-badge {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background-color: #ff4757;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 20rpx;
  text-align: center;
}

/* 订单列表样式 */
.pending-orders {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
}

.order-list {
  margin-top: 20rpx;
}

.order-item {
  padding: 20rpx;
  border: 2rpx solid #ebedf0;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.order-number {
  font-size: 28rpx;
  color: #323233;
  font-weight: 500;
}

.order-status {
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
}

.order-status.pending {
  background-color: #fff7e6;
  color: #ff976a;
}

.order-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.service-name {
  font-size: 30rpx;
  color: #323233;
}

.order-amount {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

.order-time {
  font-size: 24rpx;
  color: #969799;
  margin-bottom: 20rpx;
}

.order-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}

.action-btn {
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
}

.action-btn.reject {
  background-color: #fff;
  color: #969799;
  border: 2rpx solid #ebedf0;
}

.action-btn.accept {
  background-color: #ff6b35;
  color: #fff;
}

/* 消息列表样式 */
.recent-messages {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
}

.message-list {
  margin-top: 20rpx;
}

.message-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #ebedf0;
}

.message-item:last-child {
  border-bottom: none;
}

.message-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.message-content {
  flex: 1;
}

.message-name {
  display: block;
  font-size: 28rpx;
  color: #323233;
  margin-bottom: 8rpx;
}

.message-text {
  font-size: 24rpx;
  color: #969799;
}

.message-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.message-time {
  font-size: 20rpx;
  color: #c8c9cc;
  margin-bottom: 8rpx;
}

.message-badge {
  background-color: #ff4757;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 20rpx;
  text-align: center;
}

.more-btn {
  font-size: 24rpx;
  color: #ff6b35;
}
