// 商家端收益管理页面
const app = getApp();

Page({
  data: {
    overview: {
      totalEarning: '12,580.00',
      orderCount: 156,
      avgOrderAmount: '80.64',
      completionRate: 95,
      availableAmount: '8,420.00',
      changeType: 'up',
      changeText: '较上月增长 12.5%'
    },
    selectedPeriod: {
      label: '本月',
      value: 'month'
    },
    timeOptions: [
      { label: '今日', value: 'today' },
      { label: '本周', value: 'week' },
      { label: '本月', value: 'month' },
      { label: '本季度', value: 'quarter' },
      { label: '本年', value: 'year' }
    ],
    chartType: 'daily',
    chartTabs: [
      { label: '日', value: 'daily' },
      { label: '周', value: 'weekly' },
      { label: '月', value: 'monthly' }
    ],
    recentEarnings: [],
    withdrawRecords: [],
    showTimePicker: false,
    showWithdrawModal: false,
    withdrawAmount: '',
    withdrawFee: 2.00,
    selectedCard: {
      bankName: '招商银行',
      cardNumber: '****1234'
    },
    canWithdraw: false
  },

  onLoad() {
    this.loadEarningsData();
    this.loadRecentEarnings();
    this.loadWithdrawRecords();
  },

  onShow() {
    this.loadEarningsData();
  },

  onPullDownRefresh() {
    this.loadEarningsData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载收益数据
  async loadEarningsData() {
    try {
      // 模拟API调用
      const data = await this.getMockEarningsData();
      this.setData({
        overview: data.overview
      });
    } catch (error) {
      console.error('加载收益数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 加载最近收益记录
  async loadRecentEarnings() {
    try {
      const earnings = await this.getMockRecentEarnings();
      this.setData({
        recentEarnings: earnings
      });
    } catch (error) {
      console.error('加载收益记录失败:', error);
    }
  },

  // 加载提现记录
  async loadWithdrawRecords() {
    try {
      const records = await this.getMockWithdrawRecords();
      this.setData({
        withdrawRecords: records
      });
    } catch (error) {
      console.error('加载提现记录失败:', error);
    }
  },

  // 模拟获取收益数据
  getMockEarningsData() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          overview: {
            totalEarning: '12,580.00',
            orderCount: 156,
            avgOrderAmount: '80.64',
            completionRate: 95,
            availableAmount: '8,420.00',
            changeType: 'up',
            changeText: '较上月增长 12.5%'
          }
        });
      }, 500);
    });
  },

  // 模拟获取最近收益记录
  getMockRecentEarnings() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            id: 1,
            title: '订单收益',
            description: '订单号：LY202412150001',
            time: '12-15 14:30',
            type: 'order',
            typeText: '订单',
            status: 'completed',
            statusText: '已完成',
            amountType: 'income',
            amountText: '+¥85.00',
            balance: '8,420.00'
          },
          {
            id: 2,
            title: '提现',
            description: '提现到招商银行',
            time: '12-14 16:20',
            type: 'withdraw',
            typeText: '提现',
            status: 'processing',
            statusText: '处理中',
            amountType: 'expense',
            amountText: '-¥500.00',
            balance: '8,335.00'
          },
          {
            id: 3,
            title: '订单收益',
            description: '订单号：LY202412140003',
            time: '12-14 10:15',
            type: 'order',
            typeText: '订单',
            status: 'completed',
            statusText: '已完成',
            amountType: 'income',
            amountText: '+¥120.00',
            balance: '8,835.00'
          }
        ]);
      }, 300);
    });
  },

  // 模拟获取提现记录
  getMockWithdrawRecords() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            id: 1,
            amount: '500.00',
            time: '12-14 16:20',
            status: 'processing',
            statusText: '处理中'
          },
          {
            id: 2,
            amount: '1,000.00',
            time: '12-10 09:30',
            status: 'completed',
            statusText: '已到账'
          },
          {
            id: 3,
            amount: '800.00',
            time: '12-05 15:45',
            status: 'completed',
            statusText: '已到账'
          }
        ]);
      }, 300);
    });
  },

  // 时间选择
  onTimeSelect() {
    this.setData({
      showTimePicker: true
    });
  },

  // 隐藏时间选择器
  hideTimePicker() {
    this.setData({
      showTimePicker: false
    });
  },

  // 选择时间周期
  onPeriodSelect(e) {
    const period = e.currentTarget.dataset.period;
    this.setData({
      selectedPeriod: period,
      showTimePicker: false
    });
    this.loadEarningsData();
  },

  // 图表类型切换
  onChartTypeChange(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      chartType: type
    });
  },

  // 提现
  onWithdraw() {
    this.setData({
      showWithdrawModal: true,
      withdrawAmount: '',
      canWithdraw: false
    });
  },

  // 隐藏提现弹窗
  hideWithdrawModal() {
    this.setData({
      showWithdrawModal: false
    });
  },

  // 提现金额输入
  onWithdrawAmountInput(e) {
    const amount = e.detail.value;
    const availableAmount = parseFloat(this.data.overview.availableAmount.replace(/,/g, ''));
    const inputAmount = parseFloat(amount);

    this.setData({
      withdrawAmount: amount,
      canWithdraw: inputAmount > 0 && inputAmount <= availableAmount
    });
  },

  // 选择银行卡
  onSelectBankCard() {
    wx.navigateTo({
      url: '/pages/bank-cards/bank-cards'
    });
  },

  // 确认提现
  onConfirmWithdraw() {
    if (!this.data.canWithdraw) {
      return;
    }

    const { withdrawAmount, withdrawFee } = this.data;
    const totalAmount = parseFloat(withdrawAmount) + withdrawFee;

    wx.showModal({
      title: '确认提现',
      content: `提现金额：¥${withdrawAmount}\n手续费：¥${withdrawFee}\n实际扣除：¥${totalAmount.toFixed(2)}`,
      success: (res) => {
        if (res.confirm) {
          this.submitWithdraw();
        }
      }
    });
  },

  // 提交提现申请
  async submitWithdraw() {
    try {
      wx.showLoading({
        title: '提交中...'
      });

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      wx.hideLoading();
      wx.showToast({
        title: '提现申请已提交',
        icon: 'success'
      });

      this.setData({
        showWithdrawModal: false
      });

      // 刷新数据
      this.loadEarningsData();
      this.loadWithdrawRecords();

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '提交失败',
        icon: 'none'
      });
    }
  },

  // 查看详情
  onViewDetails() {
    wx.navigateTo({
      url: '/pages/earnings-detail/earnings-detail'
    });
  },

  // 查看报表
  onViewReports() {
    wx.navigateTo({
      url: '/pages/earnings-report/earnings-report'
    });
  },

  // 查看全部收益
  onViewAllEarnings() {
    wx.navigateTo({
      url: '/pages/earnings-list/earnings-list'
    });
  },

  // 查看全部提现记录
  onViewAllWithdraws() {
    wx.navigateTo({
      url: '/pages/withdraw-list/withdraw-list'
    });
  },

  // 收益详情
  onEarningDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/earning-detail/earning-detail?id=${id}`
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  }
});