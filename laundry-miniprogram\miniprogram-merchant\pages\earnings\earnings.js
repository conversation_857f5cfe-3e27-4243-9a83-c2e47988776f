// 商家端收益管理页面
const app = getApp();
const { earningsAPI } = require('../../utils/api.js');

Page({
  data: {
    overview: {
      totalEarning: '0.00',
      orderCount: 0,
      avgOrderAmount: '0.00',
      completionRate: 0,
      availableAmount: '0.00',
      changeType: 'up',
      changeText: '暂无数据'
    },
    selectedPeriod: {
      label: '本月',
      value: 'month'
    },
    timeOptions: [
      { label: '今日', value: 'today' },
      { label: '本周', value: 'week' },
      { label: '本月', value: 'month' },
      { label: '本季度', value: 'quarter' },
      { label: '本年', value: 'year' }
    ],
    chartType: 'daily',
    chartTabs: [
      { label: '日', value: 'daily' },
      { label: '周', value: 'weekly' },
      { label: '月', value: 'monthly' }
    ],
    recentEarnings: [],
    withdrawRecords: [],
    showTimePicker: false,
    showWithdrawModal: false,
    withdrawAmount: '',
    withdrawFee: 2.00,
    selectedCard: {
      bankName: '招商银行',
      cardNumber: '****1234'
    },
    canWithdraw: false
  },

  onLoad() {
    this.loadEarningsData();
    this.loadRecentEarnings();
    this.loadWithdrawRecords();
  },

  onShow() {
    this.loadEarningsData();
  },

  onPullDownRefresh() {
    this.loadEarningsData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载收益数据
  async loadEarningsData() {
    try {
      const overview = await earningsAPI.getEarningsOverview();
      this.setData({
        overview
      });
    } catch (error) {
      console.error('加载收益数据失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    }
  },

  // 加载最近收益记录
  async loadRecentEarnings() {
    try {
      const earnings = await earningsAPI.getEarningsList({
        page: 1,
        pageSize: 10
      });
      this.setData({
        recentEarnings: earnings.list || earnings || []
      });
    } catch (error) {
      console.error('加载收益记录失败:', error);
    }
  },

  // 加载提现记录
  async loadWithdrawRecords() {
    try {
      const records = await earningsAPI.getWithdrawRecords({
        page: 1,
        pageSize: 5
      });
      this.setData({
        withdrawRecords: records.list || records || []
      });
    } catch (error) {
      console.error('加载提现记录失败:', error);
    }
  },



  // 时间选择
  onTimeSelect() {
    this.setData({
      showTimePicker: true
    });
  },

  // 隐藏时间选择器
  hideTimePicker() {
    this.setData({
      showTimePicker: false
    });
  },

  // 选择时间周期
  onPeriodSelect(e) {
    const period = e.currentTarget.dataset.period;
    this.setData({
      selectedPeriod: period,
      showTimePicker: false
    });
    this.loadEarningsData();
  },

  // 图表类型切换
  onChartTypeChange(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      chartType: type
    });
  },

  // 提现
  onWithdraw() {
    this.setData({
      showWithdrawModal: true,
      withdrawAmount: '',
      canWithdraw: false
    });
  },

  // 隐藏提现弹窗
  hideWithdrawModal() {
    this.setData({
      showWithdrawModal: false
    });
  },

  // 提现金额输入
  onWithdrawAmountInput(e) {
    const amount = e.detail.value;
    const availableAmount = parseFloat(this.data.overview.availableAmount.replace(/,/g, ''));
    const inputAmount = parseFloat(amount);

    this.setData({
      withdrawAmount: amount,
      canWithdraw: inputAmount > 0 && inputAmount <= availableAmount
    });
  },

  // 选择银行卡
  onSelectBankCard() {
    wx.navigateTo({
      url: '/pages/bank-cards/bank-cards'
    });
  },

  // 确认提现
  onConfirmWithdraw() {
    if (!this.data.canWithdraw) {
      return;
    }

    const { withdrawAmount, withdrawFee } = this.data;
    const totalAmount = parseFloat(withdrawAmount) + withdrawFee;

    wx.showModal({
      title: '确认提现',
      content: `提现金额：¥${withdrawAmount}\n手续费：¥${withdrawFee}\n实际扣除：¥${totalAmount.toFixed(2)}`,
      success: (res) => {
        if (res.confirm) {
          this.submitWithdraw();
        }
      }
    });
  },

  // 提交提现申请
  async submitWithdraw() {
    try {
      wx.showLoading({
        title: '提交中...'
      });

      await earningsAPI.requestWithdraw({
        amount: this.data.withdrawAmount,
        bankCard: this.data.selectedCard
      });

      wx.hideLoading();
      wx.showToast({
        title: '提现申请已提交',
        icon: 'success'
      });

      this.setData({
        showWithdrawModal: false
      });

      // 刷新数据
      this.loadEarningsData();
      this.loadWithdrawRecords();

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '提交失败',
        icon: 'none'
      });
    }
  },

  // 查看详情
  onViewDetails() {
    wx.navigateTo({
      url: '/pages/earnings-detail/earnings-detail'
    });
  },

  // 查看报表
  onViewReports() {
    wx.navigateTo({
      url: '/pages/earnings-report/earnings-report'
    });
  },

  // 查看全部收益
  onViewAllEarnings() {
    wx.navigateTo({
      url: '/pages/earnings-list/earnings-list'
    });
  },

  // 查看全部提现记录
  onViewAllWithdraws() {
    wx.navigateTo({
      url: '/pages/withdraw-list/withdraw-list'
    });
  },

  // 收益详情
  onEarningDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/earning-detail/earning-detail?id=${id}`
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  }
});