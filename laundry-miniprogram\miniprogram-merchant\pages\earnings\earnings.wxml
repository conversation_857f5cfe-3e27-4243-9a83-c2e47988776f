<!--商家端收益管理页面-->
<view class="container">
  <!-- 收益概览 -->
  <view class="earnings-overview">
    <view class="overview-header">
      <text class="overview-title">收益概览</text>
      <view class="time-selector" bindtap="onTimeSelect">
        <text class="time-text">{{selectedPeriod.label}}</text>
        <image class="arrow-icon" src="/images/arrow-down.png"></image>
      </view>
    </view>

    <view class="overview-content">
      <view class="main-earning">
        <text class="earning-label">总收益</text>
        <text class="earning-amount">¥{{overview.totalEarning}}</text>
        <view class="earning-change {{overview.changeType}}">
          <image class="change-icon" src="/images/{{overview.changeType}}-icon.png"></image>
          <text class="change-text">{{overview.changeText}}</text>
        </view>
      </view>

      <view class="earning-stats">
        <view class="stat-item">
          <text class="stat-number">{{overview.orderCount}}</text>
          <text class="stat-label">订单数</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">¥{{overview.avgOrderAmount}}</text>
          <text class="stat-label">客单价</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{overview.completionRate}}%</text>
          <text class="stat-label">完成率</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 收益趋势图 -->
  <view class="earnings-chart">
    <view class="chart-header">
      <text class="chart-title">收益趋势</text>
      <view class="chart-tabs">
        <view
          class="chart-tab {{chartType === item.value ? 'active' : ''}}"
          wx:for="{{chartTabs}}"
          wx:key="value"
          bindtap="onChartTypeChange"
          data-type="{{item.value}}"
        >
          {{item.label}}
        </view>
      </view>
    </view>
    <view class="chart-container">
      <!-- 这里可以集成图表组件 -->
      <view class="chart-placeholder">
        <image class="chart-image" src="/images/earnings-chart.png"></image>
        <text class="chart-desc">收益趋势图</text>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <view class="action-item" bindtap="onWithdraw">
      <image class="action-icon" src="/images/withdraw-icon.png"></image>
      <text class="action-text">提现</text>
      <text class="action-desc">可提现 ¥{{overview.availableAmount}}</text>
    </view>
    <view class="action-item" bindtap="onViewDetails">
      <image class="action-icon" src="/images/detail-icon.png"></image>
      <text class="action-text">明细</text>
      <text class="action-desc">查看收益明细</text>
    </view>
    <view class="action-item" bindtap="onViewReports">
      <image class="action-icon" src="/images/report-icon.png"></image>
      <text class="action-text">报表</text>
      <text class="action-desc">财务报表</text>
    </view>
  </view>

  <!-- 最近收益记录 -->
  <view class="recent-earnings">
    <view class="section-header">
      <text class="section-title">最近收益</text>
      <text class="more-btn" bindtap="onViewAllEarnings">查看全部</text>
    </view>

    <view class="earnings-list">
      <view class="earning-item" wx:for="{{recentEarnings}}" wx:key="id" bindtap="onEarningDetail" data-id="{{item.id}}">
        <view class="earning-info">
          <view class="earning-header">
            <text class="earning-title">{{item.title}}</text>
            <text class="earning-time">{{item.time}}</text>
          </view>
          <text class="earning-desc">{{item.description}}</text>
          <view class="earning-tags">
            <text class="earning-tag {{item.type}}">{{item.typeText}}</text>
            <text class="earning-status {{item.status}}">{{item.statusText}}</text>
          </view>
        </view>
        <view class="earning-amount-container">
          <text class="earning-amount {{item.amountType}}">{{item.amountText}}</text>
          <text class="earning-balance">余额: ¥{{item.balance}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 提现记录 -->
  <view class="withdraw-records">
    <view class="section-header">
      <text class="section-title">提现记录</text>
      <text class="more-btn" bindtap="onViewAllWithdraws">查看全部</text>
    </view>

    <view class="withdraw-list">
      <view class="withdraw-item" wx:for="{{withdrawRecords}}" wx:key="id">
        <view class="withdraw-info">
          <text class="withdraw-amount">¥{{item.amount}}</text>
          <text class="withdraw-time">{{item.time}}</text>
        </view>
        <view class="withdraw-status {{item.status}}">
          <text class="status-text">{{item.statusText}}</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!withdrawRecords.length}}">
      <image class="empty-image" src="/images/empty-withdraw.png"></image>
      <text class="empty-text">暂无提现记录</text>
    </view>
  </view>
</view>

<!-- 时间选择器 -->
<view class="time-picker-modal" wx:if="{{showTimePicker}}" bindtap="hideTimePicker">
  <view class="time-picker-content" catchtap="stopPropagation">
    <view class="picker-header">
      <text class="picker-title">选择时间范围</text>
      <image class="close-btn" src="/images/close.png" bindtap="hideTimePicker"></image>
    </view>
    <view class="picker-body">
      <view
        class="time-option {{selectedPeriod.value === item.value ? 'active' : ''}}"
        wx:for="{{timeOptions}}"
        wx:key="value"
        bindtap="onPeriodSelect"
        data-period="{{item}}"
      >
        <text class="option-text">{{item.label}}</text>
        <image class="check-icon" src="/images/check.png" wx:if="{{selectedPeriod.value === item.value}}"></image>
      </view>
    </view>
  </view>
</view>

<!-- 提现弹窗 -->
<view class="withdraw-modal" wx:if="{{showWithdrawModal}}" bindtap="hideWithdrawModal">
  <view class="withdraw-modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">申请提现</text>
      <image class="close-btn" src="/images/close.png" bindtap="hideWithdrawModal"></image>
    </view>
    <view class="modal-body">
      <view class="withdraw-form">
        <view class="form-item">
          <text class="form-label">可提现金额</text>
          <text class="available-amount">¥{{overview.availableAmount}}</text>
        </view>
        <view class="form-item">
          <text class="form-label">提现金额</text>
          <input
            class="amount-input"
            type="digit"
            placeholder="请输入提现金额"
            value="{{withdrawAmount}}"
            bindinput="onWithdrawAmountInput"
          />
        </view>
        <view class="form-item">
          <text class="form-label">到账银行卡</text>
          <view class="bank-card" bindtap="onSelectBankCard">
            <text class="card-info">{{selectedCard.bankName}} ({{selectedCard.cardNumber}})</text>
            <image class="arrow-icon" src="/images/arrow-right.png"></image>
          </view>
        </view>
        <view class="form-tips">
          <text class="tips-text">• 提现手续费：{{withdrawFee}}元</text>
          <text class="tips-text">• 预计到账时间：1-3个工作日</text>
        </view>
      </view>
    </view>
    <view class="modal-footer">
      <button class="cancel-btn" bindtap="hideWithdrawModal">取消</button>
      <button class="confirm-btn" bindtap="onConfirmWithdraw" disabled="{{!canWithdraw}}">确认提现</button>
    </view>
  </view>
</view>