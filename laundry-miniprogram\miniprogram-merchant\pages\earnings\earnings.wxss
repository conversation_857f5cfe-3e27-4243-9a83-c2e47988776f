/* 商家端收益管理页面样式 */
.container {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
}

/* 收益概览 */
.earnings-overview {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  color: #ffffff;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.overview-title {
  font-size: 32rpx;
  font-weight: bold;
}

.time-selector {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 12rpx 20rpx;
  border-radius: 40rpx;
}

.time-text {
  font-size: 26rpx;
  margin-right: 8rpx;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
}

.overview-content {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.main-earning {
  text-align: center;
}

.earning-label {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 12rpx;
}

.earning-amount {
  display: block;
  font-size: 56rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.earning-change {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.change-icon {
  width: 24rpx;
  height: 24rpx;
}

.change-text {
  font-size: 24rpx;
  opacity: 0.9;
}

.earning-stats {
  display: flex;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 收益趋势图 */
.earnings-chart {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.chart-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.chart-tabs {
  display: flex;
  background: #f8f9fa;
  border-radius: 40rpx;
  padding: 4rpx;
}

.chart-tab {
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  color: #666;
  border-radius: 36rpx;
  transition: all 0.3s ease;
}

.chart-tab.active {
  background: #ff6b35;
  color: #ffffff;
}

.chart-container {
  height: 400rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
}

.chart-image {
  width: 200rpx;
  height: 150rpx;
  opacity: 0.6;
  margin-bottom: 20rpx;
}

.chart-desc {
  font-size: 28rpx;
  color: #999;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.action-item {
  flex: 1;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.action-item:active {
  transform: scale(0.98);
  background: #f8f9fa;
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 16rpx;
}

.action-text {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.action-desc {
  display: block;
  font-size: 22rpx;
  color: #999;
}

/* 最近收益记录 */
.recent-earnings {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.more-btn {
  font-size: 26rpx;
  color: #ff6b35;
}

.earnings-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.earning-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
  transition: all 0.3s ease;
}

.earning-item:last-child {
  border-bottom: none;
}

.earning-item:active {
  background: #f8f9fa;
  margin: 0 -20rpx;
  padding-left: 20rpx;
  padding-right: 20rpx;
  border-radius: 12rpx;
}

.earning-info {
  flex: 1;
  margin-right: 20rpx;
}

.earning-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.earning-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.earning-time {
  font-size: 24rpx;
  color: #999;
}

.earning-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.earning-tags {
  display: flex;
  gap: 12rpx;
}

.earning-tag {
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
}

.earning-tag.order {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.earning-tag.withdraw {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.earning-status {
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
}

.earning-status.completed {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.earning-status.processing {
  background: rgba(250, 140, 22, 0.1);
  color: #fa8c16;
}

.earning-amount-container {
  text-align: right;
}

.earning-amount {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.earning-amount.income {
  color: #52c41a;
}

.earning-amount.expense {
  color: #ff4d4f;
}

.earning-balance {
  font-size: 22rpx;
  color: #999;
}

/* 提现记录 */
.withdraw-records {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.withdraw-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.withdraw-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
}

.withdraw-item:last-child {
  border-bottom: none;
}

.withdraw-info {
  flex: 1;
}

.withdraw-amount {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.withdraw-time {
  font-size: 24rpx;
  color: #999;
}

.withdraw-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.withdraw-status.processing {
  background: rgba(250, 140, 22, 0.1);
  color: #fa8c16;
}

.withdraw-status.completed {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-image {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 弹窗样式 */
.time-picker-modal,
.withdraw-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.time-picker-content,
.withdraw-modal-content {
  background: #ffffff;
  border-radius: 20rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.picker-header,
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.picker-title,
.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  width: 40rpx;
  height: 40rpx;
}

.picker-body {
  padding: 20rpx 0;
}

.time-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 40rpx;
  transition: all 0.3s ease;
}

.time-option:active {
  background: #f8f9fa;
}

.time-option.active {
  background: #f0f8ff;
}

.option-text {
  font-size: 30rpx;
  color: #333;
}

.check-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 提现表单 */
.modal-body {
  padding: 40rpx;
}

.withdraw-form {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.available-amount {
  font-size: 36rpx;
  color: #ff6b35;
  font-weight: bold;
}

.amount-input {
  height: 80rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 32rpx;
  color: #333;
}

.amount-input:focus {
  border-color: #ff6b35;
}

.bank-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 20rpx;
}

.card-info {
  font-size: 28rpx;
  color: #333;
}

.form-tips {
  background: #f0f8ff;
  padding: 20rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #1890ff;
}

.tips-text {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.tips-text:last-child {
  margin-bottom: 0;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 40rpx 40rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666;
}

.confirm-btn {
  background: #ff6b35;
  color: #ffffff;
}

.confirm-btn:disabled {
  background: #cccccc;
  color: #999999;
}

/* 动画效果 */
.earnings-overview {
  animation: slideDown 0.5s ease-out;
}

.earnings-chart,
.quick-actions,
.recent-earnings,
.withdraw-records {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 15rpx;
  }
  
  .earnings-overview,
  .earnings-chart,
  .recent-earnings,
  .withdraw-records {
    padding: 30rpx;
    margin-bottom: 15rpx;
  }
  
  .earning-amount {
    font-size: 48rpx;
  }
  
  .quick-actions {
    gap: 15rpx;
  }
  
  .action-item {
    padding: 25rpx 15rpx;
  }
}
