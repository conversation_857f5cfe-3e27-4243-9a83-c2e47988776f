const app = getApp();
const { financeAPI, earningsAPI } = require('../../utils/api.js');

Page({
  data: {
    currentTab: 0,
    tabs: [
      { key: 'overview', name: '财务概览' },
      { key: 'earnings', name: '收益明细' },
      { key: 'withdraw', name: '提现记录' },
      { key: 'bills', name: '账单明细' }
    ],
    
    // 财务概览
    overview: {
      totalEarnings: '0.00',
      availableAmount: '0.00',
      frozenAmount: '0.00',
      todayEarnings: '0.00',
      monthEarnings: '0.00',
      totalWithdraw: '0.00',
      pendingWithdraw: '0.00'
    },

    // 收益明细
    earnings: [],
    
    // 提现记录
    withdrawRecords: [],
    
    // 账单明细
    bills: [],

    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 20,

    // 提现相关
    showWithdrawModal: false,
    withdrawAmount: '',
    minWithdrawAmount: 100,
    maxWithdrawAmount: 50000,
    withdrawFee: 0,
    actualAmount: 0,

    // 银行卡信息
    bankCards: [],
    selectedCard: null,
    showCardModal: false
  },

  onLoad() {
    this.loadOverview();
    this.loadBankCards();
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
    
    // 刷新数据
    this.loadOverview();
    this.loadCurrentTabData();
  },

  // Tab切换
  onTabChange(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentTab: index,
      page: 1,
      hasMore: true
    });
    this.loadCurrentTabData();
  },

  // 加载当前Tab数据
  loadCurrentTabData() {
    const currentTab = this.data.tabs[this.data.currentTab];
    
    switch (currentTab.key) {
      case 'earnings':
        this.loadEarnings();
        break;
      case 'withdraw':
        this.loadWithdrawRecords();
        break;
      case 'bills':
        this.loadBills();
        break;
    }
  },

  // 加载财务概览
  async loadOverview() {
    try {
      const overview = await financeAPI.getFinanceOverview();
      this.setData({ overview });
    } catch (error) {
      console.error('加载财务概览失败:', error);
    }
  },

  // 加载收益明细
  async loadEarnings() {
    if (this.data.loading || !this.data.hasMore) return;

    try {
      this.setData({ loading: true });

      const params = {
        page: this.data.page,
        pageSize: this.data.pageSize
      };

      const result = await earningsAPI.getEarningsList(params);
      const newEarnings = result.list || result || [];
      const earnings = this.data.page === 1 ? newEarnings : [...this.data.earnings, ...newEarnings];

      this.setData({
        earnings,
        hasMore: newEarnings.length === this.data.pageSize,
        page: this.data.page + 1,
        loading: false
      });

    } catch (error) {
      console.error('加载收益明细失败:', error);
      this.setData({ loading: false });
    }
  },

  // 加载提现记录
  async loadWithdrawRecords() {
    if (this.data.loading || !this.data.hasMore) return;

    try {
      this.setData({ loading: true });

      const params = {
        page: this.data.page,
        pageSize: this.data.pageSize
      };

      const result = await financeAPI.getWithdrawRecords(params);
      const newRecords = result.list || result || [];
      const withdrawRecords = this.data.page === 1 ? newRecords : [...this.data.withdrawRecords, ...newRecords];

      this.setData({
        withdrawRecords,
        hasMore: newRecords.length === this.data.pageSize,
        page: this.data.page + 1,
        loading: false
      });

    } catch (error) {
      console.error('加载提现记录失败:', error);
      this.setData({ loading: false });
    }
  },

  // 加载账单明细
  async loadBills() {
    if (this.data.loading || !this.data.hasMore) return;

    try {
      this.setData({ loading: true });

      const params = {
        page: this.data.page,
        pageSize: this.data.pageSize
      };

      const result = await financeAPI.getBills(params);
      const newBills = result.list || result || [];
      const bills = this.data.page === 1 ? newBills : [...this.data.bills, ...newBills];

      this.setData({
        bills,
        hasMore: newBills.length === this.data.pageSize,
        page: this.data.page + 1,
        loading: false
      });

    } catch (error) {
      console.error('加载账单明细失败:', error);
      this.setData({ loading: false });
    }
  },

  // 加载银行卡
  async loadBankCards() {
    try {
      const bankCards = await financeAPI.getBankCards();
      this.setData({
        bankCards,
        selectedCard: bankCards.find(card => card.isDefault) || bankCards[0]
      });
    } catch (error) {
      console.error('加载银行卡失败:', error);
    }
  },

  // 显示提现弹窗
  onShowWithdraw() {
    if (this.data.bankCards.length === 0) {
      wx.showModal({
        title: '提示',
        content: '请先添加银行卡',
        confirmText: '去添加',
        success: (res) => {
          if (res.confirm) {
            this.onAddBankCard();
          }
        }
      });
      return;
    }

    this.setData({
      showWithdrawModal: true,
      withdrawAmount: '',
      withdrawFee: 0,
      actualAmount: 0
    });
  },

  // 关闭提现弹窗
  onCloseWithdraw() {
    this.setData({
      showWithdrawModal: false
    });
  },

  // 提现金额输入
  onWithdrawAmountInput(e) {
    const amount = parseFloat(e.detail.value) || 0;
    const fee = this.calculateWithdrawFee(amount);
    const actualAmount = Math.max(0, amount - fee);

    this.setData({
      withdrawAmount: e.detail.value,
      withdrawFee: fee,
      actualAmount
    });
  },

  // 计算提现手续费
  calculateWithdrawFee(amount) {
    if (amount <= 0) return 0;
    
    // 提现手续费规则：1000以下免费，1000以上收取0.1%，最低2元
    if (amount <= 1000) {
      return 0;
    } else {
      return Math.max(2, amount * 0.001);
    }
  },

  // 快速金额选择
  onQuickAmount(e) {
    const amount = e.currentTarget.dataset.amount;
    const availableAmount = parseFloat(this.data.overview.availableAmount);
    
    let finalAmount;
    if (amount === 'all') {
      finalAmount = availableAmount;
    } else {
      finalAmount = Math.min(parseFloat(amount), availableAmount);
    }

    const fee = this.calculateWithdrawFee(finalAmount);
    const actualAmount = Math.max(0, finalAmount - fee);

    this.setData({
      withdrawAmount: finalAmount.toString(),
      withdrawFee: fee,
      actualAmount
    });
  },

  // 选择银行卡
  onSelectCard() {
    this.setData({
      showCardModal: true
    });
  },

  // 银行卡选择确认
  onCardSelect(e) {
    const card = e.currentTarget.dataset.card;
    this.setData({
      selectedCard: card,
      showCardModal: false
    });
  },

  // 添加银行卡
  onAddBankCard() {
    wx.navigateTo({
      url: '/pages/bank-card/bank-card'
    });
  },

  // 提交提现申请
  async onSubmitWithdraw() {
    const { withdrawAmount, selectedCard, minWithdrawAmount, maxWithdrawAmount } = this.data;
    const amount = parseFloat(withdrawAmount);

    if (!amount || amount < minWithdrawAmount) {
      wx.showToast({
        title: `最低提现金额${minWithdrawAmount}元`,
        icon: 'none'
      });
      return;
    }

    if (amount > maxWithdrawAmount) {
      wx.showToast({
        title: `最高提现金额${maxWithdrawAmount}元`,
        icon: 'none'
      });
      return;
    }

    if (amount > parseFloat(this.data.overview.availableAmount)) {
      wx.showToast({
        title: '提现金额超过可用余额',
        icon: 'none'
      });
      return;
    }

    if (!selectedCard) {
      wx.showToast({
        title: '请选择银行卡',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({
        title: '提交中...'
      });

      await financeAPI.requestWithdraw({
        amount,
        bankCardId: selectedCard.id,
        fee: this.data.withdrawFee
      });

      wx.hideLoading();
      wx.showToast({
        title: '提现申请已提交',
        icon: 'success'
      });

      this.setData({
        showWithdrawModal: false
      });

      // 刷新数据
      this.loadOverview();
      this.loadWithdrawRecords();

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '提现申请失败',
        icon: 'none'
      });
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadOverview();
    this.setData({
      page: 1,
      hasMore: true
    });
    this.loadCurrentTabData();
    wx.stopPullDownRefresh();
  },

  // 触底加载
  onReachBottom() {
    if (this.data.currentTab !== 0) {
      this.loadCurrentTabData();
    }
  }
});
