const app = getApp();
const { dashboardAPI, orderAPI, serviceAPI, earningsAPI } = require('../../utils/api.js');

Page({
  data: {
    merchantInfo: {},
    dashboardStats: {
      todayOrders: 0,
      todayEarnings: 0,
      totalOrders: 0,
      totalEarnings: 0,
      pendingOrders: 0,
      processingOrders: 0,
      completedOrders: 0,
      totalServices: 0
    },
    recentOrders: [],
    quickActions: [
      {
        icon: 'add-circle',
        title: '添加服务',
        desc: '发布新的服务项目',
        url: '/pages/service-add/service-add'
      },
      {
        icon: 'list',
        title: '订单管理',
        desc: '查看和处理订单',
        url: '/pages/orders/orders'
      },
      {
        icon: 'money',
        title: '收益管理',
        desc: '查看收益和提现',
        url: '/pages/earnings/earnings'
      },
      {
        icon: 'chart',
        title: '数据统计',
        desc: '查看经营数据',
        url: '/pages/statistics/statistics'
      }
    ],
    loading: true
  },

  onLoad(options) {
    this.loadDashboardData();
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    // 刷新数据
    this.loadDashboardData();
  },

  // 加载仪表盘数据
  loadDashboardData() {
    this.setData({
      loading: true
    });

    Promise.all([
      this.loadDashboardOverview(),
      this.loadRecentOrders(),
      this.loadMerchantInfo()
    ]).finally(() => {
      this.setData({
        loading: false
      });
    });
  },

  // 加载仪表盘概览
  loadDashboardOverview() {
    return dashboardAPI.getDashboardOverview().then(res => {
      this.setData({
        dashboardStats: {
          ...this.data.dashboardStats,
          ...res
        }
      });
    }).catch(err => {
      console.error('加载仪表盘概览失败:', err);
    });
  },

  // 加载最近订单
  loadRecentOrders() {
    return orderAPI.getOrders({
      page: 1,
      pageSize: 5,
      sortBy: 'createTime'
    }).then(res => {
      this.setData({
        recentOrders: res.list || res || []
      });
    }).catch(err => {
      console.error('加载最近订单失败:', err);
    });
  },

  // 加载商家信息
  loadMerchantInfo() {
    return app.getMerchantInfo().then(merchantInfo => {
      this.setData({
        merchantInfo
      });
    }).catch(err => {
      console.error('加载商家信息失败:', err);
    });
  },

  // 快捷操作点击
  onQuickActionTap(e) {
    const url = e.currentTarget.dataset.url;
    wx.navigateTo({
      url
    });
  },

  // 查看更多订单
  onViewMoreOrders() {
    wx.switchTab({
      url: '/pages/orders/orders'
    });
  },

  // 订单项点击
  onOrderTap(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?id=${orderId}`
    });
  },

  // 统计卡片点击
  onStatCardTap(e) {
    const type = e.currentTarget.dataset.type;
    let url = '/pages/orders/orders';

    switch (type) {
      case 'pending':
        url += '?tab=pending';
        break;
      case 'processing':
        url += '?tab=processing';
        break;
      case 'completed':
        url += '?tab=completed';
        break;
      case 'earnings':
        url = '/pages/earnings/earnings';
        break;
      case 'services':
        url = '/pages/services/services';
        break;
    }

    wx.navigateTo({
      url
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadDashboardData();
    wx.stopPullDownRefresh();
  }
});