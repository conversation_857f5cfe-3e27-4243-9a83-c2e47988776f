<view class="container">
  <!-- 商家信息卡片 -->
  <view class="merchant-card">
    <view class="merchant-header">
      <image src="{{merchantInfo.avatar}}" class="merchant-avatar"></image>
      <view class="merchant-info">
        <text class="merchant-name">{{merchantInfo.name}}</text>
        <text class="merchant-status {{merchantInfo.status}}">{{merchantInfo.statusText}}</text>
        <view class="merchant-rating">
          <text class="rating-score">★{{merchantInfo.rating}}</text>
          <text class="rating-count">({{merchantInfo.reviewCount}}条评价)</text>
        </view>
      </view>
      <view class="merchant-switch">
        <switch checked="{{merchantInfo.isOnline}}" bindchange="onStatusChange" />
        <text class="switch-text">{{merchantInfo.isOnline ? '营业中' : '休息中'}}</text>
      </view>
    </view>
  </view>

  <!-- 今日数据 -->
  <view class="today-data">
    <view class="data-title">今日数据</view>
    <view class="data-grid">
      <view class="data-item">
        <text class="data-number">{{todayData.orders}}</text>
        <text class="data-label">新订单</text>
      </view>
      <view class="data-item">
        <text class="data-number">¥{{todayData.revenue}}</text>
        <text class="data-label">收入</text>
      </view>
      <view class="data-item">
        <text class="data-number">{{todayData.completedOrders}}</text>
        <text class="data-label">完成订单</text>
      </view>
      <view class="data-item">
        <text class="data-number">{{todayData.newCustomers}}</text>
        <text class="data-label">新客户</text>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <view class="section-title">快捷操作</view>
    <view class="actions-grid">
      <view class="action-item" bindtap="onOrderManagement">
        <image src="/images/order-manage.png" class="action-icon"></image>
        <text class="action-text">订单管理</text>
        <view class="action-badge" wx:if="{{pendingOrders > 0}}">{{pendingOrders}}</view>
      </view>
      <view class="action-item" bindtap="onServiceManagement">
        <image src="/images/service-manage.png" class="action-icon"></image>
        <text class="action-text">服务管理</text>
      </view>
      <view class="action-item" bindtap="onEarningsManagement">
        <image src="/images/earnings.png" class="action-icon"></image>
        <text class="action-text">收益管理</text>
      </view>
      <view class="action-item" bindtap="onCustomerService">
        <image src="/images/customer-service.png" class="action-icon"></image>
        <text class="action-text">客服聊天</text>
        <view class="action-badge" wx:if="{{unreadMessages > 0}}">{{unreadMessages}}</view>
      </view>
      <view class="action-item" bindtap="onStatistics">
        <image src="/images/statistics.png" class="action-icon"></image>
        <text class="action-text">数据统计</text>
      </view>
      <view class="action-item" bindtap="onProfileSettings">
        <image src="/images/settings.png" class="action-icon"></image>
        <text class="action-text">店铺设置</text>
      </view>
    </view>
  </view>

  <!-- 待处理订单 -->
  <view class="pending-orders" wx:if="{{pendingOrderList.length > 0}}">
    <view class="section-title">
      <text>待处理订单</text>
      <text class="more-btn" bindtap="onMoreOrders">查看全部</text>
    </view>
    <view class="order-list">
      <view class="order-item" wx:for="{{pendingOrderList}}" wx:key="id" bindtap="onOrderTap" data-id="{{item.id}}">
        <view class="order-header">
          <text class="order-number">{{item.orderNumber}}</text>
          <text class="order-status {{item.status}}">{{item.statusText}}</text>
        </view>
        <view class="order-content">
          <text class="service-name">{{item.serviceName}}</text>
          <text class="order-amount">¥{{item.amount}}</text>
        </view>
        <view class="order-time">{{item.createTime}}</view>
        <view class="order-actions">
          <button class="action-btn reject" bindtap="onRejectOrder" data-id="{{item.id}}">拒接</button>
          <button class="action-btn accept" bindtap="onAcceptOrder" data-id="{{item.id}}">接单</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 最新消息 -->
  <view class="recent-messages" wx:if="{{recentMessages.length > 0}}">
    <view class="section-title">
      <text>最新消息</text>
      <text class="more-btn" bindtap="onMoreMessages">查看全部</text>
    </view>
    <view class="message-list">
      <view class="message-item" wx:for="{{recentMessages}}" wx:key="id" bindtap="onMessageTap" data-id="{{item.id}}">
        <image src="{{item.avatar}}" class="message-avatar"></image>
        <view class="message-content">
          <text class="message-name">{{item.customerName}}</text>
          <text class="message-text">{{item.lastMessage}}</text>
        </view>
        <view class="message-meta">
          <text class="message-time">{{item.time}}</text>
          <view class="message-badge" wx:if="{{item.unreadCount > 0}}">{{item.unreadCount}}</view>
        </view>
      </view>
    </view>
  </view>
</view>
