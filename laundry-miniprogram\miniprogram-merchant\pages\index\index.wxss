/* 商家端首页样式 */
.container {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
}

/* 商家信息卡片 */
.merchant-card {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  color: #ffffff;
}

.merchant-header {
  display: flex;
  align-items: center;
}

.merchant-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  margin-right: 30rpx;
}

.merchant-info {
  flex: 1;
}

.merchant-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.merchant-status {
  display: inline-block;
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  margin-bottom: 10rpx;
}

.merchant-status.APPROVED {
  background: rgba(82, 196, 26, 0.2);
  color: #52c41a;
}

.merchant-status.PENDING {
  background: rgba(250, 140, 22, 0.2);
  color: #fa8c16;
}

.merchant-status.REJECTED {
  background: rgba(255, 77, 79, 0.2);
  color: #ff4d4f;
}

.merchant-rating {
  display: flex;
  align-items: center;
}

.rating-score {
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 10rpx;
  color: #ffd700;
}

.rating-count {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.merchant-switch {
  text-align: center;
}

.switch-text {
  display: block;
  font-size: 24rpx;
  margin-top: 10rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 今日数据 */
.today-data {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.data-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.data-grid {
  display: flex;
  justify-content: space-between;
}

.data-item {
  text-align: center;
  flex: 1;
}

.data-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b35;
  margin-bottom: 8rpx;
}

.data-label {
  font-size: 24rpx;
  color: #666;
}

/* 快捷操作 */
.quick-actions {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.more-btn {
  font-size: 26rpx;
  color: #ff6b35;
  font-weight: normal;
}

.actions-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.action-item {
  width: calc(33.333% - 14rpx);
  text-align: center;
  padding: 30rpx 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  position: relative;
  transition: all 0.3s ease;
}

.action-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 16rpx;
}

.action-text {
  display: block;
  font-size: 26rpx;
  color: #333;
}

.action-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: #ff4d4f;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1;
}

/* 待处理订单 */
.pending-orders {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.order-list {
  margin-top: 20rpx;
}

.order-item {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-left: 6rpx solid #ff6b35;
}

.order-item:last-child {
  margin-bottom: 0;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.order-number {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.order-status {
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
}

.order-status.PENDING_ACCEPT {
  background: rgba(250, 140, 22, 0.1);
  color: #fa8c16;
}

.order-status.PENDING_PAYMENT {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.order-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.service-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.order-amount {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

.order-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.order-actions {
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
}

.action-btn {
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  border: none;
  min-width: 120rpx;
}

.action-btn.reject {
  background: #ffffff;
  color: #ff4d4f;
  border: 2rpx solid #ff4d4f;
}

.action-btn.accept {
  background: #ff6b35;
  color: #ffffff;
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 最新消息 */
.recent-messages {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.message-list {
  margin-top: 20rpx;
}

.message-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.message-item:last-child {
  border-bottom: none;
}

.message-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
}

.message-content {
  flex: 1;
  margin-right: 20rpx;
}

.message-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.message-text {
  display: block;
  font-size: 26rpx;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.message-meta {
  text-align: right;
}

.message-time {
  display: block;
  font-size: 22rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.message-badge {
  background: #ff4d4f;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1;
}

/* 动画效果 */
.merchant-card {
  animation: slideDown 0.5s ease-out;
}

.today-data,
.quick-actions,
.pending-orders,
.recent-messages {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 15rpx;
  }
  
  .merchant-card,
  .today-data,
  .quick-actions,
  .pending-orders,
  .recent-messages {
    padding: 30rpx;
    margin-bottom: 15rpx;
  }
  
  .action-item {
    width: calc(50% - 10rpx);
    padding: 25rpx 15rpx;
  }
  
  .data-number {
    font-size: 32rpx;
  }
  
  .merchant-name {
    font-size: 32rpx;
  }
}
