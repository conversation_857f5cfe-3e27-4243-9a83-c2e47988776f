// 商家端登录页面
const app = getApp();
const { authAPI } = require('../../utils/api.js');

Page({
  data: {
    username: '',
    password: '',
    showPassword: false,
    rememberPassword: false,
    loginLoading: false,
    canLogin: false,
    showAuthStatus: false,
    authStatus: {},
    showAgreementModal: false,
    modalTitle: '',
    agreementContent: ''
  },

  onLoad(options) {
    // 检查是否已登录
    if (app.globalData.isLoggedIn && app.globalData.userType === 'MERCHANT') {
      wx.switchTab({
        url: '/pages/index/index'
      });
      return;
    }

    // 加载记住的密码
    this.loadRememberedPassword();

    // 检查认证状态
    this.checkAuthStatus();
  },

  // 加载记住的密码
  loadRememberedPassword() {
    try {
      const savedUsername = wx.getStorageSync('merchant_username');
      const savedPassword = wx.getStorageSync('merchant_password');
      const rememberPassword = wx.getStorageSync('merchant_remember_password');

      if (rememberPassword && savedUsername && savedPassword) {
        this.setData({
          username: savedUsername,
          password: savedPassword,
          rememberPassword: true
        });
        this.checkCanLogin();
      }
    } catch (error) {
      console.error('加载记住密码失败:', error);
    }
  },

  // 检查认证状态
  checkAuthStatus() {
    // 这里可以检查商家的认证状态
    // 如果有特殊状态需要显示，可以设置 showAuthStatus 为 true
  },

  // 用户名输入
  onUsernameInput(e) {
    this.setData({
      username: e.detail.value
    });
    this.checkCanLogin();
  },

  // 密码输入
  onPasswordInput(e) {
    this.setData({
      password: e.detail.value
    });
    this.checkCanLogin();
  },

  // 切换密码显示
  togglePassword() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  // 切换记住密码
  toggleRemember() {
    this.setData({
      rememberPassword: !this.data.rememberPassword
    });
  },

  // 检查是否可以登录
  checkCanLogin() {
    const { username, password } = this.data;
    const canLogin = username.trim().length >= 3 && password.length >= 6;

    this.setData({
      canLogin
    });
  },

  // 登录
  async onLogin() {
    if (!this.data.canLogin || this.data.loginLoading) {
      return;
    }

    const { username, password, rememberPassword } = this.data;

    this.setData({
      loginLoading: true
    });

    try {
      // 调用登录API
      const loginResult = await authAPI.login({
        username: username.trim(),
        password: password,
        loginType: 'PASSWORD'
      });

      // 保存登录信息
      app.saveLoginInfo(loginResult);

      // 保存记住的密码
      if (rememberPassword) {
        this.savePassword();
      } else {
        this.clearSavedPassword();
      }

      app.showSuccess('登录成功');

      // 检查商家认证状态
      this.checkMerchantStatus(loginResult.merchantInfo);

    } catch (error) {
      console.error('登录失败:', error);
      app.showError(error.message || '登录失败，请重试');
    } finally {
      this.setData({
        loginLoading: false
      });
    }
  },

  // 检查商家状态
  checkMerchantStatus(merchantInfo) {
    const status = merchantInfo.status;

    switch (status) {
      case 'PENDING':
        this.showAuthStatusDialog({
          icon: 'pending',
          title: '审核中',
          description: '您的商家资质正在审核中，请耐心等待审核结果',
          buttonText: '我知道了',
          action: 'hideAuthStatus'
        });
        break;
      case 'REJECTED':
        this.showAuthStatusDialog({
          icon: 'rejected',
          title: '审核未通过',
          description: merchantInfo.rejectReason || '您的商家资质审核未通过，请重新提交',
          buttonText: '重新提交',
          action: 'goToAuth'
        });
        break;
      case 'APPROVED':
        // 审核通过，正常跳转
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }, 1500);
        break;
      case 'SUSPENDED':
        this.showAuthStatusDialog({
          icon: 'suspended',
          title: '账户已暂停',
          description: '您的商家账户已被暂停，请联系客服处理',
          buttonText: '联系客服',
          action: 'contactService'
        });
        break;
      default:
        // 默认跳转到首页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }, 1500);
    }
  },

  // 显示认证状态对话框
  showAuthStatusDialog(status) {
    this.setData({
      showAuthStatus: true,
      authStatus: status
    });
  },

  // 隐藏认证状态对话框
  hideAuthStatus() {
    this.setData({
      showAuthStatus: false
    });
  },

  // 跳转到认证页面
  goToAuth() {
    this.hideAuthStatus();
    wx.navigateTo({
      url: '/pages/auth/auth'
    });
  },

  // 保存密码
  savePassword() {
    try {
      wx.setStorageSync('merchant_username', this.data.username);
      wx.setStorageSync('merchant_password', this.data.password);
      wx.setStorageSync('merchant_remember_password', true);
    } catch (error) {
      console.error('保存密码失败:', error);
    }
  },

  // 清除保存的密码
  clearSavedPassword() {
    try {
      wx.removeStorageSync('merchant_username');
      wx.removeStorageSync('merchant_password');
      wx.removeStorageSync('merchant_remember_password');
    } catch (error) {
      console.error('清除密码失败:', error);
    }
  },

  // 微信登录
  onWechatLogin(e) {
    if (e.detail.userInfo) {
      wx.login({
        success: (res) => {
          if (res.code) {
            app.request({
              url: '/auth/merchant/wechat-login',
              method: 'POST',
              data: {
                code: res.code,
                userInfo: e.detail.userInfo,
                userType: 'MERCHANT'
              }
            }).then(result => {
              if (result.success) {
                app.login(result.data.userInfo, result.data.token, 'MERCHANT');

                wx.showToast({
                  title: '登录成功',
                  icon: 'success'
                });

                this.checkMerchantStatus(result.data.userInfo);
              } else {
                wx.showToast({
                  title: result.message || '微信登录失败',
                  icon: 'none'
                });
              }
            }).catch(err => {
              wx.showToast({
                title: '微信登录失败',
                icon: 'none'
              });
            });
          }
        }
      });
    }
  },

  // 忘记密码
  onForgotPassword() {
    wx.navigateTo({
      url: '/pages/forgot-password/forgot-password'
    });
  },

  // 注册
  onRegister() {
    wx.navigateTo({
      url: '/pages/register/register'
    });
  },

  // 显示用户协议
  showUserAgreement() {
    this.setData({
      showAgreementModal: true,
      modalTitle: '商家服务协议',
      agreementContent: `
欢迎使用洗护服务商家管理系统！

1. 服务说明
本系统为商家提供专业的洗护服务管理功能，包括订单管理、服务管理、财务管理等。

2. 商家权利
- 发布和管理洗护服务
- 接收和处理用户订单
- 查看营业数据和财务报表
- 与用户进行沟通交流

3. 商家义务
- 提供真实有效的商家信息和资质
- 按时完成用户订单
- 保证服务质量
- 遵守平台运营规范

4. 费用说明
平台将按照约定比例收取服务费用，具体费率请查看费率说明。

5. 违约责任
如违反协议规定，平台有权暂停或终止服务。

如有疑问，请联系客服：400-123-4567
      `
    });
  },

  // 显示隐私政策
  showPrivacyPolicy() {
    this.setData({
      showAgreementModal: true,
      modalTitle: '隐私政策',
      agreementContent: `
我们非常重视您的隐私保护！

1. 信息收集
我们会收集您提供的商家信息、联系方式等必要信息。

2. 信息使用
收集的信息仅用于提供服务、改善用户体验、安全保障等目的。

3. 信息保护
我们采用行业标准的安全措施保护您的个人信息。

4. 信息共享
除法律要求外，我们不会向第三方分享您的个人信息。

5. 权利保障
您有权查看、修改、删除个人信息。

如有隐私相关问题，请联系我们：<EMAIL>
      `
    });
  },

  // 隐藏协议弹窗
  hideAgreementModal() {
    this.setData({
      showAgreementModal: false
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  }
});