<!--商家端登录页面-->
<view class="container">
  <!-- 顶部logo区域 -->
  <view class="header">
    <image class="logo" src="/images/merchant-logo.png" mode="aspectFit"></image>
    <text class="title">商家管理系统</text>
    <text class="subtitle">专业的洗护服务管理平台</text>
  </view>

  <!-- 登录表单 -->
  <view class="login-form">
    <view class="form-title">
      <text class="title-text">商家登录</text>
      <text class="subtitle-text">请使用商家账号登录</text>
    </view>

    <!-- 账号密码登录 -->
    <view class="form-content">
      <view class="input-group">
        <view class="input-label">
          <image class="label-icon" src="/images/user-icon.png"></image>
          <text class="label-text">账号</text>
        </view>
        <input
          class="input-field"
          placeholder="请输入商家账号/手机号"
          value="{{username}}"
          bindinput="onUsernameInput"
          type="text"
          maxlength="20"
        />
      </view>

      <view class="input-group">
        <view class="input-label">
          <image class="label-icon" src="/images/lock-icon.png"></image>
          <text class="label-text">密码</text>
        </view>
        <view class="password-input">
          <input
            class="input-field"
            placeholder="请输入登录密码"
            value="{{password}}"
            bindinput="onPasswordInput"
            password="{{!showPassword}}"
            maxlength="20"
          />
          <view class="password-toggle" bindtap="togglePassword">
            <image
              class="toggle-icon"
              src="{{showPassword ? '/images/eye-open.png' : '/images/eye-close.png'}}"
            ></image>
          </view>
        </view>
      </view>

      <!-- 记住密码和忘记密码 -->
      <view class="form-options">
        <view class="remember-password" bindtap="toggleRemember">
          <checkbox-group>
            <checkbox value="remember" checked="{{rememberPassword}}" color="#ff6b35"/>
          </checkbox-group>
          <text class="option-text">记住密码</text>
        </view>
        <text class="forgot-password" bindtap="onForgotPassword">忘记密码？</text>
      </view>

      <!-- 登录按钮 -->
      <button
        class="login-btn {{canLogin ? 'active' : 'disabled'}}"
        bindtap="onLogin"
        loading="{{loginLoading}}"
        disabled="{{!canLogin || loginLoading}}"
      >
        {{loginLoading ? '登录中...' : '登录'}}
      </button>

      <!-- 其他登录方式 -->
      <view class="other-login">
        <view class="divider">
          <text class="divider-text">其他登录方式</text>
        </view>

        <!-- 微信登录 -->
        <button
          class="wechat-login-btn"
          open-type="getUserInfo"
          bindgetuserinfo="onWechatLogin"
        >
          <image class="wechat-icon" src="/images/wechat-icon.png"></image>
          <text class="wechat-text">微信快速登录</text>
        </button>
      </view>

      <!-- 注册链接 -->
      <view class="register-section">
        <text class="register-text">还没有商家账号？</text>
        <text class="register-link" bindtap="onRegister">立即注册</text>
      </view>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="footer">
    <view class="service-info">
      <text class="service-text">客服热线：400-123-4567</text>
      <text class="service-time">服务时间：7×24小时</text>
    </view>

    <view class="agreement">
      <text class="agreement-text">
        登录即表示同意
        <text class="link" bindtap="showUserAgreement">《商家服务协议》</text>
        和
        <text class="link" bindtap="showPrivacyPolicy">《隐私政策》</text>
      </text>
    </view>
  </view>

  <!-- 认证状态提示 -->
  <view class="auth-status" wx:if="{{showAuthStatus}}">
    <view class="status-content">
      <image class="status-icon" src="/images/{{authStatus.icon}}.png"></image>
      <text class="status-title">{{authStatus.title}}</text>
      <text class="status-desc">{{authStatus.description}}</text>
      <button class="status-btn" bindtap="{{authStatus.action}}">{{authStatus.buttonText}}</button>
    </view>
  </view>
</view>

<!-- 协议弹窗 -->
<view class="modal" wx:if="{{showAgreementModal}}" bindtap="hideAgreementModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">{{modalTitle}}</text>
      <image class="close-btn" src="/images/close.png" bindtap="hideAgreementModal"></image>
    </view>
    <scroll-view class="modal-body" scroll-y>
      <text class="agreement-content">{{agreementContent}}</text>
    </scroll-view>
  </view>
</view>