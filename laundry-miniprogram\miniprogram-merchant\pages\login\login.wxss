/* 商家端登录页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  padding: 0 40rpx;
  display: flex;
  flex-direction: column;
}

/* 顶部logo区域 */
.header {
  text-align: center;
  padding: 80rpx 0 60rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 登录表单 */
.login-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.form-title {
  text-align: center;
  margin-bottom: 60rpx;
}

.title-text {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle-text {
  display: block;
  font-size: 26rpx;
  color: #666;
}

/* 表单内容 */
.form-content {
  width: 100%;
}

.input-group {
  margin-bottom: 40rpx;
}

.input-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.label-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.input-field {
  width: 100%;
  height: 88rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 30rpx;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.input-field:focus {
  border-color: #ff6b35;
  background: #ffffff;
  box-shadow: 0 0 0 4rpx rgba(255, 107, 53, 0.1);
}

.input-field::placeholder {
  color: #999;
}

/* 密码输入框 */
.password-input {
  position: relative;
  display: flex;
  align-items: center;
}

.password-toggle {
  position: absolute;
  right: 30rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 50rpx;
}

.remember-password {
  display: flex;
  align-items: center;
}

.option-text {
  font-size: 26rpx;
  color: #666;
  margin-left: 10rpx;
}

.forgot-password {
  font-size: 26rpx;
  color: #ff6b35;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
  transition: all 0.3s ease;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-btn.active {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: #ffffff;
  box-shadow: 0 8rpx 20rpx rgba(255, 107, 53, 0.3);
}

.login-btn.disabled {
  background: #cccccc;
  color: #999999;
}

.login-btn.active:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(255, 107, 53, 0.3);
}

/* 其他登录方式 */
.other-login {
  margin-bottom: 40rpx;
}

.divider {
  text-align: center;
  margin: 40rpx 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2rpx;
  background: #e9ecef;
}

.divider-text {
  background: rgba(255, 255, 255, 0.95);
  color: #999;
  padding: 0 20rpx;
  font-size: 24rpx;
  position: relative;
  z-index: 1;
}

.wechat-login-btn {
  width: 100%;
  height: 88rpx;
  background: #ffffff;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.wechat-login-btn:active {
  background: #f8f9fa;
  transform: translateY(2rpx);
}

.wechat-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

/* 注册区域 */
.register-section {
  text-align: center;
}

.register-text {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.register-link {
  font-size: 26rpx;
  color: #ff6b35;
  font-weight: 500;
}

/* 底部信息 */
.footer {
  margin-top: auto;
  padding-bottom: 60rpx;
}

.service-info {
  text-align: center;
  margin-bottom: 30rpx;
}

.service-text,
.service-time {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
}

.agreement {
  text-align: center;
}

.agreement-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
}

.link {
  color: #ffffff;
  text-decoration: underline;
}

/* 认证状态弹窗 */
.auth-status {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.status-content {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin: 0 40rpx;
  text-align: center;
  max-width: 600rpx;
}

.status-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.status-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.status-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.status-btn {
  background: #ff6b35;
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
}

/* 协议弹窗 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #ffffff;
  border-radius: 20rpx;
  margin: 40rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  width: 40rpx;
  height: 40rpx;
}

.modal-body {
  padding: 40rpx;
  flex: 1;
  max-height: 60vh;
}

.agreement-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  white-space: pre-line;
}

/* 动画效果 */
.login-form {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.header {
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-height: 600px) {
  .header {
    padding: 60rpx 0 40rpx;
  }
  
  .logo {
    width: 100rpx;
    height: 100rpx;
  }
  
  .title {
    font-size: 40rpx;
  }
}
