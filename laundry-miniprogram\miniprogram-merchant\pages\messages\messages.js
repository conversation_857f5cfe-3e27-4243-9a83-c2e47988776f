const app = getApp();
const { messageAPI } = require('../../utils/api.js');

Page({
  data: {
    currentTab: 0,
    tabs: [
      { key: 'all', name: '全部消息' },
      { key: 'system', name: '系统通知' },
      { key: 'order', name: '订单消息' },
      { key: 'user', name: '用户消息' }
    ],
    messages: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 20,
    unreadCount: 0
  },

  onLoad() {
    this.loadMessages();
    this.loadUnreadCount();
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
    
    // 刷新消息列表
    this.resetAndLoad();
  },

  // Tab切换
  onTabChange(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentTab: index
    });
    this.resetAndLoad();
  },

  // 重置并加载
  resetAndLoad() {
    this.setData({
      messages: [],
      page: 1,
      hasMore: true
    });
    this.loadMessages();
  },

  // 加载消息列表
  async loadMessages() {
    if (this.data.loading || !this.data.hasMore) return;

    try {
      this.setData({ loading: true });

      const currentTab = this.data.tabs[this.data.currentTab];
      const params = {
        page: this.data.page,
        pageSize: this.data.pageSize,
        type: currentTab.key === 'all' ? undefined : currentTab.key
      };

      const result = await messageAPI.getMessages(params);
      const newMessages = result.list || result || [];
      const messages = this.data.page === 1 ? newMessages : [...this.data.messages, ...newMessages];

      this.setData({
        messages,
        hasMore: newMessages.length === this.data.pageSize,
        page: this.data.page + 1,
        loading: false
      });

    } catch (error) {
      console.error('加载消息失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    }
  },

  // 加载未读数量
  async loadUnreadCount() {
    try {
      const result = await messageAPI.getUnreadCount();
      this.setData({
        unreadCount: result.count || 0
      });
    } catch (error) {
      console.error('加载未读数量失败:', error);
    }
  },

  // 消息点击
  async onMessageTap(e) {
    const message = e.currentTarget.dataset.item;
    
    // 标记为已读
    if (!message.isRead) {
      try {
        await messageAPI.markAsRead(message.id);
        
        // 更新本地数据
        const messages = this.data.messages.map(msg => 
          msg.id === message.id ? { ...msg, isRead: true } : msg
        );
        this.setData({ messages });
        
        // 更新未读数量
        this.loadUnreadCount();
      } catch (error) {
        console.error('标记已读失败:', error);
      }
    }

    // 根据消息类型跳转
    this.handleMessageNavigation(message);
  },

  // 处理消息导航
  handleMessageNavigation(message) {
    switch (message.type) {
      case 'order':
        if (message.orderId) {
          wx.navigateTo({
            url: `/pages/order-detail/order-detail?id=${message.orderId}`
          });
        }
        break;
      case 'user':
        if (message.userId) {
          wx.navigateTo({
            url: `/pages/chat/chat?userId=${message.userId}`
          });
        }
        break;
      case 'system':
        this.showMessageDetail(message);
        break;
      default:
        this.showMessageDetail(message);
    }
  },

  // 显示消息详情
  showMessageDetail(message) {
    wx.showModal({
      title: message.title,
      content: message.content,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 全部标记为已读
  async onMarkAllRead() {
    try {
      wx.showLoading({
        title: '处理中...'
      });

      await messageAPI.markAllAsRead();

      // 更新本地数据
      const messages = this.data.messages.map(msg => ({ ...msg, isRead: true }));
      this.setData({
        messages,
        unreadCount: 0
      });

      wx.hideLoading();
      wx.showToast({
        title: '已全部标记为已读',
        icon: 'success'
      });

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      });
    }
  },

  // 删除消息
  async onDeleteMessage(e) {
    const message = e.currentTarget.dataset.item;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条消息吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({
              title: '删除中...'
            });

            await messageAPI.deleteMessage(message.id);

            // 从列表中移除
            const messages = this.data.messages.filter(msg => msg.id !== message.id);
            this.setData({ messages });

            wx.hideLoading();
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });

          } catch (error) {
            wx.hideLoading();
            wx.showToast({
              title: error.message || '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 清空消息
  onClearMessages() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有消息吗？此操作不可恢复。',
      confirmColor: '#ff4d4f',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({
              title: '清空中...'
            });

            const currentTab = this.data.tabs[this.data.currentTab];
            await messageAPI.clearMessages(currentTab.key === 'all' ? undefined : currentTab.key);

            this.setData({
              messages: [],
              unreadCount: 0
            });

            wx.hideLoading();
            wx.showToast({
              title: '清空成功',
              icon: 'success'
            });

          } catch (error) {
            wx.hideLoading();
            wx.showToast({
              title: error.message || '清空失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.resetAndLoad();
    this.loadUnreadCount();
    wx.stopPullDownRefresh();
  },

  // 触底加载
  onReachBottom() {
    this.loadMessages();
  }
});
