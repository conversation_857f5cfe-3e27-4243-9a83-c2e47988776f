const app = getApp();
const { orderAPI } = require('../../utils/api.js');

Page({
  data: {
    orderId: null,
    order: {},
    statusSteps: [],
    loading: true,
    showRejectModal: false,
    rejectReason: '',
    rejectReasons: [
      '服务时间不合适',
      '服务地址太远',
      '暂时无法提供服务',
      '价格有误',
      '其他原因'
    ],
    showCompleteModal: false,
    completionImages: [],
    completionNote: ''
  },

  onLoad(options) {
    if (options.id) {
      this.setData({
        orderId: options.id
      });
      this.loadOrderDetail();
    }
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    // 刷新订单详情
    if (this.data.orderId) {
      this.loadOrderDetail();
    }
  },

  // 加载订单详情
  async loadOrderDetail() {
    try {
      this.setData({ loading: true });

      const order = await orderAPI.getOrderDetail(this.data.orderId);
      const statusSteps = this.generateStatusSteps(order);

      this.setData({
        order,
        statusSteps,
        loading: false
      });

    } catch (error) {
      console.error('加载订单详情失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    }
  },

  // 生成状态步骤
  generateStatusSteps(order) {
    const steps = [
      { title: '用户下单', desc: '用户已提交订单', completed: true },
      { title: '商家接单', desc: '等待商家确认', completed: false },
      { title: '服务进行中', desc: '正在提供服务', completed: false },
      { title: '服务完成', desc: '服务已完成', completed: false }
    ];

    switch (order.status) {
      case 'PENDING':
        steps[0].completed = true;
        break;
      case 'ACCEPTED':
        steps[0].completed = true;
        steps[1].completed = true;
        break;
      case 'IN_PROGRESS':
        steps[0].completed = true;
        steps[1].completed = true;
        steps[2].completed = true;
        break;
      case 'COMPLETED':
        steps.forEach(step => step.completed = true);
        break;
    }

    return steps;
  },

  // 联系用户
  onContactUser() {
    wx.navigateTo({
      url: `/pages/chat/chat?userId=${this.data.order.userId}&orderId=${this.data.orderId}`
    });
  },

  // 接受订单
  async onAcceptOrder() {
    wx.showModal({
      title: '确认接单',
      content: '确定要接受这个订单吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({
              title: '处理中...'
            });

            await orderAPI.acceptOrder(this.data.orderId);

            wx.hideLoading();
            wx.showToast({
              title: '接单成功',
              icon: 'success'
            });

            // 刷新订单详情
            this.loadOrderDetail();

          } catch (error) {
            wx.hideLoading();
            wx.showToast({
              title: error.message || '接单失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 拒绝订单
  onRejectOrder() {
    this.setData({
      showRejectModal: true,
      rejectReason: ''
    });
  },

  // 关闭拒绝弹窗
  onCloseRejectModal() {
    this.setData({
      showRejectModal: false
    });
  },

  // 选择拒绝原因
  onRejectReasonSelect(e) {
    const reason = e.currentTarget.dataset.reason;
    this.setData({
      rejectReason: reason
    });
  },

  // 确认拒绝订单
  async onConfirmReject() {
    if (!this.data.rejectReason) {
      wx.showToast({
        title: '请选择拒绝原因',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({
        title: '处理中...'
      });

      await orderAPI.rejectOrder(this.data.orderId, this.data.rejectReason);

      wx.hideLoading();
      wx.showToast({
        title: '订单已拒绝',
        icon: 'success'
      });

      this.setData({
        showRejectModal: false
      });

      // 刷新订单详情
      this.loadOrderDetail();

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      });
    }
  },

  // 开始服务
  async onStartService() {
    wx.showModal({
      title: '开始服务',
      content: '确认开始提供服务？',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({
              title: '处理中...'
            });

            await orderAPI.startService(this.data.orderId);

            wx.hideLoading();
            wx.showToast({
              title: '服务已开始',
              icon: 'success'
            });

            // 刷新订单详情
            this.loadOrderDetail();

          } catch (error) {
            wx.hideLoading();
            wx.showToast({
              title: error.message || '操作失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 完成服务
  onCompleteService() {
    this.setData({
      showCompleteModal: true,
      completionImages: [],
      completionNote: ''
    });
  },

  // 关闭完成弹窗
  onCloseCompleteModal() {
    this.setData({
      showCompleteModal: false
    });
  },

  // 选择完成图片
  onChooseCompletionImages() {
    wx.chooseImage({
      count: 3 - this.data.completionImages.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const images = [...this.data.completionImages, ...res.tempFilePaths];
        this.setData({
          completionImages: images
        });
      }
    });
  },

  // 删除完成图片
  onDeleteCompletionImage(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.completionImages.filter((_, i) => i !== index);
    this.setData({
      completionImages: images
    });
  },

  // 完成备注输入
  onCompletionNoteInput(e) {
    this.setData({
      completionNote: e.detail.value
    });
  },

  // 确认完成服务
  async onConfirmComplete() {
    try {
      wx.showLoading({
        title: '提交中...'
      });

      await orderAPI.completeService(this.data.orderId, {
        images: this.data.completionImages,
        note: this.data.completionNote
      });

      wx.hideLoading();
      wx.showToast({
        title: '服务已完成',
        icon: 'success'
      });

      this.setData({
        showCompleteModal: false
      });

      // 刷新订单详情
      this.loadOrderDetail();

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '提交失败',
        icon: 'none'
      });
    }
  },

  // 查看地图导航
  onViewMap() {
    const { address } = this.data.order;
    if (address && address.latitude && address.longitude) {
      wx.openLocation({
        latitude: address.latitude,
        longitude: address.longitude,
        name: address.name,
        address: address.detail
      });
    } else {
      wx.showToast({
        title: '地址信息不完整',
        icon: 'none'
      });
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadOrderDetail();
    wx.stopPullDownRefresh();
  }
});