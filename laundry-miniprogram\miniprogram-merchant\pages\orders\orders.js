// 商家端订单管理页面
const app = getApp();
const { orderAPI } = require('../../utils/api.js');

Page({
  data: {
    // 订单统计
    orderStats: {
      pending: 0,
      processing: 0,
      completed: 0,
      total: 0
    },

    // 状态标签
    statusTabs: [
      { label: '全部', value: 'ALL', count: 0 },
      { label: '待处理', value: 'PENDING', count: 0 },
      { label: '已接单', value: 'ACCEPTED', count: 0 },
      { label: '进行中', value: 'IN_PROGRESS', count: 0 },
      { label: '已完成', value: 'COMPLETED', count: 0 },
      { label: '已取消', value: 'CANCELLED', count: 0 }
    ],

    // 当前状态
    currentStatus: 'ALL',

    // 订单列表
    orders: [],

    // 分页参数
    page: 1,
    pageSize: 10,
    hasMore: true,

    // 加载状态
    loading: false,
    refreshing: false,
    loadingMore: false,

    // 拒绝订单相关
    showRejectModal: false,
    currentRejectOrder: null,
    rejectReason: '',
    customRejectReason: '',
    rejectReasons: [
      '商品缺货',
      '服务时间冲突',
      '超出服务范围',
      '设备故障',
      '其他原因'
    ]
  },

  onLoad() {
    this.loadOrderStats();
    this.loadOrders();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadOrderStats();
    this.loadOrders();
  },

  // 加载订单统计
  async loadOrderStats() {
    try {
      const stats = await orderAPI.getOrderStats();

      // 更新统计数据
      this.setData({
        orderStats: stats,
        'statusTabs[1].count': stats.pending,
        'statusTabs[2].count': stats.accepted,
        'statusTabs[3].count': stats.inProgress,
        'statusTabs[4].count': stats.completed,
        'statusTabs[5].count': stats.cancelled
      });

    } catch (error) {
      console.error('加载订单统计失败:', error);
    }
  },

  // 加载订单列表
  async loadOrders(refresh = false) {
    if (this.data.loading && !refresh) return;

    try {
      if (refresh) {
        this.setData({
          refreshing: true,
          page: 1,
          hasMore: true
        });
      } else {
        this.setData({ loading: true });
      }

      const params = {
        page: refresh ? 1 : this.data.page,
        pageSize: this.data.pageSize,
        status: this.data.currentStatus === 'ALL' ? '' : this.data.currentStatus
      };

      const result = await orderAPI.getOrders(params);

      const orders = result.list.map(order => ({
        ...order,
        statusText: this.getStatusText(order.status),
        createTime: this.formatTime(order.createTime)
      }));

      this.setData({
        orders: refresh ? orders : [...this.data.orders, ...orders],
        hasMore: result.hasMore,
        page: refresh ? 2 : this.data.page + 1
      });

    } catch (error) {
      console.error('加载订单列表失败:', error);
      app.showError(error.message || '加载订单失败');
    } finally {
      this.setData({
        loading: false,
        refreshing: false,
        loadingMore: false
      });
    }
  },

  // 状态切换
  onStatusChange(e) {
    const status = e.currentTarget.dataset.status;
    if (status === this.data.currentStatus) return;

    this.setData({
      currentStatus: status,
      orders: [],
      page: 1,
      hasMore: true
    });

    this.loadOrders();
  },

  // 下拉刷新
  onRefresh() {
    this.loadOrderStats();
    this.loadOrders(true);
  },

  // 加载更多
  onLoadMore() {
    if (this.data.hasMore && !this.data.loadingMore) {
      this.setData({ loadingMore: true });
      this.loadOrders();
    }
  },

  // 接受订单
  async onAcceptOrder(e) {
    const order = e.currentTarget.dataset.order;

    try {
      wx.showModal({
        title: '确认接单',
        content: `确定接受订单 ${order.orderNo} 吗？`,
        success: async (res) => {
          if (res.confirm) {
            app.showLoading('处理中...');

            await orderAPI.acceptOrder(order.id);

            app.hideLoading();
            app.showSuccess('接单成功');

            // 刷新列表
            this.loadOrderStats();
            this.loadOrders(true);
          }
        }
      });
    } catch (error) {
      app.hideLoading();
      app.showError(error.message || '接单失败');
    }
  },

  // 拒绝订单
  onRejectOrder(e) {
    const order = e.currentTarget.dataset.order;
    this.setData({
      showRejectModal: true,
      currentRejectOrder: order,
      rejectReason: '',
      customRejectReason: ''
    });
  },

  // 关闭拒绝弹窗
  onCloseRejectModal() {
    this.setData({
      showRejectModal: false,
      currentRejectOrder: null,
      rejectReason: '',
      customRejectReason: ''
    });
  },

  // 拒绝原因选择
  onRejectReasonChange(e) {
    this.setData({
      rejectReason: e.detail.value
    });
  },

  // 自定义原因输入
  onCustomReasonInput(e) {
    this.setData({
      customRejectReason: e.detail.value
    });
  },

  // 确认拒绝
  async onConfirmReject() {
    const { rejectReason, customRejectReason, currentRejectOrder } = this.data;

    if (!rejectReason) {
      app.showError('请选择拒绝原因');
      return;
    }

    if (rejectReason === '其他原因' && !customRejectReason.trim()) {
      app.showError('请输入具体原因');
      return;
    }

    try {
      app.showLoading('处理中...');

      const reason = rejectReason === '其他原因' ? customRejectReason : rejectReason;
      await orderAPI.rejectOrder(currentRejectOrder.id, reason);

      app.hideLoading();
      app.showSuccess('已拒绝订单');

      this.onCloseRejectModal();

      // 刷新列表
      this.loadOrderStats();
      this.loadOrders(true);

    } catch (error) {
      app.hideLoading();
      app.showError(error.message || '拒绝订单失败');
    }
  },

  // 开始服务
  async onStartOrder(e) {
    const order = e.currentTarget.dataset.order;

    try {
      wx.showModal({
        title: '开始服务',
        content: `确定开始为订单 ${order.orderNo} 提供服务吗？`,
        success: async (res) => {
          if (res.confirm) {
            app.showLoading('处理中...');

            await orderAPI.startOrder(order.id);

            app.hideLoading();
            app.showSuccess('已开始服务');

            // 刷新列表
            this.loadOrderStats();
            this.loadOrders(true);
          }
        }
      });
    } catch (error) {
      app.hideLoading();
      app.showError(error.message || '开始服务失败');
    }
  },

  // 完成订单
  async onCompleteOrder(e) {
    const order = e.currentTarget.dataset.order;

    try {
      wx.showModal({
        title: '完成订单',
        content: `确定完成订单 ${order.orderNo} 吗？`,
        success: async (res) => {
          if (res.confirm) {
            app.showLoading('处理中...');

            await orderAPI.completeOrder(order.id, {
              completionTime: new Date().toISOString(),
              notes: '服务已完成'
            });

            app.hideLoading();
            app.showSuccess('订单已完成');

            // 刷新列表
            this.loadOrderStats();
            this.loadOrders(true);
          }
        }
      });
    } catch (error) {
      app.hideLoading();
      app.showError(error.message || '完成订单失败');
    }
  },

  // 查看订单详情
  onOrderDetail(e) {
    const order = e.currentTarget.dataset.order;
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?orderId=${order.id}`
    });
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'PENDING': '待处理',
      'ACCEPTED': '已接单',
      'REJECTED': '已拒绝',
      'IN_PROGRESS': '进行中',
      'COMPLETED': '已完成',
      'CANCELLED': '已取消'
    };
    return statusMap[status] || status;
  },

  // 格式化时间
  formatTime(timeStr) {
    const date = new Date(timeStr);
    const now = new Date();
    const diff = now - date;

    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 1天内
      return Math.floor(diff / 3600000) + '小时前';
    } else {
      return date.toLocaleDateString();
    }
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  }
});