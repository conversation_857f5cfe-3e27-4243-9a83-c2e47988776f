// 商家端订单管理页面
const app = getApp();
const { orderAPI } = require('../../utils/api.js');

Page({
  data: {
    // 订单统计
    orderStats: {
      pending: 0,
      processing: 0,
      completed: 0,
      total: 0
    },

    // 状态标签
    statusTabs: [
      { label: '全部', value: 'ALL', count: 0 },
      { label: '待处理', value: 'PENDING', count: 0 },
      { label: '已接单', value: 'ACCEPTED', count: 0 },
      { label: '进行中', value: 'IN_PROGRESS', count: 0 },
      { label: '已完成', value: 'COMPLETED', count: 0 },
      { label: '已取消', value: 'CANCELLED', count: 0 }
    ],

    // 当前状态
    currentStatus: 'ALL',

    // 订单列表
    orders: [],

    // 分页参数
    page: 1,
    pageSize: 10,
    hasMore: true,

    // 加载状态
    loading: false,
    refreshing: false,
    loadingMore: false,

    // 拒绝订单相关
    showRejectModal: false,
    currentRejectOrder: null,
    rejectReason: '',
    customRejectReason: '',
    rejectReasons: [
      '商品缺货',
      '服务时间冲突',
      '超出服务范围',
      '设备故障',
      '其他原因'
    ]
  },

  onLoad() {
    this.loadOrderStats();
    this.loadOrders();
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    // 页面显示时刷新数据
    this.loadOrderStats();
    this.loadOrders();
  },

  // 加载订单统计
  async loadOrderStats() {
    try {
      const stats = await orderAPI.getOrderStats();

      // 更新统计数据
      this.setData({
        orderStats: stats,
        'statusTabs[1].count': stats.pending,
        'statusTabs[2].count': stats.accepted,
        'statusTabs[3].count': stats.inProgress,
        'statusTabs[4].count': stats.completed,
        'statusTabs[5].count': stats.cancelled
      });

    } catch (error) {
      console.error('加载订单统计失败:', error);
    }
  },

  // 加载订单列表
  async loadOrders(refresh = false) {
    if (this.data.loading && !refresh) return;

    try {
      if (refresh) {
        this.setData({
          refreshing: true,
          page: 1,
          hasMore: true
        });
      } else {
        this.setData({ loading: true });
      }

      const params = {
        page: refresh ? 1 : this.data.page,
        pageSize: this.data.pageSize,
        status: this.data.currentStatus === 'ALL' ? '' : this.data.currentStatus
      };

      const result = await orderAPI.getOrders(params);

      const orders = result.list.map(order => ({
        ...order,
        statusText: this.getStatusText(order.status),
        createTime: this.formatTime(order.createTime)
      }));

      this.setData({
        orders: refresh ? orders : [...this.data.orders, ...orders],
        hasMore: result.hasMore,
        page: refresh ? 2 : this.data.page + 1
      });

    } catch (error) {
      console.error('加载订单列表失败:', error);
      app.showError(error.message || '加载订单失败');
    } finally {
      this.setData({
        loading: false,
        refreshing: false,
        loadingMore: false
      });
    }
  },

  // 状态切换
  onStatusChange(e) {
    const status = e.currentTarget.dataset.status;
    if (status === this.data.currentStatus) return;

    this.setData({
      currentStatus: status,
      orders: [],
      page: 1,
      hasMore: true
    });

    this.loadOrders();
  },

  // 下拉刷新
  onRefresh() {
    this.loadOrderStats();
    this.loadOrders(true);
  },

  // 加载更多
  onLoadMore() {
    if (this.data.hasMore && !this.data.loadingMore) {
      this.setData({ loadingMore: true });
      this.loadOrders();
    }
  },

  // 接受订单
  async onAcceptOrder(e) {
    const order = e.currentTarget.dataset.order;

    try {
      wx.showModal({
        title: '确认接单',
        content: `确定接受订单 ${order.orderNo} 吗？`,
        success: async (res) => {
          if (res.confirm) {
            app.showLoading('处理中...');

            await orderAPI.acceptOrder(order.id);

            app.hideLoading();
            app.showSuccess('接单成功');

            // 刷新列表
            this.loadOrderStats();
            this.loadOrders(true);
          }
        }
      });
    } catch (error) {
      app.hideLoading();
      app.showError(error.message || '接单失败');
    }
  },

  // 拒绝订单
  onRejectOrder(e) {
    const order = e.currentTarget.dataset.order;
    this.setData({
      showRejectModal: true,
      currentRejectOrder: order,
      rejectReason: '',
      customRejectReason: ''
    });
  },

  // 关闭拒绝弹窗
  onCloseRejectModal() {
    this.setData({
      showRejectModal: false,
      currentRejectOrder: null,
      rejectReason: '',
      customRejectReason: ''
    });
  },

  // 拒绝原因选择
  onRejectReasonChange(e) {
    this.setData({
      rejectReason: e.detail.value
    });
  },

  // 自定义原因输入
  onCustomReasonInput(e) {
    this.setData({
      customRejectReason: e.detail.value
    });
  },

  // 确认拒绝
  async onConfirmReject() {
    const { rejectReason, customRejectReason, currentRejectOrder } = this.data;

    if (!rejectReason) {
      app.showError('请选择拒绝原因');
      return;
    }

    if (rejectReason === '其他原因' && !customRejectReason.trim()) {
      app.showError('请输入具体原因');
      return;
    }

    try {
      app.showLoading('处理中...');

      const reason = rejectReason === '其他原因' ? customRejectReason : rejectReason;
      await orderAPI.rejectOrder(currentRejectOrder.id, reason);

      app.hideLoading();
      app.showSuccess('已拒绝订单');

      this.onCloseRejectModal();

      // 刷新列表
      this.loadOrderStats();
      this.loadOrders(true);

    } catch (error) {
      app.hideLoading();
      app.showError(error.message || '拒绝订单失败');
    }
  },

  // 开始服务
  async onStartOrder(e) {
    const order = e.currentTarget.dataset.order;

    try {
      wx.showModal({
        title: '开始服务',
        content: `确定开始为订单 ${order.orderNo} 提供服务吗？`,
        success: async (res) => {
          if (res.confirm) {
            app.showLoading('处理中...');

            await orderAPI.startOrder(order.id);

            app.hideLoading();
            app.showSuccess('已开始服务');

            // 刷新列表
            this.loadOrderStats();
            this.loadOrders(true);
          }
        }
      });
    } catch (error) {
      app.hideLoading();
      app.showError(error.message || '开始服务失败');
    }
  },

  // 完成订单
  async onCompleteOrder(e) {
    const order = e.currentTarget.dataset.order;

    try {
      wx.showModal({
        title: '完成订单',
        content: `确定完成订单 ${order.orderNo} 吗？`,
        success: async (res) => {
          if (res.confirm) {
            app.showLoading('处理中...');

            await orderAPI.completeOrder(order.id, {
              completionTime: new Date().toISOString(),
              notes: '服务已完成'
            });

            app.hideLoading();
            app.showSuccess('订单已完成');

            // 刷新列表
            this.loadOrderStats();
            this.loadOrders(true);
          }
        }
      });
    } catch (error) {
      app.hideLoading();
      app.showError(error.message || '完成订单失败');
    }
  },

  // 查看订单详情
  onOrderDetail(e) {
    const order = e.currentTarget.dataset.order;
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?orderId=${order.id}`
    });
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'PENDING': '待处理',
      'ACCEPTED': '已接单',
      'REJECTED': '已拒绝',
      'IN_PROGRESS': '进行中',
      'COMPLETED': '已完成',
      'CANCELLED': '已取消'
    };
    return statusMap[status] || status;
  },

  // 格式化时间
  formatTime(timeStr) {
    const date = new Date(timeStr);
    const now = new Date();
    const diff = now - date;

    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 1天内
      return Math.floor(diff / 3600000) + '小时前';
    } else {
      return date.toLocaleDateString();
    }
  },

  // 联系用户
  onContactUser(e) {
    const order = e.currentTarget.dataset.order;
    wx.navigateTo({
      url: `/pages/chat/chat?userId=${order.userId}&orderId=${order.id}`
    });
  },

  // 查看地图
  onViewMap(e) {
    const order = e.currentTarget.dataset.order;
    if (order.address && order.address.latitude && order.address.longitude) {
      wx.openLocation({
        latitude: order.address.latitude,
        longitude: order.address.longitude,
        name: order.address.name,
        address: order.address.detail
      });
    } else {
      wx.showToast({
        title: '地址信息不完整',
        icon: 'none'
      });
    }
  },

  // 拨打电话
  onCallUser(e) {
    const order = e.currentTarget.dataset.order;
    if (order.contactPhone) {
      wx.makePhoneCall({
        phoneNumber: order.contactPhone
      });
    } else {
      wx.showToast({
        title: '无联系电话',
        icon: 'none'
      });
    }
  },

  // 批量操作
  onBatchOperation() {
    wx.showActionSheet({
      itemList: ['批量接单', '批量拒绝', '导出订单'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.batchAcceptOrders();
            break;
          case 1:
            this.batchRejectOrders();
            break;
          case 2:
            this.exportOrders();
            break;
        }
      }
    });
  },

  // 批量接单
  async batchAcceptOrders() {
    const pendingOrders = this.data.orders.filter(order => order.status === 'PENDING');

    if (pendingOrders.length === 0) {
      wx.showToast({
        title: '没有待处理订单',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '批量接单',
      content: `确定要接受 ${pendingOrders.length} 个待处理订单吗？`,
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({
              title: '处理中...'
            });

            await orderAPI.batchAcceptOrders(pendingOrders.map(order => order.id));

            wx.hideLoading();
            wx.showToast({
              title: '批量接单成功',
              icon: 'success'
            });

            // 刷新列表
            this.loadOrderStats();
            this.loadOrders(true);

          } catch (error) {
            wx.hideLoading();
            wx.showToast({
              title: error.message || '批量接单失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 导出订单
  async exportOrders() {
    try {
      wx.showLoading({
        title: '导出中...'
      });

      const result = await orderAPI.exportOrders({
        status: this.data.currentStatus === 'ALL' ? undefined : this.data.currentStatus,
        startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        endDate: new Date().toISOString().split('T')[0]
      });

      wx.hideLoading();

      if (result.downloadUrl) {
        wx.downloadFile({
          url: result.downloadUrl,
          success: (res) => {
            wx.openDocument({
              filePath: res.tempFilePath,
              success: () => {
                wx.showToast({
                  title: '导出成功',
                  icon: 'success'
                });
              }
            });
          },
          fail: () => {
            wx.showToast({
              title: '下载失败',
              icon: 'none'
            });
          }
        });
      }

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '导出失败',
        icon: 'none'
      });
    }
  },

  // 搜索订单
  onSearchOrder() {
    wx.navigateTo({
      url: '/pages/order-search/order-search'
    });
  },

  // 筛选订单
  onFilterOrder() {
    wx.navigateTo({
      url: '/pages/order-filter/order-filter'
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  }
});