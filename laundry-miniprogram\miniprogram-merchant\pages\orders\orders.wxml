<!-- 商家端订单管理页面 -->
<view class="container">
  <!-- 顶部统计 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{ orderStats.pending || 0 }}</text>
        <text class="stat-label">待处理</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ orderStats.processing || 0 }}</text>
        <text class="stat-label">进行中</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ orderStats.completed || 0 }}</text>
        <text class="stat-label">已完成</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ orderStats.total || 0 }}</text>
        <text class="stat-label">总订单</text>
      </view>
    </view>
  </view>

  <!-- 筛选标签 -->
  <view class="filter-tabs">
    <scroll-view scroll-x="true" class="tabs-scroll">
      <view class="tabs-container">
        <view
          wx:for="{{ statusTabs }}"
          wx:key="value"
          class="tab-item {{ currentStatus === item.value ? 'active' : '' }}"
          bindtap="onStatusChange"
          data-status="{{ item.value }}"
        >
          {{ item.label }}
          <view wx:if="{{ item.count > 0 }}" class="tab-badge">{{ item.count }}</view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 订单列表 -->
  <view class="orders-section">
    <scroll-view
      scroll-y="true"
      class="orders-scroll"
      refresher-enabled="true"
      refresher-triggered="{{ refreshing }}"
      bindrefresherrefresh="onRefresh"
      bindscrolltolower="onLoadMore"
    >
      <view wx:if="{{ loading && orders.length === 0 }}" class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>

      <view wx:elif="{{ orders.length === 0 }}" class="empty-container">
        <image src="/images/empty-orders.png" class="empty-image"></image>
        <text class="empty-text">暂无订单</text>
        <text class="empty-desc">等待客户下单中...</text>
      </view>

      <view wx:else>
        <view
          wx:for="{{ orders }}"
          wx:key="id"
          class="order-card"
          bindtap="onOrderDetail"
          data-order="{{ item }}"
        >
          <!-- 订单头部 -->
          <view class="order-header">
            <view class="order-info">
              <text class="order-no">订单号：{{ item.orderNo }}</text>
              <text class="order-time">{{ item.createTime }}</text>
            </view>
            <view class="order-status {{ item.status }}">
              {{ item.statusText }}
            </view>
          </view>

          <!-- 服务信息 -->
          <view class="service-info">
            <image src="{{ item.serviceImage }}" class="service-image"></image>
            <view class="service-details">
              <text class="service-name">{{ item.serviceName }}</text>
              <text class="service-desc">数量：{{ item.quantity }}{{ item.unit }}</text>
              <text class="service-price">¥{{ item.totalAmount }}</text>
            </view>
          </view>

          <!-- 客户信息 -->
          <view class="customer-info">
            <view class="customer-item">
              <text class="label">客户：</text>
              <text class="value">{{ item.customerName }}</text>
            </view>
            <view class="customer-item">
              <text class="label">电话：</text>
              <text class="value">{{ item.customerPhone }}</text>
            </view>
            <view class="customer-item">
              <text class="label">地址：</text>
              <text class="value">{{ item.address }}</text>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="order-actions">
            <button
              wx:if="{{ item.status === 'PENDING' }}"
              class="action-btn reject-btn"
              bindtap="onRejectOrder"
              data-order="{{ item }}"
            >
              拒绝
            </button>
            <button
              wx:if="{{ item.status === 'PENDING' }}"
              class="action-btn accept-btn"
              bindtap="onAcceptOrder"
              data-order="{{ item }}"
            >
              接单
            </button>
            <button
              wx:if="{{ item.status === 'ACCEPTED' }}"
              class="action-btn start-btn"
              bindtap="onStartOrder"
              data-order="{{ item }}"
            >
              开始服务
            </button>
            <button
              wx:if="{{ item.status === 'IN_PROGRESS' }}"
              class="action-btn complete-btn"
              bindtap="onCompleteOrder"
              data-order="{{ item }}"
            >
              完成订单
            </button>
            <button
              class="action-btn detail-btn"
              bindtap="onOrderDetail"
              data-order="{{ item }}"
            >
              查看详情
            </button>
          </view>
        </view>

        <!-- 加载更多 -->
        <view wx:if="{{ hasMore }}" class="load-more">
          <view wx:if="{{ loadingMore }}" class="loading-more">
            <view class="loading-spinner small"></view>
            <text>加载更多...</text>
          </view>
          <text wx:else class="load-more-text">上拉加载更多</text>
        </view>

        <view wx:else class="no-more">
          <text>没有更多订单了</text>
        </view>
      </view>
    </scroll-view>
  </view>
</view>

<!-- 拒绝订单弹窗 -->
<view wx:if="{{ showRejectModal }}" class="modal-overlay" bindtap="onCloseRejectModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">拒绝订单</text>
      <view class="modal-close" bindtap="onCloseRejectModal">×</view>
    </view>
    <view class="modal-body">
      <text class="modal-label">请选择拒绝原因：</text>
      <radio-group bindchange="onRejectReasonChange">
        <label wx:for="{{ rejectReasons }}" wx:key="*this" class="reason-item">
          <radio value="{{ item }}" checked="{{ rejectReason === item }}"/>
          <text>{{ item }}</text>
        </label>
      </radio-group>
      <textarea
        wx:if="{{ rejectReason === '其他原因' }}"
        placeholder="请输入具体原因"
        value="{{ customRejectReason }}"
        bindinput="onCustomReasonInput"
        class="custom-reason-input"
      ></textarea>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel-btn" bindtap="onCloseRejectModal">取消</button>
      <button class="modal-btn confirm-btn" bindtap="onConfirmReject">确认拒绝</button>
    </view>
  </view>
</view>