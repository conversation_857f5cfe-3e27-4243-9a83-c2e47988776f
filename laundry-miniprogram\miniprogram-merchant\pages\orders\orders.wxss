/* 商家端订单管理页面样式 */
.container {
  background: #f5f5f5;
  min-height: 100vh;
}

/* 标签栏样式 */
.van-tabs {
  background: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.van-tab {
  font-size: 28rpx;
  font-weight: 500;
}

.van-tab--active {
  color: #ff6b35;
  font-weight: bold;
}

.van-tabs__line {
  background: #ff6b35;
  height: 4rpx;
  border-radius: 2rpx;
}

/* 订单列表 */
.order-list {
  padding: 20rpx;
}

.order-item {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.order-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background: #ff6b35;
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.order-number {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.order-status.pending {
  background: rgba(250, 140, 22, 0.1);
  color: #fa8c16;
}

.order-status.accepted {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.order-status.processing {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.order-status.completed {
  background: rgba(82, 196, 26, 0.2);
  color: #389e0d;
}

.order-status.cancelled {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

/* 客户信息 */
.customer-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.customer-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
}

.customer-details {
  flex: 1;
}

.customer-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.customer-phone {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.contact-btn {
  background: #ff6b35;
  color: #ffffff;
  padding: 16rpx 24rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  border: none;
}

/* 服务信息 */
.service-info {
  margin-bottom: 20rpx;
}

.service-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.service-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.service-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.service-details {
  flex: 1;
}

.service-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.service-spec {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.service-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-price {
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: bold;
}

.service-qty {
  font-size: 24rpx;
  color: #999;
}

/* 地址信息 */
.address-info {
  background: #f0f8ff;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  border-left: 4rpx solid #1890ff;
}

.address-contact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.contact-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.contact-phone {
  font-size: 26rpx;
  color: #1890ff;
}

.address-detail {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 订单金额 */
.order-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #fff7e6;
  border-radius: 12rpx;
  border-left: 4rpx solid #faad14;
}

.amount-label {
  font-size: 28rpx;
  color: #333;
}

.amount-value {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 订单操作 */
.order-actions {
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #f5f5f5;
}

.action-btn {
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  border: none;
  min-width: 120rpx;
  transition: all 0.3s ease;
}

.action-btn.reject {
  background: #ffffff;
  color: #ff4d4f;
  border: 2rpx solid #ff4d4f;
}

.action-btn.accept {
  background: #52c41a;
  color: #ffffff;
}

.action-btn.complete {
  background: #1890ff;
  color: #ffffff;
}

.action-btn.contact {
  background: #faad14;
  color: #ffffff;
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.empty-btn {
  background: #ff6b35;
  color: #ffffff;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
  margin-left: 20rpx;
}

/* 动画效果 */
.order-item {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 375px) {
  .order-list {
    padding: 15rpx;
  }
  
  .order-item {
    padding: 25rpx;
    margin-bottom: 15rpx;
  }
  
  .customer-info {
    padding: 15rpx;
  }
  
  .service-image {
    width: 80rpx;
    height: 80rpx;
  }
  
  .action-btn {
    padding: 12rpx 24rpx;
    font-size: 24rpx;
    min-width: 100rpx;
  }
}
