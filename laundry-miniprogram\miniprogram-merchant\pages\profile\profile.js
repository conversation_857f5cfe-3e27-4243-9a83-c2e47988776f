// 商家端个人中心页面
const app = getApp();
const { merchantAPI, dashboardAPI } = require('../../utils/api');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 商家信息
    merchantInfo: {
      id: null,
      username: '',
      merchantName: '',
      contactName: '',
      phone: '',
      email: '',
      logo: '',
      status: '',
      statusText: '',
      isOnline: false,
      address: '',
      description: ''
    },

    // 统计数据
    stats: {
      totalOrders: 0,
      totalEarnings: '0.00',
      rating: '0.0',
      serviceCount: 0
    },

    // 未读消息数量
    unreadCount: 0,
    loading: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadMerchantInfo();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    // 每次显示页面时刷新数据
    this.loadStats();
    this.loadUnreadCount();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadMerchantInfo();
    this.loadStats();
    this.loadUnreadCount();

    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 加载商家信息
   */
  async loadMerchantInfo() {
    try {
      this.setData({ loading: true });

      // 从本地存储获取商家信息
      const merchantInfo = app.globalData.merchantInfo || wx.getStorageSync('merchantInfo');
      if (merchantInfo) {
        this.setData({
          merchantInfo: {
            ...this.data.merchantInfo,
            ...merchantInfo,
            statusText: this.getStatusText(merchantInfo.status)
          }
        });
      }

      // 从服务器获取最新信息
      const serverMerchantInfo = await merchantAPI.getProfile();
      this.setData({
        merchantInfo: {
          ...serverMerchantInfo,
          statusText: this.getStatusText(serverMerchantInfo.status)
        }
      });

      // 更新全局和本地存储
      app.globalData.merchantInfo = serverMerchantInfo;
      wx.setStorageSync('merchantInfo', serverMerchantInfo);

      // 加载其他数据
      this.loadStats();
      this.loadUnreadCount();

    } catch (error) {
      console.error('加载商家信息失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 加载统计数据
   */
  async loadStats() {
    try {
      const stats = await dashboardAPI.getDashboardOverview();
      this.setData({
        stats: {
          totalOrders: stats.totalOrders || 0,
          totalEarnings: stats.totalEarnings || '0.00',
          rating: stats.rating || '0.0',
          serviceCount: stats.serviceCount || 0
        }
      });
    } catch (error) {
      console.error('加载统计数据失败:', error);
      // 使用默认数据
      this.setData({
        stats: {
          totalOrders: 0,
          totalEarnings: '0.00',
          rating: '0.0',
          serviceCount: 0
        }
      });
    }
  },

  /**
   * 加载未读消息数量
   */
  async loadUnreadCount() {
    try {
      const result = await merchantAPI.getUnreadCount();
      this.setData({
        unreadCount: result.count || 0
      });
    } catch (error) {
      console.error('加载未读消息数量失败:', error);
      // 使用默认数据
      this.setData({
        unreadCount: 0
      });
    }
  },

  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const statusMap = {
      'PENDING': '待审核',
      'APPROVED': '已认证',
      'REJECTED': '已拒绝',
      'SUSPENDED': '已暂停'
    };
    return statusMap[status] || '未知状态';
  },

  /**
   * 编辑商家资料
   */
  onEditProfile() {
    wx.navigateTo({
      url: '/pages/profile-edit/profile-edit'
    });
  },

  /**
   * 页面导航
   */
  onNavigate(e) {
    const url = e.currentTarget.dataset.url;
    if (url) {
      wx.navigateTo({
        url: url,
        fail: () => {
          wx.showToast({
            title: '页面开发中',
            icon: 'none'
          });
        }
      });
    }
  },

  /**
   * 查看订单
   */
  onViewOrders() {
    wx.switchTab({
      url: '/pages/orders/orders'
    });
  },

  /**
   * 查看收益
   */
  onViewEarnings() {
    wx.navigateTo({
      url: '/pages/finance/finance'
    });
  },

  /**
   * 查看评分
   */
  onViewRating() {
    wx.navigateTo({
      url: '/pages/reviews/reviews'
    });
  },

  /**
   * 查看服务
   */
  onViewServices() {
    wx.navigateTo({
      url: '/pages/services/services'
    });
  },

  /**
   * 切换营业状态
   */
  async onToggleOnline(e) {
    const isOnline = e.detail.value;

    try {
      wx.showLoading({ title: '更新中...' });

      await merchantAPI.updateOnlineStatus(isOnline);

      this.setData({
        'merchantInfo.isOnline': isOnline
      });

      // 更新全局数据
      app.globalData.merchantInfo.isOnline = isOnline;
      wx.setStorageSync('merchantInfo', app.globalData.merchantInfo);

      wx.hideLoading();
      wx.showToast({
        title: isOnline ? '已上线' : '已下线',
        icon: 'success'
      });

    } catch (error) {
      console.error('更新营业状态失败:', error);

      // 恢复开关状态
      this.setData({
        'merchantInfo.isOnline': !isOnline
      });

      wx.hideLoading();
      wx.showToast({
        title: error.message || '更新失败',
        icon: 'none'
      });
    }
  },

  /**
   * 联系客服
   */
  onContactService() {
    wx.makePhoneCall({
      phoneNumber: '************',
      fail: () => {
        wx.showToast({
          title: '拨号失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 关于我们
   */
  onAbout() {
    wx.navigateTo({
      url: '/pages/about/about'
    });
  },

  /**
   * 退出登录
   */
  onLogout() {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储
          wx.clearStorageSync();

          // 跳转到登录页
          wx.reLaunch({
            url: '/pages/login/login'
          });

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  }
})