<!-- 商家端个人中心页面 -->
<view class="container">
  <!-- 头部信息 -->
  <view class="header-section">
    <view class="merchant-info">
      <image src="{{ merchantInfo.logo || '/images/default-merchant.png' }}" class="merchant-avatar"></image>
      <view class="merchant-details">
        <text class="merchant-name">{{ merchantInfo.merchantName || '未设置商家名称' }}</text>
        <text class="merchant-contact">{{ merchantInfo.contactName || '未设置联系人' }}</text>
        <view class="merchant-status">
          <text class="status-text {{ merchantInfo.status }}">
            {{ merchantInfo.statusText || '待审核' }}
          </text>
          <text class="merchant-phone">{{ merchantInfo.phone || '未绑定手机' }}</text>
        </view>
      </view>
      <view class="edit-btn" bindtap="onEditProfile">
        <text>编辑</text>
      </view>
    </view>
  </view>

  <!-- 数据统计 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stat-item" bindtap="onViewOrders">
        <text class="stat-number">{{ stats.totalOrders || 0 }}</text>
        <text class="stat-label">总订单</text>
      </view>
      <view class="stat-item" bindtap="onViewEarnings">
        <text class="stat-number">¥{{ stats.totalEarnings || '0.00' }}</text>
        <text class="stat-label">总收益</text>
      </view>
      <view class="stat-item" bindtap="onViewRating">
        <text class="stat-number">{{ stats.rating || '5.0' }}</text>
        <text class="stat-label">评分</text>
      </view>
      <view class="stat-item" bindtap="onViewServices">
        <text class="stat-number">{{ stats.serviceCount || 0 }}</text>
        <text class="stat-label">服务数</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <!-- 业务管理 -->
    <view class="menu-group">
      <view class="group-title">业务管理</view>
      <view class="menu-item" bindtap="onNavigate" data-url="/pages/services/services">
        <view class="menu-icon">
          <image src="/images/icons/service.png"></image>
        </view>
        <text class="menu-text">服务管理</text>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="onNavigate" data-url="/pages/orders/orders">
        <view class="menu-icon">
          <image src="/images/icons/order.png"></image>
        </view>
        <text class="menu-text">订单管理</text>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="onNavigate" data-url="/pages/finance/finance">
        <view class="menu-icon">
          <image src="/images/icons/finance.png"></image>
        </view>
        <text class="menu-text">财务管理</text>
        <view class="menu-arrow">></view>
      </view>
    </view>

    <!-- 店铺设置 -->
    <view class="menu-group">
      <view class="group-title">店铺设置</view>
      <view class="menu-item" bindtap="onNavigate" data-url="/pages/settings/settings">
        <view class="menu-icon">
          <image src="/images/icons/settings.png"></image>
        </view>
        <text class="menu-text">店铺设置</text>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="onToggleOnline">
        <view class="menu-icon">
          <image src="/images/icons/online.png"></image>
        </view>
        <text class="menu-text">营业状态</text>
        <switch checked="{{ merchantInfo.isOnline }}" bindchange="onToggleOnline" class="online-switch"></switch>
      </view>
    </view>

    <!-- 其他功能 -->
    <view class="menu-group">
      <view class="group-title">其他功能</view>
      <view class="menu-item" bindtap="onNavigate" data-url="/pages/statistics/statistics">
        <view class="menu-icon">
          <image src="/images/icons/chart.png"></image>
        </view>
        <text class="menu-text">数据统计</text>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="onNavigate" data-url="/pages/messages/messages">
        <view class="menu-icon">
          <image src="/images/icons/message.png"></image>
        </view>
        <text class="menu-text">消息中心</text>
        <view wx:if="{{ unreadCount > 0 }}" class="message-badge">{{ unreadCount }}</view>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="onContactService">
        <view class="menu-icon">
          <image src="/images/icons/service-center.png"></image>
        </view>
        <text class="menu-text">客服中心</text>
        <view class="menu-arrow">></view>
      </view>
      <view class="menu-item" bindtap="onAbout">
        <view class="menu-icon">
          <image src="/images/icons/about.png"></image>
        </view>
        <text class="menu-text">关于我们</text>
        <view class="menu-arrow">></view>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section">
    <button class="logout-btn" bindtap="onLogout">退出登录</button>
  </view>
</view>