// 商家端服务管理页面
const app = getApp();
const { serviceAPI } = require('../../utils/api.js');

Page({
  data: {
    searchKeyword: '',
    activeFilter: 'all',
    services: [],
    stats: {
      total: 0,
      active: 0,
      inactive: 0,
      pending: 0
    },
    filterTabs: [
      { label: '全部', value: 'all', count: 0 },
      { label: '上架中', value: 'active', count: 0 },
      { label: '已下架', value: 'inactive', count: 0 },
      { label: '待审核', value: 'pending', count: 0 }
    ],
    refreshing: false,
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    batchMode: false,
    selectedServices: [],
    showActionSheet: false,
    currentServiceId: null
  },

  onLoad() {
    this.loadServices();
    this.loadStats();
  },

  onShow() {
    // 从编辑页面返回时刷新列表
    this.loadServices();
    this.loadStats();
  },

  onPullDownRefresh() {
    this.onRefresh();
  },

  // 下拉刷新
  onRefresh() {
    this.setData({
      refreshing: true,
      page: 1,
      hasMore: true
    });

    this.loadServices().finally(() => {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onLoadMore() {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }

    this.setData({
      page: this.data.page + 1
    });

    this.loadServices(true);
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 搜索确认
  onSearch() {
    this.setData({
      page: 1,
      hasMore: true
    });
    this.loadServices();
  },

  // 筛选切换
  onFilterChange(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({
      activeFilter: filter,
      page: 1,
      hasMore: true
    });
    this.loadServices();
  },

  // 加载服务列表
  async loadServices(append = false) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const { page, pageSize, searchKeyword, activeFilter } = this.data;

      const params = {
        page,
        pageSize,
        keyword: searchKeyword,
        status: activeFilter === 'all' ? undefined : activeFilter.toUpperCase()
      };

      const response = await serviceAPI.getServices(params);

      const services = response.list || response || [];
      const newServices = append ? [...this.data.services, ...services] : services;

      this.setData({
        services: newServices,
        hasMore: services.length === pageSize,
        loading: false
      });

    } catch (error) {
      console.error('加载服务列表失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  // 加载统计数据
  async loadStats() {
    try {
      const stats = await serviceAPI.getServiceStats();

      // 更新筛选标签的数量
      const filterTabs = this.data.filterTabs.map(tab => ({
        ...tab,
        count: stats[tab.value] || 0
      }));

      this.setData({
        stats,
        filterTabs
      });

    } catch (error) {
      console.error('加载统计数据失败:', error);
      // 使用默认统计数据
      this.setData({
        stats: {
          total: 0,
          all: 0,
          active: 0,
          inactive: 0,
          pending: 0
        }
      });
    }
  },



  // 添加服务
  onAddService() {
    wx.navigateTo({
      url: '/pages/service-add/service-add'
    });
  },

  // 服务详情
  onServiceDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/service-detail/service-detail?id=${id}`
    });
  },

  // 编辑服务
  onEditService(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/service-edit/service-edit?id=${id}`
    });
  },

  // 更多操作
  onMoreActions(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({
      showActionSheet: true,
      currentServiceId: id
    });
  },

  // 隐藏操作菜单
  hideActionSheet() {
    this.setData({
      showActionSheet: false,
      currentServiceId: null
    });
  },

  // 切换服务状态
  onToggleStatus(e) {
    const { id, status } = e.currentTarget.dataset;
    const newStatus = status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE';
    const actionText = newStatus === 'ACTIVE' ? '上架' : '下架';

    wx.showModal({
      title: '确认操作',
      content: `确定要${actionText}这个服务吗？`,
      success: (res) => {
        if (res.confirm) {
          this.updateServiceStatus(id, newStatus);
        }
      }
    });
  },

  // 更新服务状态
  async updateServiceStatus(id, status) {
    try {
      wx.showLoading({
        title: status === 'ACTIVE' ? '上架中...' : '下架中...'
      });

      await serviceAPI.toggleServiceStatus(id);

      // 更新本地数据
      const services = this.data.services.map(service => {
        if (service.id === id) {
          return {
            ...service,
            status,
            statusText: status === 'ACTIVE' ? '上架中' : '已下架'
          };
        }
        return service;
      });

      this.setData({ services });

      wx.hideLoading();
      wx.showToast({
        title: status === 'ACTIVE' ? '上架成功' : '下架成功',
        icon: 'success'
      });

      // 刷新统计数据
      this.loadStats();

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      });
    }
  },

  // 复制服务
  onCopyService(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/service-add/service-add?copyId=${id}`
    });
    this.hideActionSheet();
  },

  // 查看统计
  onViewStats(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/service-stats/service-stats?id=${id}`
    });
    this.hideActionSheet();
  },

  // 删除服务
  onDeleteService(e) {
    const id = e.currentTarget.dataset.id;

    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除这个服务吗？',
      confirmColor: '#ff4d4f',
      success: (res) => {
        if (res.confirm) {
          this.deleteService(id);
        }
      }
    });

    this.hideActionSheet();
  },

  // 删除服务
  async deleteService(id) {
    try {
      wx.showLoading({
        title: '删除中...'
      });

      await serviceAPI.deleteService(id);

      // 更新本地数据
      const services = this.data.services.filter(service => service.id !== id);
      this.setData({ services });

      wx.hideLoading();
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });

      // 刷新统计数据
      this.loadStats();

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '删除失败',
        icon: 'none'
      });
    }
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  }
});