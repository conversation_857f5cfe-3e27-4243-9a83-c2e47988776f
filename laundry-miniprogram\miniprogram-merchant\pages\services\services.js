// 商家端服务管理页面
const app = getApp();

Page({
  data: {
    searchKeyword: '',
    activeFilter: 'all',
    services: [],
    stats: {
      total: 0,
      active: 0,
      inactive: 0,
      pending: 0
    },
    filterTabs: [
      { label: '全部', value: 'all', count: 0 },
      { label: '上架中', value: 'active', count: 0 },
      { label: '已下架', value: 'inactive', count: 0 },
      { label: '待审核', value: 'pending', count: 0 }
    ],
    refreshing: false,
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    batchMode: false,
    selectedServices: [],
    showActionSheet: false,
    currentServiceId: null
  },

  onLoad() {
    this.loadServices();
    this.loadStats();
  },

  onShow() {
    // 从编辑页面返回时刷新列表
    this.loadServices();
    this.loadStats();
  },

  onPullDownRefresh() {
    this.onRefresh();
  },

  // 下拉刷新
  onRefresh() {
    this.setData({
      refreshing: true,
      page: 1,
      hasMore: true
    });

    this.loadServices().finally(() => {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onLoadMore() {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }

    this.setData({
      page: this.data.page + 1
    });

    this.loadServices(true);
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 搜索确认
  onSearch() {
    this.setData({
      page: 1,
      hasMore: true
    });
    this.loadServices();
  },

  // 筛选切换
  onFilterChange(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({
      activeFilter: filter,
      page: 1,
      hasMore: true
    });
    this.loadServices();
  },

  // 加载服务列表
  async loadServices(append = false) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const { page, pageSize, searchKeyword, activeFilter } = this.data;

      // 模拟API调用
      const response = await this.getMockServices(page, pageSize, searchKeyword, activeFilter);

      const newServices = append ?
        [...this.data.services, ...response.data] :
        response.data;

      this.setData({
        services: newServices,
        hasMore: response.hasMore,
        loading: false
      });

    } catch (error) {
      console.error('加载服务列表失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  // 加载统计数据
  async loadStats() {
    try {
      // 模拟API调用
      const stats = await this.getMockStats();

      // 更新筛选标签的数量
      const filterTabs = this.data.filterTabs.map(tab => ({
        ...tab,
        count: stats[tab.value] || 0
      }));

      this.setData({
        stats,
        filterTabs
      });

    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  },

  // 模拟获取服务数据
  getMockServices(page, pageSize, keyword, filter) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const allServices = [
          {
            id: 1,
            name: '普通衣物洗涤',
            description: '日常衣物专业清洗，去污除菌，呵护面料',
            image: '/images/service-wash.jpg',
            price: 15.00,
            unit: '件',
            status: 'ACTIVE',
            statusText: '上架中',
            tags: ['热门', '快速'],
            salesCount: 156,
            rating: 4.8
          },
          {
            id: 2,
            name: '高档衣物护理',
            description: '高档面料专业护理，保持衣物品质',
            image: '/images/service-care.jpg',
            price: 35.00,
            unit: '件',
            status: 'ACTIVE',
            statusText: '上架中',
            tags: ['专业', '高端'],
            salesCount: 89,
            rating: 4.9
          },
          {
            id: 3,
            name: '运动鞋清洗',
            description: '专业运动鞋深度清洁，除臭杀菌',
            image: '/images/service-shoes.jpg',
            price: 25.00,
            unit: '双',
            status: 'INACTIVE',
            statusText: '已下架',
            tags: ['专业'],
            salesCount: 67,
            rating: 4.7
          }
        ];

        // 根据筛选条件过滤
        let filteredServices = allServices;
        if (filter !== 'all') {
          filteredServices = allServices.filter(service => {
            switch (filter) {
              case 'active':
                return service.status === 'ACTIVE';
              case 'inactive':
                return service.status === 'INACTIVE';
              case 'pending':
                return service.status === 'PENDING';
              default:
                return true;
            }
          });
        }

        // 根据关键词搜索
        if (keyword) {
          filteredServices = filteredServices.filter(service =>
            service.name.includes(keyword) || service.description.includes(keyword)
          );
        }

        // 分页
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        const data = filteredServices.slice(start, end);
        const hasMore = end < filteredServices.length;

        resolve({ data, hasMore });
      }, 500);
    });
  },

  // 模拟获取统计数据
  getMockStats() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          total: 8,
          all: 8,
          active: 5,
          inactive: 2,
          pending: 1
        });
      }, 300);
    });
  },

  // 添加服务
  onAddService() {
    wx.navigateTo({
      url: '/pages/service-add/service-add'
    });
  },

  // 服务详情
  onServiceDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/service-detail/service-detail?id=${id}`
    });
  },

  // 编辑服务
  onEditService(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/service-edit/service-edit?id=${id}`
    });
  },

  // 更多操作
  onMoreActions(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({
      showActionSheet: true,
      currentServiceId: id
    });
  },

  // 隐藏操作菜单
  hideActionSheet() {
    this.setData({
      showActionSheet: false,
      currentServiceId: null
    });
  },

  // 切换服务状态
  onToggleStatus(e) {
    const { id, status } = e.currentTarget.dataset;
    const newStatus = status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE';
    const actionText = newStatus === 'ACTIVE' ? '上架' : '下架';

    wx.showModal({
      title: '确认操作',
      content: `确定要${actionText}这个服务吗？`,
      success: (res) => {
        if (res.confirm) {
          this.updateServiceStatus(id, newStatus);
        }
      }
    });
  },

  // 更新服务状态
  async updateServiceStatus(id, status) {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));

      // 更新本地数据
      const services = this.data.services.map(service => {
        if (service.id === id) {
          return {
            ...service,
            status,
            statusText: status === 'ACTIVE' ? '上架中' : '已下架'
          };
        }
        return service;
      });

      this.setData({ services });

      wx.showToast({
        title: status === 'ACTIVE' ? '上架成功' : '下架成功',
        icon: 'success'
      });

      // 刷新统计数据
      this.loadStats();

    } catch (error) {
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  // 复制服务
  onCopyService(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/service-add/service-add?copyId=${id}`
    });
    this.hideActionSheet();
  },

  // 查看统计
  onViewStats(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/service-stats/service-stats?id=${id}`
    });
    this.hideActionSheet();
  },

  // 删除服务
  onDeleteService(e) {
    const id = e.currentTarget.dataset.id;

    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除这个服务吗？',
      confirmColor: '#ff4d4f',
      success: (res) => {
        if (res.confirm) {
          this.deleteService(id);
        }
      }
    });

    this.hideActionSheet();
  },

  // 删除服务
  async deleteService(id) {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));

      // 更新本地数据
      const services = this.data.services.filter(service => service.id !== id);
      this.setData({ services });

      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });

      // 刷新统计数据
      this.loadStats();

    } catch (error) {
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    }
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  }
});