<!--商家端服务管理页面-->
<view class="container">
  <!-- 顶部操作栏 -->
  <view class="header-actions">
    <view class="search-box">
      <input
        class="search-input"
        placeholder="搜索服务名称"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
      />
      <image class="search-icon" src="/images/search-icon.png"></image>
    </view>
    <button class="add-service-btn" bindtap="onAddService">
      <image class="add-icon" src="/images/add-icon.png"></image>
      <text>添加服务</text>
    </button>
  </view>

  <!-- 服务统计 -->
  <view class="service-stats">
    <view class="stat-item">
      <text class="stat-number">{{stats.total}}</text>
      <text class="stat-label">总服务</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{stats.active}}</text>
      <text class="stat-label">上架中</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{stats.inactive}}</text>
      <text class="stat-label">已下架</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{stats.pending}}</text>
      <text class="stat-label">待审核</text>
    </view>
  </view>

  <!-- 筛选标签 -->
  <view class="filter-tabs">
    <view
      class="filter-tab {{activeFilter === item.value ? 'active' : ''}}"
      wx:for="{{filterTabs}}"
      wx:key="value"
      bindtap="onFilterChange"
      data-filter="{{item.value}}"
    >
      <text class="tab-text">{{item.label}}</text>
      <view class="tab-badge" wx:if="{{item.count > 0}}">{{item.count}}</view>
    </view>
  </view>

  <!-- 服务列表 -->
  <scroll-view
    class="service-list"
    scroll-y
    refresher-enabled
    refresher-triggered="{{refreshing}}"
    bindrefresherrefresh="onRefresh"
    bindscrolltolower="onLoadMore"
  >
    <view class="service-item" wx:for="{{services}}" wx:key="id" bindtap="onServiceDetail" data-id="{{item.id}}">
      <!-- 服务图片 -->
      <view class="service-image-container">
        <image class="service-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="service-status {{item.status}}">
          <text class="status-text">{{item.statusText}}</text>
        </view>
      </view>

      <!-- 服务信息 -->
      <view class="service-info">
        <view class="service-header">
          <text class="service-name">{{item.name}}</text>
          <view class="service-actions" catchtap="stopPropagation">
            <button class="action-btn edit" bindtap="onEditService" data-id="{{item.id}}">
              <image class="action-icon" src="/images/edit-icon.png"></image>
            </button>
            <button class="action-btn more" bindtap="onMoreActions" data-id="{{item.id}}">
              <image class="action-icon" src="/images/more-icon.png"></image>
            </button>
          </view>
        </view>

        <text class="service-desc">{{item.description}}</text>

        <!-- 服务标签 -->
        <view class="service-tags">
          <text class="service-tag" wx:for="{{item.tags}}" wx:key="*this">{{item}}</text>
        </view>

        <!-- 价格和销量 -->
        <view class="service-bottom">
          <view class="price-info">
            <text class="price-symbol">¥</text>
            <text class="price-amount">{{item.price}}</text>
            <text class="price-unit">/{{item.unit}}</text>
          </view>
          <view class="sales-info">
            <text class="sales-count">已售{{item.salesCount}}</text>
            <text class="rating-score">{{item.rating}}分</text>
          </view>
        </view>

        <!-- 服务操作 -->
        <view class="service-operations" catchtap="stopPropagation">
          <button
            class="operation-btn {{item.status === 'ACTIVE' ? 'offline' : 'online'}}"
            bindtap="onToggleStatus"
            data-id="{{item.id}}"
            data-status="{{item.status}}"
          >
            {{item.status === 'ACTIVE' ? '下架' : '上架'}}
          </button>
          <button class="operation-btn edit" bindtap="onEditService" data-id="{{item.id}}">
            编辑
          </button>
          <button class="operation-btn stats" bindtap="onViewStats" data-id="{{item.id}}">
            统计
          </button>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore}}">
      <text class="load-text">{{loading ? '加载中...' : '上拉加载更多'}}</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!services.length && !loading}}">
      <image class="empty-image" src="/images/empty-services.png"></image>
      <text class="empty-text">暂无服务</text>
      <button class="empty-btn" bindtap="onAddService">添加第一个服务</button>
    </view>
  </scroll-view>

  <!-- 批量操作栏 -->
  <view class="batch-actions" wx:if="{{batchMode}}">
    <view class="batch-info">
      <text class="selected-count">已选择 {{selectedServices.length}} 个服务</text>
    </view>
    <view class="batch-buttons">
      <button class="batch-btn cancel" bindtap="onCancelBatch">取消</button>
      <button class="batch-btn online" bindtap="onBatchOnline">批量上架</button>
      <button class="batch-btn offline" bindtap="onBatchOffline">批量下架</button>
      <button class="batch-btn delete" bindtap="onBatchDelete">删除</button>
    </view>
  </view>
</view>

<!-- 更多操作菜单 -->
<view class="action-sheet" wx:if="{{showActionSheet}}" bindtap="hideActionSheet">
  <view class="action-sheet-content" catchtap="stopPropagation">
    <view class="action-sheet-header">
      <text class="sheet-title">服务操作</text>
      <image class="close-btn" src="/images/close.png" bindtap="hideActionSheet"></image>
    </view>
    <view class="action-sheet-body">
      <button class="sheet-action" bindtap="onEditService" data-id="{{currentServiceId}}">
        <image class="sheet-icon" src="/images/edit-icon.png"></image>
        <text class="sheet-text">编辑服务</text>
      </button>
      <button class="sheet-action" bindtap="onCopyService" data-id="{{currentServiceId}}">
        <image class="sheet-icon" src="/images/copy-icon.png"></image>
        <text class="sheet-text">复制服务</text>
      </button>
      <button class="sheet-action" bindtap="onViewStats" data-id="{{currentServiceId}}">
        <image class="sheet-icon" src="/images/stats-icon.png"></image>
        <text class="sheet-text">查看统计</text>
      </button>
      <button class="sheet-action danger" bindtap="onDeleteService" data-id="{{currentServiceId}}">
        <image class="sheet-icon" src="/images/delete-icon.png"></image>
        <text class="sheet-text">删除服务</text>
      </button>
    </view>
  </view>
</view>