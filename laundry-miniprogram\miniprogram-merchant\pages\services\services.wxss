/* 商家端服务管理页面样式 */
.container {
  background: #f5f5f5;
  min-height: 100vh;
}

/* 顶部操作栏 */
.header-actions {
  background: #ffffff;
  padding: 20rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-box {
  flex: 1;
  position: relative;
  background: #f8f9fa;
  border-radius: 40rpx;
  padding: 0 40rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  height: 100%;
}

.search-input::placeholder {
  color: #999;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 20rpx;
}

.add-service-btn {
  background: #ff6b35;
  color: #ffffff;
  padding: 20rpx 30rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  border: none;
}

.add-icon {
  width: 28rpx;
  height: 28rpx;
}

/* 服务统计 */
.service-stats {
  background: #ffffff;
  padding: 30rpx;
  margin: 20rpx;
  border-radius: 20rpx;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b35;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 筛选标签 */
.filter-tabs {
  background: #ffffff;
  padding: 20rpx;
  display: flex;
  gap: 20rpx;
  overflow-x: auto;
  white-space: nowrap;
}

.filter-tab {
  padding: 16rpx 32rpx;
  background: #f8f9fa;
  color: #666;
  border-radius: 40rpx;
  font-size: 26rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex-shrink: 0;
}

.filter-tab.active {
  background: #ff6b35;
  color: #ffffff;
  border-color: #ff6b35;
}

.tab-badge {
  background: rgba(255, 255, 255, 0.3);
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1;
}

.filter-tab:not(.active) .tab-badge {
  background: #ff4d4f;
  color: #ffffff;
}

/* 服务列表 */
.service-list {
  height: calc(100vh - 300rpx);
  padding: 20rpx;
}

.service-item {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.service-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 服务图片容器 */
.service-image-container {
  position: relative;
  width: 100%;
  height: 300rpx;
}

.service-image {
  width: 100%;
  height: 100%;
}

.service-status {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.service-status.ACTIVE {
  background: rgba(82, 196, 26, 0.9);
  color: #ffffff;
}

.service-status.INACTIVE {
  background: rgba(255, 77, 79, 0.9);
  color: #ffffff;
}

.service-status.PENDING {
  background: rgba(250, 140, 22, 0.9);
  color: #ffffff;
}

/* 服务信息 */
.service-info {
  padding: 30rpx;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.service-name {
  flex: 1;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-right: 20rpx;
}

.service-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  background: #f8f9fa;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  transition: all 0.3s ease;
}

.action-btn:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
}

.service-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 服务标签 */
.service-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.service-tag {
  padding: 8rpx 16rpx;
  background: #f0f8ff;
  color: #1890ff;
  border-radius: 20rpx;
  font-size: 22rpx;
}

/* 价格和销量 */
.service-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.price-info {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 24rpx;
  color: #ff4d4f;
  margin-right: 4rpx;
}

.price-amount {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff4d4f;
}

.price-unit {
  font-size: 24rpx;
  color: #999;
  margin-left: 4rpx;
}

.sales-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.sales-count {
  font-size: 24rpx;
  color: #666;
}

.rating-score {
  font-size: 24rpx;
  color: #faad14;
  font-weight: 500;
}

/* 服务操作 */
.service-operations {
  display: flex;
  gap: 20rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #f5f5f5;
}

.operation-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  border: none;
  transition: all 0.3s ease;
}

.operation-btn.online {
  background: #52c41a;
  color: #ffffff;
}

.operation-btn.offline {
  background: #ff4d4f;
  color: #ffffff;
}

.operation-btn.edit {
  background: #1890ff;
  color: #ffffff;
}

.operation-btn.stats {
  background: #faad14;
  color: #ffffff;
}

.operation-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.empty-btn {
  background: #ff6b35;
  color: #ffffff;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx 0;
  font-size: 28rpx;
  color: #999;
}

/* 操作菜单 */
.action-sheet {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.action-sheet-content {
  background: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  width: 100%;
  max-height: 80vh;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.action-sheet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.sheet-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  width: 40rpx;
  height: 40rpx;
}

.action-sheet-body {
  padding: 20rpx 0;
}

.sheet-action {
  width: 100%;
  height: 100rpx;
  background: #ffffff;
  border: none;
  display: flex;
  align-items: center;
  padding: 0 40rpx;
  font-size: 30rpx;
  color: #333;
}

.sheet-action:active {
  background: #f8f9fa;
}

.sheet-action.danger {
  color: #ff4d4f;
}

.sheet-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 30rpx;
}

/* 动画效果 */
.service-item {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 375px) {
  .service-list {
    padding: 15rpx;
  }
  
  .service-item {
    margin-bottom: 15rpx;
  }
  
  .service-info {
    padding: 25rpx;
  }
  
  .service-name {
    font-size: 30rpx;
  }
  
  .price-amount {
    font-size: 32rpx;
  }
  
  .operation-btn {
    height: 60rpx;
    font-size: 24rpx;
  }
}
