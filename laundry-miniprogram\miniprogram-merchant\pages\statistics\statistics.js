const app = getApp();
const { statisticsAPI, dashboardAPI } = require('../../utils/api.js');

Page({
  data: {
    currentPeriod: 'week',
    periodOptions: [
      { value: 'today', label: '今日' },
      { value: 'week', label: '本周' },
      { value: 'month', label: '本月' },
      { value: 'quarter', label: '本季度' },
      { value: 'year', label: '本年' }
    ],

    // 概览数据
    overview: {
      totalOrders: 0,
      totalEarnings: '0.00',
      avgOrderAmount: '0.00',
      completionRate: 0,
      customerCount: 0,
      repeatRate: 0
    },

    // 趋势数据
    trends: {
      orderTrend: 0,
      earningsTrend: 0,
      customerTrend: 0
    },

    // 图表数据
    chartData: {
      orders: [],
      earnings: [],
      customers: []
    },

    // 服务统计
    serviceStats: [],

    // 时间分布
    timeDistribution: [],

    // 地区分布
    regionDistribution: [],

    loading: true,
    showPeriodPicker: false
  },

  onLoad() {
    this.loadAllData();
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
  },

  // 加载所有数据
  async loadAllData() {
    this.setData({ loading: true });

    try {
      await Promise.all([
        this.loadOverview(),
        this.loadTrends(),
        this.loadChartData(),
        this.loadServiceStats(),
        this.loadTimeDistribution(),
        this.loadRegionDistribution()
      ]);
    } catch (error) {
      console.error('加载统计数据失败:', error);
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载概览数据
  async loadOverview() {
    try {
      const overview = await statisticsAPI.getOverview(this.data.currentPeriod);
      this.setData({ overview });
    } catch (error) {
      console.error('加载概览数据失败:', error);
      this.setData({
        overview: {
          totalOrders: 0,
          totalEarnings: '0.00',
          avgOrderAmount: '0.00',
          completionRate: 0,
          customerCount: 0,
          repeatRate: 0
        }
      });
    }
  },

  // 加载趋势数据
  async loadTrends() {
    try {
      const trends = await statisticsAPI.getTrends(this.data.currentPeriod);
      this.setData({ trends });
    } catch (error) {
      console.error('加载趋势数据失败:', error);
      this.setData({
        trends: {
          orderTrend: 0,
          earningsTrend: 0,
          customerTrend: 0
        }
      });
    }
  },

  // 加载图表数据
  async loadChartData() {
    try {
      const chartData = await statisticsAPI.getChartData(this.data.currentPeriod);
      this.setData({ chartData });
    } catch (error) {
      console.error('加载图表数据失败:', error);
      this.setData({
        chartData: {
          orders: [],
          earnings: [],
          customers: []
        }
      });
    }
  },

  // 加载服务统计
  async loadServiceStats() {
    try {
      const serviceStats = await statisticsAPI.getServiceStats(this.data.currentPeriod);
      this.setData({ serviceStats });
    } catch (error) {
      console.error('加载服务统计失败:', error);
      this.setData({ serviceStats: [] });
    }
  },

  // 加载时间分布
  async loadTimeDistribution() {
    try {
      const timeDistribution = await statisticsAPI.getTimeDistribution(this.data.currentPeriod);
      this.setData({ timeDistribution });
    } catch (error) {
      console.error('加载时间分布失败:', error);
      this.setData({ timeDistribution: [] });
    }
  },

  // 加载地区分布
  async loadRegionDistribution() {
    try {
      const regionDistribution = await statisticsAPI.getRegionDistribution(this.data.currentPeriod);
      this.setData({ regionDistribution });
    } catch (error) {
      console.error('加载地区分布失败:', error);
      this.setData({ regionDistribution: [] });
    }
  },

  // 显示时间选择器
  onShowPeriodPicker() {
    this.setData({
      showPeriodPicker: true
    });
  },

  // 时间选择确认
  onPeriodConfirm(e) {
    const index = e.detail.value;
    const period = this.data.periodOptions[index];

    this.setData({
      currentPeriod: period.value,
      showPeriodPicker: false
    });

    this.loadAllData();
  },

  // 时间选择取消
  onPeriodCancel() {
    this.setData({
      showPeriodPicker: false
    });
  },

  // 查看订单详情
  onViewOrders() {
    wx.navigateTo({
      url: '/pages/orders/orders'
    });
  },

  // 查看收益详情
  onViewEarnings() {
    wx.navigateTo({
      url: '/pages/finance/finance?tab=1'
    });
  },

  // 查看服务详情
  onViewServices() {
    wx.navigateTo({
      url: '/pages/services/services'
    });
  },

  // 查看客户详情
  onViewCustomers() {
    wx.navigateTo({
      url: '/pages/customers/customers'
    });
  },

  // 服务项点击
  onServiceTap(e) {
    const service = e.currentTarget.dataset.service;
    wx.navigateTo({
      url: `/pages/service-detail/service-detail?id=${service.id}`
    });
  },

  // 导出数据
  async onExportData() {
    try {
      wx.showLoading({
        title: '导出中...'
      });

      const result = await statisticsAPI.exportData({
        period: this.data.currentPeriod,
        type: 'all'
      });

      wx.hideLoading();

      if (result.downloadUrl) {
        wx.downloadFile({
          url: result.downloadUrl,
          success: (res) => {
            wx.openDocument({
              filePath: res.tempFilePath,
              success: () => {
                wx.showToast({
                  title: '导出成功',
                  icon: 'success'
                });
              }
            });
          },
          fail: () => {
            wx.showToast({
              title: '下载失败',
              icon: 'none'
            });
          }
        });
      }

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '导出失败',
        icon: 'none'
      });
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadAllData();
    wx.stopPullDownRefresh();
  }
});