// 商家端小程序API接口封装
const { request, API_CONFIG, getApiUrl } = require('./request.js');

// 获取API端点
const ENDPOINTS = API_CONFIG.ENDPOINTS;

// 商家认证相关API
const authAPI = {
  // 商家登录
  login(loginData) {
    return request.post(ENDPOINTS.AUTH.LOGIN, loginData);
  },
  
  // 商家注册
  register(registerData) {
    return request.post(ENDPOINTS.AUTH.REGISTER, registerData);
  },
  
  // 获取商家信息
  getMerchantInfo() {
    return request.get(ENDPOINTS.AUTH.INFO);
  },
  
  // 更新商家信息
  updateMerchantInfo(merchantInfo) {
    return request.put(ENDPOINTS.AUTH.UPDATE_INFO, merchantInfo);
  },
  
  // 退出登录
  logout() {
    return request.post(ENDPOINTS.AUTH.LOGOUT);
  },
  
  // 发送验证码
  sendSMS(phone, type = 'LOGIN') {
    return request.post(ENDPOINTS.COMMON.SMS, {
      phone,
      type
    });
  }
};

// 订单相关API
const orderAPI = {
  // 获取订单列表
  getOrders(params = {}) {
    return request.get(ENDPOINTS.ORDERS.LIST, params);
  },
  
  // 获取订单详情
  getOrderDetail(orderId) {
    const url = getApiUrl(ENDPOINTS.ORDERS.DETAIL, { id: orderId });
    return request.get(url);
  },
  
  // 接受订单
  acceptOrder(orderId) {
    const url = getApiUrl(ENDPOINTS.ORDERS.ACCEPT, { id: orderId });
    return request.post(url);
  },
  
  // 拒绝订单
  rejectOrder(orderId, reason) {
    const url = getApiUrl(ENDPOINTS.ORDERS.REJECT, { id: orderId });
    return request.post(url, { reason });
  },
  
  // 开始服务
  startOrder(orderId) {
    const url = getApiUrl(ENDPOINTS.ORDERS.START, { id: orderId });
    return request.post(url);
  },
  
  // 完成订单
  completeOrder(orderId, completionData) {
    const url = getApiUrl(ENDPOINTS.ORDERS.COMPLETE, { id: orderId });
    return request.post(url, completionData);
  },
  
  // 获取订单统计
  getOrderStats() {
    return request.get(ENDPOINTS.ORDERS.STATS);
  }
};

// 服务相关API
const serviceAPI = {
  // 获取服务列表
  getServices(params = {}) {
    return request.get(ENDPOINTS.SERVICES.LIST, params);
  },
  
  // 获取服务详情
  getServiceDetail(serviceId) {
    const url = getApiUrl(ENDPOINTS.SERVICES.DETAIL, { id: serviceId });
    return request.get(url);
  },
  
  // 创建服务
  createService(serviceData) {
    return request.post(ENDPOINTS.SERVICES.CREATE, serviceData);
  },
  
  // 更新服务
  updateService(serviceId, serviceData) {
    const url = getApiUrl(ENDPOINTS.SERVICES.UPDATE, { id: serviceId });
    return request.put(url, serviceData);
  },
  
  // 删除服务
  deleteService(serviceId) {
    const url = getApiUrl(ENDPOINTS.SERVICES.DELETE, { id: serviceId });
    return request.delete(url);
  },
  
  // 切换服务状态
  toggleServiceStatus(serviceId) {
    const url = getApiUrl(ENDPOINTS.SERVICES.TOGGLE_STATUS, { id: serviceId });
    return request.post(url);
  },
  
  // 获取服务统计
  getServiceStats() {
    return request.get(ENDPOINTS.SERVICES.STATS);
  }
};

// 收益相关API
const earningsAPI = {
  // 获取收益概览
  getEarningsOverview() {
    return request.get(ENDPOINTS.EARNINGS.OVERVIEW);
  },
  
  // 获取收益列表
  getEarningsList(params = {}) {
    return request.get(ENDPOINTS.EARNINGS.LIST, params);
  },
  
  // 申请提现
  requestWithdraw(withdrawData) {
    return request.post(ENDPOINTS.EARNINGS.WITHDRAW, withdrawData);
  },
  
  // 获取提现记录
  getWithdrawRecords(params = {}) {
    return request.get(ENDPOINTS.EARNINGS.WITHDRAW_RECORDS, params);
  },
  
  // 获取收益统计
  getEarningsStats(period = 'month') {
    return request.get(ENDPOINTS.EARNINGS.STATS, { period });
  }
};

// 仪表盘相关API
const dashboardAPI = {
  // 获取仪表盘概览
  getDashboardOverview() {
    return request.get(ENDPOINTS.DASHBOARD.OVERVIEW);
  },
  
  // 获取仪表盘统计
  getDashboardStats() {
    return request.get(ENDPOINTS.DASHBOARD.STATS);
  }
};

// 商家API
const merchantAPI = {
  // 获取商家资料
  getProfile() {
    return request.get('/merchant/profile');
  },

  // 更新商家资料
  updateProfile(profileData) {
    return request.put('/merchant/profile', profileData);
  },

  // 更新营业状态
  updateOnlineStatus(isOnline) {
    return request.post('/merchant/online-status', { isOnline });
  },

  // 获取未读消息数量
  getUnreadCount() {
    return request.get('/merchant/unread-count');
  }
};

// 财务API
const financeAPI = {
  // 获取财务概览
  getFinanceOverview() {
    return request.get('/merchant/finance/overview');
  },

  // 获取提现记录
  getWithdrawRecords(params = {}) {
    return request.get('/merchant/finance/withdraw-records', params);
  },

  // 获取账单明细
  getBills(params = {}) {
    return request.get('/merchant/finance/bills', params);
  },

  // 获取银行卡列表
  getBankCards() {
    return request.get('/merchant/finance/bank-cards');
  },

  // 申请提现
  requestWithdraw(withdrawData) {
    return request.post('/merchant/finance/withdraw', withdrawData);
  }
};

// 公共API
const commonAPI = {
  // 上传文件
  uploadFile(filePath, formData = {}) {
    return request.upload('/upload', filePath, formData);
  },

  // 获取地区数据
  getRegions(parentId = 0) {
    return request.get(ENDPOINTS.COMMON.REGIONS, { parentId });
  }
};

// 导出所有API
module.exports = {
  authAPI,
  orderAPI,
  serviceAPI,
  earningsAPI,
  dashboardAPI,
  merchantAPI,
  financeAPI,
  commonAPI,

  // 兼容旧版本
  request: request.request.bind(request),
  get: request.get.bind(request),
  post: request.post.bind(request),
  put: request.put.bind(request),
  delete: request.delete.bind(request)
};
