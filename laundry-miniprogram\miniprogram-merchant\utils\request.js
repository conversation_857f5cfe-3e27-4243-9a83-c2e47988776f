// 商家端小程序请求工具类
const API_CONFIG = {
  BASE_URL: 'http://localhost:8080/api',
  TIMEOUT: 10000,
  
  // API端点配置
  ENDPOINTS: {
    AUTH: {
      LOGIN: '/merchant/login',
      REGISTER: '/merchant/register',
      LOGOUT: '/merchant/logout',
      INFO: '/merchant/info',
      UPDATE_INFO: '/merchant/update'
    },
    ORDERS: {
      LIST: '/merchant/orders',
      DETAIL: '/merchant/orders/{id}',
      ACCEPT: '/merchant/orders/{id}/accept',
      REJECT: '/merchant/orders/{id}/reject',
      START: '/merchant/orders/{id}/start',
      COMPLETE: '/merchant/orders/{id}/complete',
      STATS: '/merchant/orders/stats'
    },
    SERVICES: {
      LIST: '/merchant/services',
      DETAIL: '/merchant/services/{id}',
      CREATE: '/merchant/services',
      UPDATE: '/merchant/services/{id}',
      DELETE: '/merchant/services/{id}',
      TOGGLE_STATUS: '/merchant/services/{id}/toggle-status',
      STATS: '/merchant/services/stats'
    },
    EARNINGS: {
      OVERVIEW: '/merchant/earnings/overview',
      LIST: '/merchant/earnings/list',
      WITHDRAW: '/merchant/earnings/withdraw',
      WITHDRAW_RECORDS: '/merchant/earnings/withdraw-records',
      STATS: '/merchant/earnings/stats'
    },
    DASHBOARD: {
      OVERVIEW: '/merchant/dashboard/overview',
      STATS: '/merchant/dashboard/stats'
    },
    COMMON: {
      UPLOAD: '/common/upload',
      REGIONS: '/common/regions',
      SMS: '/common/sms/send'
    }
  },
  
  // 错误消息配置
  ERROR_MESSAGES: {
    NETWORK_ERROR: '网络连接失败，请检查网络设置',
    TIMEOUT_ERROR: '请求超时，请重试',
    SERVER_ERROR: '服务器错误，请稍后重试',
    UNAUTHORIZED: '登录已过期，请重新登录',
    FORBIDDEN: '没有权限访问',
    NOT_FOUND: '请求的资源不存在'
  }
};

class RequestManager {
  constructor() {
    this.baseURL = API_CONFIG.BASE_URL;
    this.timeout = API_CONFIG.TIMEOUT;
  }
  
  // 获取token
  getToken() {
    try {
      return wx.getStorageSync('merchant_token');
    } catch (error) {
      console.error('获取token失败:', error);
      return null;
    }
  }
  
  // 设置token
  setToken(token) {
    try {
      wx.setStorageSync('merchant_token', token);
    } catch (error) {
      console.error('设置token失败:', error);
    }
  }
  
  // 清除token
  clearToken() {
    try {
      wx.removeStorageSync('merchant_token');
    } catch (error) {
      console.error('清除token失败:', error);
    }
  }
  
  // 获取错误消息
  getErrorMessage(statusCode) {
    switch (statusCode) {
      case 401:
        return API_CONFIG.ERROR_MESSAGES.UNAUTHORIZED;
      case 403:
        return API_CONFIG.ERROR_MESSAGES.FORBIDDEN;
      case 404:
        return API_CONFIG.ERROR_MESSAGES.NOT_FOUND;
      case 500:
        return API_CONFIG.ERROR_MESSAGES.SERVER_ERROR;
      default:
        return API_CONFIG.ERROR_MESSAGES.NETWORK_ERROR;
    }
  }
  
  // 基础请求方法
  request(config) {
    return new Promise((resolve, reject) => {
      // 处理URL
      let url = config.url;
      if (!url.startsWith('http')) {
        url = this.baseURL + url;
      }
      
      // 添加token
      const token = this.getToken();
      const header = {
        'Content-Type': 'application/json',
        ...config.header
      };
      
      if (token) {
        header.Authorization = `Bearer ${token}`;
      }
      
      // 添加时间戳防止缓存
      if (config.method === 'GET' || !config.method) {
        config.data = config.data || {};
        config.data._t = Date.now();
      }
      
      console.log('[商家端] 请求:', url, config);
      
      // 发起请求
      wx.request({
        url,
        method: config.method || 'GET',
        data: config.data,
        header,
        timeout: config.timeout || this.timeout,
        success: (response) => {
          console.log('[商家端] 响应:', response);
          
          if (response.statusCode === 200) {
            const data = response.data;
            
            // 后端统一返回格式: { success: boolean, data: any, message: string, code: number }
            if (data && typeof data === 'object') {
              if (data.success === false) {
                // 业务错误
                reject({
                  type: 'BUSINESS_ERROR',
                  message: data.message || '操作失败',
                  code: data.code,
                  data: data.data
                });
                return;
              }
              
              // 成功响应，返回data字段
              resolve(data.data || data);
            } else {
              resolve(data);
            }
          } else {
            // HTTP错误
            reject({
              type: 'HTTP_ERROR',
              message: this.getErrorMessage(response.statusCode),
              statusCode: response.statusCode,
              response
            });
          }
        },
        fail: (error) => {
          console.error('[商家端] 请求错误:', error);
          
          // 网络错误处理
          if (error.errMsg) {
            if (error.errMsg.includes('timeout')) {
              error.type = 'TIMEOUT_ERROR';
              error.message = API_CONFIG.ERROR_MESSAGES.TIMEOUT_ERROR;
            } else if (error.errMsg.includes('fail')) {
              error.type = 'NETWORK_ERROR';
              error.message = API_CONFIG.ERROR_MESSAGES.NETWORK_ERROR;
            }
          }
          
          reject(error);
        }
      });
    });
  }
  
  // GET请求
  get(url, params, config = {}) {
    return this.request({
      url,
      method: 'GET',
      data: params,
      ...config
    });
  }
  
  // POST请求
  post(url, data, config = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...config
    });
  }
  
  // PUT请求
  put(url, data, config = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...config
    });
  }
  
  // DELETE请求
  delete(url, config = {}) {
    return this.request({
      url,
      method: 'DELETE',
      ...config
    });
  }
  
  // 文件上传
  upload(url, filePath, formData = {}, config = {}) {
    return new Promise((resolve, reject) => {
      // 添加token
      const token = this.getToken();
      const header = { ...config.header };
      if (token) {
        header.Authorization = `Bearer ${token}`;
      }
      
      // 处理URL
      if (!url.startsWith('http')) {
        url = this.baseURL + url;
      }
      
      wx.uploadFile({
        url,
        filePath,
        name: config.name || 'file',
        formData,
        header,
        success: (response) => {
          try {
            const data = JSON.parse(response.data);
            if (data.success) {
              resolve(data.data);
            } else {
              reject({
                type: 'BUSINESS_ERROR',
                message: data.message || '上传失败'
              });
            }
          } catch (error) {
            reject({
              type: 'PARSE_ERROR',
              message: '响应数据解析失败'
            });
          }
        },
        fail: (error) => {
          reject({
            type: 'UPLOAD_ERROR',
            message: '文件上传失败',
            error
          });
        }
      });
    });
  }
}

// 获取API URL
function getApiUrl(endpoint, params = {}) {
  let url = endpoint;
  
  // 替换路径参数
  Object.keys(params).forEach(key => {
    url = url.replace(`{${key}}`, params[key]);
  });
  
  return url;
}

// 创建请求实例
const request = new RequestManager();

// 导出
module.exports = {
  request,
  API_CONFIG,
  getApiUrl
};
