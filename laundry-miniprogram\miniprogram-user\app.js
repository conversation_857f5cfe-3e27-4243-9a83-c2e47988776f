// 用户端小程序入口文件
const { authAPI } = require('./utils/api.js');

App({
  onLaunch() {
    console.log('用户端小程序启动');

    // 初始化应用
    this.initApp();
  },

  onShow() {
    // 应用从后台进入前台时触发
    this.checkLoginStatus();
  },

  onHide() {
    // 应用从前台进入后台时触发
  },

  onError(msg) {
    console.error('小程序发生错误:', msg);
  },

  // 初始化应用
  initApp() {
    // 检查更新
    this.checkUpdate();

    // 检查登录状态
    this.checkLoginStatus();

    // 获取系统信息
    this.getSystemInfo();
  },

  // 检查小程序更新
  checkUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();

      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          console.log('发现新版本');
        }
      });

      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });

      updateManager.onUpdateFailed(() => {
        console.error('新版本下载失败');
      });
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('user_token');
    const userInfo = wx.getStorageSync('user_info');

    if (token && userInfo) {
      this.globalData.isLoggedIn = true;
      this.globalData.userInfo = userInfo;
      this.globalData.token = token;

      // 验证token有效性
      this.validateToken();
    } else {
      this.globalData.isLoggedIn = false;
      this.globalData.userInfo = null;
      this.globalData.token = null;
    }
  },

  // 验证token有效性
  async validateToken() {
    try {
      const userInfo = await authAPI.getUserInfo();
      this.globalData.userInfo = userInfo;
      wx.setStorageSync('user_info', userInfo);
    } catch (error) {
      console.error('Token验证失败:', error);
      // Token无效，清除登录状态
      this.logout();
    }
  },

  // 获取系统信息
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res;
        console.log('系统信息:', res);
      },
      fail: (error) => {
        console.error('获取系统信息失败:', error);
      }
    });
  },

  // 微信登录
  async wxLogin() {
    try {
      // 获取微信登录code
      const loginRes = await this.getWxLoginCode();

      // 获取用户信息
      const userInfo = await this.getUserProfile();

      // 调用后端登录接口
      const loginResult = await authAPI.wxLogin(loginRes.code, userInfo);

      // 保存登录信息
      this.saveLoginInfo(loginResult);

      return loginResult;
    } catch (error) {
      console.error('微信登录失败:', error);
      throw error;
    }
  },

  // 获取微信登录code
  getWxLoginCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  },

  // 获取用户信息
  getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          resolve(res.userInfo);
        },
        fail: reject
      });
    });
  },

  // 保存登录信息
  saveLoginInfo(loginResult) {
    this.globalData.isLoggedIn = true;
    this.globalData.userInfo = loginResult.userInfo;
    this.globalData.token = loginResult.token;

    // 保存到本地存储
    wx.setStorageSync('user_token', loginResult.token);
    wx.setStorageSync('user_info', loginResult.userInfo);

    console.log('登录成功:', loginResult);
  },

  // 退出登录
  async logout() {
    try {
      // 调用后端退出接口
      await authAPI.logout();
    } catch (error) {
      console.error('退出登录接口调用失败:', error);
    } finally {
      // 清除本地数据
      this.clearLoginInfo();
    }
  },

  // 清除登录信息
  clearLoginInfo() {
    this.globalData.isLoggedIn = false;
    this.globalData.userInfo = null;
    this.globalData.token = null;

    // 清除本地存储
    wx.removeStorageSync('user_token');
    wx.removeStorageSync('user_info');

    console.log('已清除登录信息');
  },

  // 检查登录状态，未登录则跳转登录页
  checkLogin() {
    if (!this.globalData.isLoggedIn) {
      wx.navigateTo({
        url: '/pages/login/login'
      });
      return false;
    }
    return true;
  },

  // 显示错误提示
  showError(message, duration = 2000) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration
    });
  },

  // 显示成功提示
  showSuccess(message, duration = 2000) {
    wx.showToast({
      title: message,
      icon: 'success',
      duration
    });
  },

  // 显示加载提示
  showLoading(title = '加载中...') {
    wx.showLoading({
      title,
      mask: true
    });
  },

  // 隐藏加载提示
  hideLoading() {
    wx.hideLoading();
  },

  // 全局数据
  globalData: {
    isLoggedIn: false,
    userInfo: null,
    token: null,
    systemInfo: null
  }
});
