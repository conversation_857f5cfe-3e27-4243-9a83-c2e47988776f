/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'Segoe UI', Arial, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 通用按钮样式 */
.btn {
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  outline: none;
}

.btn-primary {
  background-color: #3cc51f;
  color: #fff;
}

.btn-primary:active {
  background-color: #2aad17;
}

.btn-secondary {
  background-color: #fff;
  color: #3cc51f;
  border: 2rpx solid #3cc51f;
}

.btn-danger {
  background-color: #ff4757;
  color: #fff;
}

.btn-disabled {
  background-color: #c8c9cc;
  color: #969799;
}

/* 通用卡片样式 */
.card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 通用列表样式 */
.list-item {
  background-color: #fff;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #ebedf0;
  display: flex;
  align-items: center;
}

.list-item:last-child {
  border-bottom: none;
}

/* 通用标题样式 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #323233;
  margin-bottom: 20rpx;
}

/* 通用标签样式 */
.tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  border-radius: 6rpx;
  margin-right: 10rpx;
}

.tag-primary {
  background-color: #e8f4fd;
  color: #1989fa;
}

.tag-success {
  background-color: #f0f9ff;
  color: #07c160;
}

.tag-warning {
  background-color: #fff7e6;
  color: #ff976a;
}

.tag-danger {
  background-color: #ffeaea;
  color: #ee0a24;
}

/* 通用状态样式 */
.status-pending {
  color: #ff976a;
}

.status-processing {
  color: #1989fa;
}

.status-completed {
  color: #07c160;
}

.status-cancelled {
  color: #969799;
}

.status-failed {
  color: #ee0a24;
}

/* 通用加载样式 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
  color: #969799;
  font-size: 28rpx;
}

/* 通用空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #969799;
  margin-bottom: 30rpx;
}

.empty-btn {
  background-color: #3cc51f;
  color: #fff;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 通用分割线 */
.divider {
  height: 1rpx;
  background-color: #ebedf0;
  margin: 20rpx 0;
}

/* 通用间距 */
.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mt-30 { margin-top: 30rpx; }
.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.mb-30 { margin-bottom: 30rpx; }
.ml-10 { margin-left: 10rpx; }
.ml-20 { margin-left: 20rpx; }
.mr-10 { margin-right: 10rpx; }
.mr-20 { margin-right: 20rpx; }

.pt-10 { padding-top: 10rpx; }
.pt-20 { padding-top: 20rpx; }
.pt-30 { padding-top: 30rpx; }
.pb-10 { padding-bottom: 10rpx; }
.pb-20 { padding-bottom: 20rpx; }
.pb-30 { padding-bottom: 30rpx; }
.pl-10 { padding-left: 10rpx; }
.pl-20 { padding-left: 20rpx; }
.pr-10 { padding-right: 10rpx; }
.pr-20 { padding-right: 20rpx; }

/* 通用文本样式 */
.text-primary { color: #3cc51f; }
.text-secondary { color: #969799; }
.text-success { color: #07c160; }
.text-warning { color: #ff976a; }
.text-danger { color: #ee0a24; }

.text-xs { font-size: 20rpx; }
.text-sm { font-size: 24rpx; }
.text-base { font-size: 28rpx; }
.text-lg { font-size: 32rpx; }
.text-xl { font-size: 36rpx; }

.text-bold { font-weight: bold; }
.text-center { text-align: center; }
.text-right { text-align: right; }

/* 通用布局 */
.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { justify-content: center; align-items: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }
.flex-1 { flex: 1; }

/* 通用圆角 */
.rounded { border-radius: 8rpx; }
.rounded-lg { border-radius: 16rpx; }
.rounded-full { border-radius: 50%; }

/* 通用阴影 */
.shadow-sm {
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.shadow {
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.shadow-lg {
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.15);
}
