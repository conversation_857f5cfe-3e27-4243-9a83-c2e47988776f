// 地址管理页面
const app = getApp();
const { addressAPI } = require('../../utils/api.js');

Page({
  data: {
    addresses: [],
    loading: true
  },

  onLoad() {
    this.fetchAddresses()
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    this.fetchAddresses()
  },

  // 获取地址列表
  async fetchAddresses() {
    try {
      this.setData({
        loading: true
      });

      const addresses = await addressAPI.getAddresses();
      this.setData({
        addresses: addresses || [],
        loading: false
      });
    } catch (error) {
      console.error('获取地址失败:', error);
      this.setData({
        loading: false
      });
      wx.showToast({
        title: error.message || '获取地址失败',
        icon: 'none'
      });
    }
  },

  // 设置默认地址
  async setDefault(e) {
    const { id } = e.currentTarget.dataset
    try {
      wx.showLoading({
        title: '设置中',
      })
      await addressAPI.setDefaultAddress(id);
      await this.fetchAddresses();
      wx.hideLoading();
      wx.showToast({
        title: '设置成功'
      });
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '设置失败',
        icon: 'none'
      });
    }
  },

  // 删除地址
  async deleteAddress(e) {
    const { id } = e.currentTarget.dataset
    try {
      const res = await wx.showModal({
        title: '提示',
        content: '确定要删除此地址吗？',
        confirmText: '删除',
        confirmColor: '#FF0000'
      })

      if (res.confirm) {
        wx.showLoading({
          title: '删除中',
        })
        await addressAPI.deleteAddress(id);
        await this.fetchAddresses();
        wx.hideLoading();
        wx.showToast({
          title: '删除成功'
        });
      }
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '删除失败',
        icon: 'none'
      });
    }
  },

  // 添加新地址
  addAddress() {
    wx.navigateTo({
      url: '/pages/address-edit/address-edit'
    })
  },

  // 编辑地址
  editAddress(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/address-edit/address-edit?id=${id}`
    })
  }
})
