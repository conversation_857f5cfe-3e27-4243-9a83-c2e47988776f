const app = getApp();
const { serviceAPI, orderAPI, addressAPI } = require('../../utils/api.js');

Page({
  data: {
    serviceId: null,
    service: {},
    selectedAddress: null,
    addresses: [],
    appointmentDate: '',
    appointmentTime: '',
    quantity: 1,
    remark: '',
    contactPhone: '',
    totalAmount: 0,
    couponList: [],
    selectedCoupon: null,
    discountAmount: 0,
    finalAmount: 0,
    timeSlots: [
      { time: '09:00-10:00', available: true },
      { time: '10:00-11:00', available: true },
      { time: '11:00-12:00', available: false },
      { time: '14:00-15:00', available: true },
      { time: '15:00-16:00', available: true },
      { time: '16:00-17:00', available: true },
      { time: '17:00-18:00', available: true },
      { time: '18:00-19:00', available: false }
    ],
    showDatePicker: false,
    showAddressPicker: false,
    showCouponPicker: false,
    minDate: new Date().getTime(),
    maxDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).getTime()
  },

  onLoad(options) {
    if (options.serviceId) {
      this.setData({
        serviceId: options.serviceId
      });
      this.loadServiceDetail();
      this.loadAddresses();
      this.loadCoupons();
    }
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
  },

  // 加载服务详情
  async loadServiceDetail() {
    try {
      const service = await serviceAPI.getServiceDetail(this.data.serviceId);
      this.setData({
        service,
        totalAmount: service.price,
        finalAmount: service.price
      });
    } catch (error) {
      console.error('加载服务详情失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    }
  },

  // 加载地址列表
  async loadAddresses() {
    try {
      const addresses = await addressAPI.getAddresses();
      const defaultAddress = addresses.find(addr => addr.isDefault);

      this.setData({
        addresses,
        selectedAddress: defaultAddress || addresses[0]
      });
    } catch (error) {
      console.error('加载地址失败:', error);
    }
  },

  // 加载优惠券
  async loadCoupons() {
    try {
      const coupons = await serviceAPI.getAvailableCoupons(this.data.serviceId);
      this.setData({
        couponList: coupons || []
      });
    } catch (error) {
      console.error('加载优惠券失败:', error);
    }
  },

  // 选择地址
  onSelectAddress() {
    this.setData({
      showAddressPicker: true
    });
  },

  // 地址选择确认
  onAddressConfirm(e) {
    const index = e.detail.value;
    this.setData({
      selectedAddress: this.data.addresses[index],
      showAddressPicker: false
    });
  },

  // 选择日期
  onSelectDate() {
    this.setData({
      showDatePicker: true
    });
  },

  // 日期选择确认
  onDateConfirm(e) {
    const date = new Date(e.detail.value);
    const dateStr = date.toISOString().split('T')[0];

    this.setData({
      appointmentDate: dateStr,
      showDatePicker: false,
      appointmentTime: '' // 重置时间选择
    });

    // 加载该日期的可用时间段
    this.loadAvailableTimeSlots(dateStr);
  },

  // 加载可用时间段
  async loadAvailableTimeSlots(date) {
    try {
      const timeSlots = await serviceAPI.getAvailableTimeSlots(this.data.serviceId, date);
      this.setData({
        timeSlots
      });
    } catch (error) {
      console.error('加载时间段失败:', error);
    }
  },

  // 选择时间段
  onTimeSlotSelect(e) {
    const time = e.currentTarget.dataset.time;
    const available = e.currentTarget.dataset.available;

    if (!available) {
      wx.showToast({
        title: '该时间段不可用',
        icon: 'none'
      });
      return;
    }

    this.setData({
      appointmentTime: time
    });
  },

  // 数量变更
  onQuantityChange(e) {
    const type = e.currentTarget.dataset.type;
    let quantity = this.data.quantity;

    if (type === 'minus' && quantity > 1) {
      quantity--;
    } else if (type === 'plus') {
      quantity++;
    }

    this.updateAmount(quantity);
  },

  // 更新金额
  updateAmount(quantity) {
    const totalAmount = this.data.service.price * quantity;
    const discountAmount = this.calculateDiscount(totalAmount);
    const finalAmount = totalAmount - discountAmount;

    this.setData({
      quantity,
      totalAmount,
      discountAmount,
      finalAmount
    });
  },

  // 计算优惠金额
  calculateDiscount(amount) {
    const coupon = this.data.selectedCoupon;
    if (!coupon) return 0;

    if (coupon.type === 'FIXED') {
      return Math.min(coupon.value, amount);
    } else if (coupon.type === 'PERCENT') {
      return amount * coupon.value / 100;
    }

    return 0;
  },

  // 选择优惠券
  onSelectCoupon() {
    this.setData({
      showCouponPicker: true
    });
  },

  // 优惠券选择
  onCouponSelect(e) {
    const index = e.currentTarget.dataset.index;
    const coupon = index >= 0 ? this.data.couponList[index] : null;

    this.setData({
      selectedCoupon: coupon,
      showCouponPicker: false
    });

    this.updateAmount(this.data.quantity);
  },

  // 备注输入
  onRemarkInput(e) {
    this.setData({
      remark: e.detail.value
    });
  },

  // 联系电话输入
  onPhoneInput(e) {
    this.setData({
      contactPhone: e.detail.value
    });
  },

  // 提交订单
  async onSubmitOrder() {
    // 验证必填项
    if (!this.data.selectedAddress) {
      wx.showToast({
        title: '请选择服务地址',
        icon: 'none'
      });
      return;
    }

    if (!this.data.appointmentDate) {
      wx.showToast({
        title: '请选择服务日期',
        icon: 'none'
      });
      return;
    }

    if (!this.data.appointmentTime) {
      wx.showToast({
        title: '请选择服务时间',
        icon: 'none'
      });
      return;
    }

    if (!this.data.contactPhone) {
      wx.showToast({
        title: '请输入联系电话',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({
        title: '提交中...'
      });

      const orderData = {
        serviceId: this.data.serviceId,
        addressId: this.data.selectedAddress.id,
        appointmentDate: this.data.appointmentDate,
        appointmentTime: this.data.appointmentTime,
        quantity: this.data.quantity,
        remark: this.data.remark,
        contactPhone: this.data.contactPhone,
        couponId: this.data.selectedCoupon?.id,
        totalAmount: this.data.totalAmount,
        discountAmount: this.data.discountAmount,
        finalAmount: this.data.finalAmount
      };

      const order = await orderAPI.createOrder(orderData);

      wx.hideLoading();
      wx.showToast({
        title: '订单提交成功',
        icon: 'success'
      });

      setTimeout(() => {
        wx.navigateTo({
          url: `/pages/payment/payment?orderId=${order.id}`
        });
      }, 1500);

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '提交失败',
        icon: 'none'
      });
    }
  },

  // 添加新地址
  onAddAddress() {
    wx.navigateTo({
      url: '/pages/address-edit/address-edit'
    });
  }
});