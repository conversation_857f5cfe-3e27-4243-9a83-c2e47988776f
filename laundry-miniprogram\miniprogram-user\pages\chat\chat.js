const app = getApp();
const { chatAPI, commonAPI } = require('../../utils/api.js');

Page({
  data: {
    merchantId: null,
    orderId: null,
    merchant: {},
    messages: [],
    inputText: '',
    inputHeight: 50,
    scrollTop: 0,
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 20,
    socketConnected: false,
    socketTask: null
  },

  onLoad(options) {
    if (options.merchantId) {
      this.setData({
        merchantId: options.merchantId,
        orderId: options.orderId || null
      });
      this.loadMerchantInfo();
      this.loadChatHistory();
      this.connectWebSocket();
    }
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
  },

  onUnload() {
    // 断开WebSocket连接
    if (this.data.socketTask) {
      this.data.socketTask.close();
    }
  },

  // 加载商家信息
  async loadMerchantInfo() {
    try {
      const merchant = await chatAPI.getMerchantInfo(this.data.merchantId);
      this.setData({ merchant });
    } catch (error) {
      console.error('加载商家信息失败:', error);
    }
  },

  // 加载聊天历史
  async loadChatHistory() {
    if (this.data.loading || !this.data.hasMore) return;

    try {
      this.setData({ loading: true });

      const params = {
        merchantId: this.data.merchantId,
        page: this.data.page,
        pageSize: this.data.pageSize
      };

      const result = await chatAPI.getChatHistory(params);
      const newMessages = result.list || result || [];

      // 倒序插入消息（最新的在下面）
      const messages = this.data.page === 1 ?
        newMessages.reverse() :
        [...newMessages.reverse(), ...this.data.messages];

      this.setData({
        messages,
        hasMore: newMessages.length === this.data.pageSize,
        page: this.data.page + 1,
        loading: false
      });

      // 滚动到底部
      if (this.data.page === 2) {
        this.scrollToBottom();
      }

    } catch (error) {
      console.error('加载聊天历史失败:', error);
      this.setData({ loading: false });
    }
  },

  // 连接WebSocket
  connectWebSocket() {
    const token = wx.getStorageSync('user_token');
    const socketUrl = `ws://localhost:8081/ws/chat?token=${token}&merchantId=${this.data.merchantId}`;

    const socketTask = wx.connectSocket({
      url: socketUrl,
      success: () => {
        console.log('WebSocket连接成功');
      },
      fail: (error) => {
        console.error('WebSocket连接失败:', error);
      }
    });

    socketTask.onOpen(() => {
      console.log('WebSocket连接已打开');
      this.setData({
        socketConnected: true,
        socketTask
      });
    });

    socketTask.onMessage((res) => {
      try {
        const message = JSON.parse(res.data);
        this.addMessage(message);
      } catch (error) {
        console.error('解析消息失败:', error);
      }
    });

    socketTask.onClose(() => {
      console.log('WebSocket连接已关闭');
      this.setData({
        socketConnected: false,
        socketTask: null
      });

      // 尝试重连
      setTimeout(() => {
        this.connectWebSocket();
      }, 3000);
    });

    socketTask.onError((error) => {
      console.error('WebSocket错误:', error);
    });
  },

  // 添加消息到列表
  addMessage(message) {
    const messages = [...this.data.messages, message];
    this.setData({ messages });
    this.scrollToBottom();
  },

  // 滚动到底部
  scrollToBottom() {
    this.setData({
      scrollTop: this.data.messages.length * 100
    });
  },

  // 输入框内容变化
  onInputChange(e) {
    this.setData({
      inputText: e.detail.value
    });
  },

  // 输入框高度变化
  onInputHeightChange(e) {
    this.setData({
      inputHeight: Math.max(50, e.detail.height)
    });
  },

  // 发送文本消息
  async onSendMessage() {
    const text = this.data.inputText.trim();
    if (!text) return;

    const message = {
      id: Date.now(),
      type: 'text',
      content: text,
      senderId: app.globalData.userInfo.id,
      senderType: 'USER',
      merchantId: this.data.merchantId,
      orderId: this.data.orderId,
      timestamp: new Date().toISOString(),
      status: 'sending'
    };

    // 立即显示消息
    this.addMessage(message);
    this.setData({ inputText: '' });

    try {
      // 通过WebSocket发送
      if (this.data.socketConnected) {
        this.data.socketTask.send({
          data: JSON.stringify(message)
        });
      } else {
        // 通过API发送
        await chatAPI.sendMessage({
          merchantId: this.data.merchantId,
          orderId: this.data.orderId,
          type: 'text',
          content: text
        });
      }

      // 更新消息状态为已发送
      const messages = this.data.messages.map(msg =>
        msg.id === message.id ? { ...msg, status: 'sent' } : msg
      );
      this.setData({ messages });

    } catch (error) {
      console.error('发送消息失败:', error);

      // 更新消息状态为失败
      const messages = this.data.messages.map(msg =>
        msg.id === message.id ? { ...msg, status: 'failed' } : msg
      );
      this.setData({ messages });

      wx.showToast({
        title: '发送失败',
        icon: 'none'
      });
    }
  },

  // 发送图片
  onSendImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        const tempFilePath = res.tempFilePaths[0];

        try {
          wx.showLoading({
            title: '发送中...'
          });

          // 上传图片
          const imageUrl = await commonAPI.uploadFile(tempFilePath);

          const message = {
            id: Date.now(),
            type: 'image',
            content: imageUrl,
            senderId: app.globalData.userInfo.id,
            senderType: 'USER',
            merchantId: this.data.merchantId,
            orderId: this.data.orderId,
            timestamp: new Date().toISOString(),
            status: 'sent'
          };

          // 发送消息
          if (this.data.socketConnected) {
            this.data.socketTask.send({
              data: JSON.stringify(message)
            });
          } else {
            await chatAPI.sendMessage({
              merchantId: this.data.merchantId,
              orderId: this.data.orderId,
              type: 'image',
              content: imageUrl
            });
          }

          this.addMessage(message);
          wx.hideLoading();

        } catch (error) {
          wx.hideLoading();
          console.error('发送图片失败:', error);
          wx.showToast({
            title: '发送失败',
            icon: 'none'
          });
        }
      }
    });
  },

  // 预览图片
  onPreviewImage(e) {
    const url = e.currentTarget.dataset.url;
    wx.previewImage({
      current: url,
      urls: [url]
    });
  },

  // 重新发送消息
  onResendMessage(e) {
    const messageId = e.currentTarget.dataset.id;
    const message = this.data.messages.find(msg => msg.id === messageId);

    if (message) {
      // 更新消息状态
      const messages = this.data.messages.map(msg =>
        msg.id === messageId ? { ...msg, status: 'sending' } : msg
      );
      this.setData({ messages });

      // 重新发送
      this.resendMessage(message);
    }
  },

  // 重新发送消息
  async resendMessage(message) {
    try {
      if (this.data.socketConnected) {
        this.data.socketTask.send({
          data: JSON.stringify(message)
        });
      } else {
        await chatAPI.sendMessage({
          merchantId: this.data.merchantId,
          orderId: this.data.orderId,
          type: message.type,
          content: message.content
        });
      }

      // 更新消息状态为已发送
      const messages = this.data.messages.map(msg =>
        msg.id === message.id ? { ...msg, status: 'sent' } : msg
      );
      this.setData({ messages });

    } catch (error) {
      console.error('重新发送失败:', error);

      // 更新消息状态为失败
      const messages = this.data.messages.map(msg =>
        msg.id === message.id ? { ...msg, status: 'failed' } : msg
      );
      this.setData({ messages });
    }
  },

  // 上拉加载更多
  onScrollToUpper() {
    this.loadChatHistory();
  }
});