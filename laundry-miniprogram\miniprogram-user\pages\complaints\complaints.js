// 用户端投诉页面
const app = getApp();
const { orderAPI, commonAPI } = require('../../utils/api.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 投诉列表
    complaints: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,

    // 筛选条件
    statusFilter: 'ALL',
    statusOptions: [
      { value: 'ALL', label: '全部' },
      { value: 'PENDING', label: '待处理' },
      { value: 'PROCESSING', label: '处理中' },
      { value: 'RESOLVED', label: '已解决' },
      { value: 'CLOSED', label: '已关闭' }
    ],

    // 显示新建投诉弹窗
    showCreateModal: false,

    // 新建投诉表单
    createForm: {
      orderId: '',
      type: 'SERVICE',
      title: '',
      content: '',
      images: []
    },

    // 投诉类型选项
    typeOptions: [
      { value: 'SERVICE', label: '服务质量' },
      { value: 'DELIVERY', label: '配送问题' },
      { value: 'PRICE', label: '价格争议' },
      { value: 'ATTITUDE', label: '服务态度' },
      { value: 'OTHER', label: '其他问题' }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 如果从订单页面跳转过来，预填订单ID
    if (options.orderId) {
      this.setData({
        'createForm.orderId': options.orderId,
        showCreateModal: true
      });
    }

    this.loadComplaints();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    // 刷新投诉列表
    this.resetAndLoad();
  },

  /**
   * 重置并加载
   */
  resetAndLoad() {
    this.setData({
      complaints: [],
      page: 1,
      hasMore: true
    });
    this.loadComplaints();
  },

  /**
   * 加载投诉列表
   */
  async loadComplaints() {
    if (this.data.loading || !this.data.hasMore) return;

    try {
      this.setData({ loading: true });

      const params = {
        page: this.data.page,
        pageSize: this.data.pageSize,
        status: this.data.statusFilter === 'ALL' ? undefined : this.data.statusFilter
      };

      // 模拟API调用，实际应该调用后端接口
      const result = await this.mockGetComplaints(params);
      const newComplaints = result.list || [];
      const complaints = this.data.page === 1 ? newComplaints : [...this.data.complaints, ...newComplaints];

      this.setData({
        complaints,
        hasMore: newComplaints.length === this.data.pageSize,
        page: this.data.page + 1,
        loading: false
      });

    } catch (error) {
      console.error('加载投诉列表失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 模拟获取投诉列表API
   */
  async mockGetComplaints(params) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockData = [
          {
            id: 1,
            orderId: 'LD202412280001',
            type: 'SERVICE',
            typeText: '服务质量',
            title: '衣物清洗不干净',
            content: '送回来的衣物还有污渍，要求重新清洗',
            status: 'PENDING',
            statusText: '待处理',
            createTime: '2024-12-28 10:30:00',
            replyTime: null,
            reply: null
          },
          {
            id: 2,
            orderId: 'LD202412270002',
            type: 'DELIVERY',
            typeText: '配送问题',
            title: '配送延迟',
            content: '约定时间内没有送达，影响了我的安排',
            status: 'RESOLVED',
            statusText: '已解决',
            createTime: '2024-12-27 15:20:00',
            replyTime: '2024-12-27 16:45:00',
            reply: '非常抱歉给您带来不便，我们已经调整了配送安排，并给您补偿了优惠券。'
          }
        ];

        resolve({
          list: mockData,
          total: mockData.length
        });
      }, 500);
    });
  },

  /**
   * 状态筛选改变
   */
  onStatusFilterChange(e) {
    this.setData({
      statusFilter: e.detail.value
    });
    this.resetAndLoad();
  },

  /**
   * 投诉项点击
   */
  onComplaintTap(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/complaint-detail/complaint-detail?id=${id}`
    });
  },

  /**
   * 显示新建投诉弹窗
   */
  onShowCreateModal() {
    this.setData({
      showCreateModal: true,
      createForm: {
        orderId: '',
        type: 'SERVICE',
        title: '',
        content: '',
        images: []
      }
    });
  },

  /**
   * 隐藏新建投诉弹窗
   */
  onHideCreateModal() {
    this.setData({
      showCreateModal: false
    });
  },

  /**
   * 表单输入
   */
  onFormInput(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    this.setData({
      [`createForm.${field}`]: value
    });
  },

  /**
   * 投诉类型选择
   */
  onTypeChange(e) {
    this.setData({
      'createForm.type': e.detail.value
    });
  },

  /**
   * 选择图片
   */
  onChooseImage() {
    const maxImages = 3;
    const currentCount = this.data.createForm.images.length;

    if (currentCount >= maxImages) {
      wx.showToast({
        title: `最多只能上传${maxImages}张图片`,
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: maxImages - currentCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const images = [...this.data.createForm.images, ...res.tempFilePaths];
        this.setData({
          'createForm.images': images
        });
      }
    });
  },

  /**
   * 删除图片
   */
  onDeleteImage(e) {
    const index = e.currentTarget.dataset.index;
    const images = [...this.data.createForm.images];
    images.splice(index, 1);
    this.setData({
      'createForm.images': images
    });
  },

  /**
   * 提交投诉
   */
  async onSubmitComplaint() {
    const form = this.data.createForm;

    // 表单验证
    if (!form.orderId.trim()) {
      wx.showToast({
        title: '请输入订单号',
        icon: 'none'
      });
      return;
    }

    if (!form.title.trim()) {
      wx.showToast({
        title: '请输入投诉标题',
        icon: 'none'
      });
      return;
    }

    if (!form.content.trim()) {
      wx.showToast({
        title: '请输入投诉内容',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({
        title: '提交中...'
      });

      // 上传图片
      const imageUrls = [];
      for (let imagePath of form.images) {
        try {
          const uploadResult = await commonAPI.uploadFile(imagePath);
          imageUrls.push(uploadResult.url);
        } catch (error) {
          console.error('图片上传失败:', error);
        }
      }

      // 提交投诉
      const complaintData = {
        ...form,
        images: imageUrls
      };

      // 模拟API调用
      await this.mockCreateComplaint(complaintData);

      wx.hideLoading();
      wx.showToast({
        title: '投诉提交成功',
        icon: 'success'
      });

      // 关闭弹窗并刷新列表
      this.onHideCreateModal();
      this.resetAndLoad();

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '提交失败',
        icon: 'none'
      });
    }
  },

  /**
   * 模拟创建投诉API
   */
  async mockCreateComplaint(data) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ success: true });
      }, 1000);
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.resetAndLoad();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    this.loadComplaints();
  }
});