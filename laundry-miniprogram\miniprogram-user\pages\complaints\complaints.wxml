<!-- 用户端投诉页面 -->
<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <view class="header-content">
      <text class="page-title">我的投诉</text>
      <button class="create-btn" bindtap="onShowCreateModal">新建投诉</button>
    </view>

    <!-- 状态筛选 -->
    <view class="filter-section">
      <picker mode="selector" range="{{statusOptions}}" range-key="label" value="{{statusFilter}}" bindchange="onStatusFilterChange">
        <view class="filter-picker">
          <text class="filter-text">{{statusOptions[statusFilter].label || '全部'}}</text>
          <text class="filter-arrow">▼</text>
        </view>
      </picker>
    </view>
  </view>

  <!-- 投诉列表 -->
  <view class="complaints-list">
    <view wx:for="{{complaints}}" wx:key="id" class="complaint-item" bindtap="onComplaintTap" data-id="{{item.id}}">
      <view class="complaint-header">
        <view class="complaint-title">{{item.title}}</view>
        <view class="complaint-status {{item.status}}">{{item.statusText}}</view>
      </view>

      <view class="complaint-info">
        <text class="complaint-type">{{item.typeText}}</text>
        <text class="complaint-order">订单号：{{item.orderId}}</text>
      </view>

      <view class="complaint-content">{{item.content}}</view>

      <view class="complaint-footer">
        <text class="complaint-time">{{item.createTime}}</text>
        <view wx:if="{{item.status === 'RESOLVED' && item.reply}}" class="has-reply">已回复</view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{!loading && complaints.length === 0}}" class="empty-state">
      <image src="/images/empty-complaints.png" class="empty-image"></image>
      <text class="empty-text">暂无投诉记录</text>
      <button class="empty-btn" bindtap="onShowCreateModal">新建投诉</button>
    </view>

    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-state">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 没有更多 -->
    <view wx:if="{{!hasMore && complaints.length > 0}}" class="no-more">
      <text class="no-more-text">没有更多了</text>
    </view>
  </view>
</view>

<!-- 新建投诉弹窗 -->
<view wx:if="{{showCreateModal}}" class="modal-overlay" bindtap="onHideCreateModal">
  <view class="modal-content" catchtap="">
    <view class="modal-header">
      <text class="modal-title">新建投诉</text>
      <text class="modal-close" bindtap="onHideCreateModal">×</text>
    </view>

    <scroll-view class="modal-body" scroll-y>
      <!-- 订单号 -->
      <view class="form-item">
        <text class="form-label">订单号 *</text>
        <input class="form-input" placeholder="请输入订单号" value="{{createForm.orderId}}" bindinput="onFormInput" data-field="orderId" />
      </view>

      <!-- 投诉类型 -->
      <view class="form-item">
        <text class="form-label">投诉类型 *</text>
        <picker mode="selector" range="{{typeOptions}}" range-key="label" value="{{createForm.type}}" bindchange="onTypeChange">
          <view class="form-picker">
            <text class="picker-text">{{typeOptions[createForm.type].label || '请选择'}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <!-- 投诉标题 -->
      <view class="form-item">
        <text class="form-label">投诉标题 *</text>
        <input class="form-input" placeholder="请输入投诉标题" value="{{createForm.title}}" bindinput="onFormInput" data-field="title" />
      </view>

      <!-- 投诉内容 -->
      <view class="form-item">
        <text class="form-label">投诉内容 *</text>
        <textarea class="form-textarea" placeholder="请详细描述您遇到的问题" value="{{createForm.content}}" bindinput="onFormInput" data-field="content"></textarea>
      </view>

      <!-- 上传图片 -->
      <view class="form-item">
        <text class="form-label">相关图片（最多3张）</text>
        <view class="image-upload">
          <view wx:for="{{createForm.images}}" wx:key="*this" class="image-item">
            <image src="{{item}}" class="upload-image" mode="aspectFill"></image>
            <text class="delete-image" bindtap="onDeleteImage" data-index="{{index}}">×</text>
          </view>
          <view wx:if="{{createForm.images.length < 3}}" class="upload-btn" bindtap="onChooseImage">
            <text class="upload-icon">+</text>
            <text class="upload-text">添加图片</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <view class="modal-footer">
      <button class="cancel-btn" bindtap="onHideCreateModal">取消</button>
      <button class="submit-btn" bindtap="onSubmitComplaint">提交投诉</button>
    </view>
  </view>
</view>