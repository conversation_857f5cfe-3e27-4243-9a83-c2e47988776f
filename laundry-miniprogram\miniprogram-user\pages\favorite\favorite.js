const app = getApp();
const { serviceAPI } = require('../../utils/api.js');

Page({
  data: {
    favorites: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    editMode: false,
    selectedItems: []
  },

  onLoad() {
    this.loadFavorites();
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
    
    // 刷新收藏列表
    this.resetAndLoad();
  },

  // 重置并加载
  resetAndLoad() {
    this.setData({
      favorites: [],
      page: 1,
      hasMore: true,
      editMode: false,
      selectedItems: []
    });
    this.loadFavorites();
  },

  // 加载收藏列表
  async loadFavorites() {
    if (this.data.loading || !this.data.hasMore) return;

    try {
      this.setData({ loading: true });

      const params = {
        page: this.data.page,
        pageSize: this.data.pageSize
      };

      const result = await serviceAPI.getFavorites(params);
      const newFavorites = result.list || result || [];
      const favorites = this.data.page === 1 ? newFavorites : [...this.data.favorites, ...newFavorites];

      this.setData({
        favorites,
        hasMore: newFavorites.length === this.data.pageSize,
        page: this.data.page + 1,
        loading: false
      });

    } catch (error) {
      console.error('加载收藏列表失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    }
  },

  // 服务项点击
  onServiceTap(e) {
    if (this.data.editMode) return;
    
    const serviceId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/service-detail/service-detail?id=${serviceId}`
    });
  },

  // 切换编辑模式
  onToggleEditMode() {
    this.setData({
      editMode: !this.data.editMode,
      selectedItems: []
    });
  },

  // 选择项目
  onSelectItem(e) {
    const id = e.currentTarget.dataset.id;
    const selectedItems = [...this.data.selectedItems];
    const index = selectedItems.indexOf(id);
    
    if (index > -1) {
      selectedItems.splice(index, 1);
    } else {
      selectedItems.push(id);
    }
    
    this.setData({ selectedItems });
  },

  // 全选/取消全选
  onSelectAll() {
    const allSelected = this.data.selectedItems.length === this.data.favorites.length;
    const selectedItems = allSelected ? [] : this.data.favorites.map(item => item.id);
    
    this.setData({ selectedItems });
  },

  // 批量删除
  async onBatchDelete() {
    if (this.data.selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要删除的项目',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的 ${this.data.selectedItems.length} 个收藏吗？`,
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({
              title: '删除中...'
            });

            await serviceAPI.batchRemoveFavorites(this.data.selectedItems);

            wx.hideLoading();
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });

            // 刷新列表
            this.resetAndLoad();

          } catch (error) {
            wx.hideLoading();
            wx.showToast({
              title: error.message || '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 单个删除
  async onDeleteItem(e) {
    const id = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个收藏吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({
              title: '删除中...'
            });

            await serviceAPI.removeFavorite(id);

            wx.hideLoading();
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });

            // 从列表中移除
            const favorites = this.data.favorites.filter(item => item.id !== id);
            this.setData({ favorites });

          } catch (error) {
            wx.hideLoading();
            wx.showToast({
              title: error.message || '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.resetAndLoad();
    wx.stopPullDownRefresh();
  },

  // 触底加载
  onReachBottom() {
    this.loadFavorites();
  }
});
