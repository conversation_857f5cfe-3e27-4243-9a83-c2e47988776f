// 用户端首页
const app = getApp();
const { serviceAPI, merchantAPI, commonAPI } = require('../../utils/api.js');

Page({
  data: {
    banners: [],
    categories: [],
    recommendServices: [],
    nearbyMerchants: [],
    loading: true,
    userLocation: null
  },

  onLoad() {
    this.initPage();
  },

  onShow() {
    // 页面显示时刷新数据
    if (app.globalData.isLoggedIn) {
      this.loadRecommendServices();
    }
  },

  onPullDownRefresh() {
    this.initPage().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 初始化页面
  async initPage() {
    try {
      this.setData({ loading: true });

      // 并行加载数据
      await Promise.all([
        this.loadBanners(),
        this.loadCategories(),
        this.getUserLocation()
      ]);

      // 加载推荐服务和附近商家
      await Promise.all([
        this.loadRecommendServices(),
        this.loadNearbyMerchants()
      ]);

    } catch (error) {
      console.error('页面初始化失败:', error);
      app.showError('页面加载失败，请重试');
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载轮播图
  async loadBanners() {
    try {
      const banners = await commonAPI.getBanners();
      this.setData({ banners });
    } catch (error) {
      console.error('加载轮播图失败:', error);
      // 设置默认轮播图
      this.setData({
        banners: []
      });
    }
  },

  // 加载服务分类
  async loadCategories() {
    try {
      const categories = await serviceAPI.getServiceCategories();
      this.setData({ categories });
    } catch (error) {
      console.error('加载服务分类失败:', error);
      // 设置默认分类
      this.setData({
        categories: []
      });
    }
  },

  // 获取用户位置
  getUserLocation() {
    return new Promise((resolve, reject) => {
      wx.getLocation({
        type: 'gcj02',
        success: (res) => {
          console.log('用户位置:', res);
          this.setData({
            userLocation: {
              latitude: res.latitude,
              longitude: res.longitude
            }
          });
          resolve(res);
        },
        fail: (error) => {
          console.error('获取位置失败:', error);
          // 位置获取失败不影响其他功能
          resolve(null);
        }
      });
    });
  },

  // 加载推荐服务
  async loadRecommendServices() {
    try {
      const services = await serviceAPI.getRecommendServices();
      this.setData({
        recommendServices: services || []
      });
    } catch (error) {
      console.error('加载推荐服务失败:', error);
      this.setData({
        recommendServices: []
      });
    }
  },

  // 加载附近商家
  async loadNearbyMerchants() {
    try {
      const { userLocation } = this.data;
      if (!userLocation) {
        return;
      }

      const merchants = await merchantAPI.getNearbyMerchants({
        latitude: userLocation.latitude,
        longitude: userLocation.longitude,
        radius: 10000 // 10公里
      });

      this.setData({
        nearbyMerchants: merchants || []
      });
    } catch (error) {
      console.error('加载附近商家失败:', error);
      this.setData({
        nearbyMerchants: []
      });
    }
  },

  // 搜索点击
  onSearchTap() {
    wx.navigateTo({
      url: '/pages/search/search'
    });
  },

  // 分类点击
  onCategoryTap(e) {
    const categoryId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/services/services?categoryId=${categoryId}`
    });
  },

  // 服务点击
  onServiceTap(e) {
    const serviceId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/service-detail/service-detail?id=${serviceId}`
    });
  },

  // 商家点击
  onMerchantTap(e) {
    const merchantId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/merchant-detail/merchant-detail?id=${merchantId}`
    });
  },

  // 更多商家
  onMoreMerchantsTap() {
    wx.navigateTo({
      url: '/pages/merchants/merchants'
    });
  }
});
