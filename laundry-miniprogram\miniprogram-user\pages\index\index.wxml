<view class="container">
  <!-- 轮播图 -->
  <swiper class="banner" indicator-dots="true" autoplay="true" interval="3000" duration="500">
    <swiper-item wx:for="{{banners}}" wx:key="id">
      <image src="{{item.image}}" mode="aspectFill" class="banner-image"></image>
    </swiper-item>
  </swiper>

  <!-- 搜索框 -->
  <view class="search-container">
    <view class="search-box" bindtap="onSearchTap">
      <icon type="search" size="16" color="#999"></icon>
      <text class="search-text">搜索洗护服务</text>
    </view>
  </view>

  <!-- 服务分类 -->
  <view class="category-section">
    <view class="section-title">服务分类</view>
    <view class="category-grid">
      <view class="category-item" wx:for="{{categories}}" wx:key="id" bindtap="onCategoryTap" data-id="{{item.id}}">
        <image src="{{item.icon}}" class="category-icon"></image>
        <text class="category-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 推荐服务 -->
  <view class="recommend-section">
    <view class="section-title">推荐服务</view>
    <scroll-view class="recommend-scroll" scroll-x="true">
      <view class="recommend-item" wx:for="{{recommendServices}}" wx:key="id" bindtap="onServiceTap" data-id="{{item.id}}">
        <image src="{{item.image}}" class="recommend-image"></image>
        <view class="recommend-info">
          <text class="recommend-title">{{item.title}}</text>
          <text class="recommend-price">¥{{item.price}}</text>
          <text class="recommend-rating">★{{item.rating}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 附近商家 -->
  <view class="nearby-section">
    <view class="section-title">
      <text>附近商家</text>
      <text class="more-btn" bindtap="onMoreMerchantsTap">更多</text>
    </view>
    <view class="merchant-list">
      <view class="merchant-item" wx:for="{{nearbyMerchants}}" wx:key="id" bindtap="onMerchantTap" data-id="{{item.id}}">
        <image src="{{item.avatar}}" class="merchant-avatar"></image>
        <view class="merchant-info">
          <text class="merchant-name">{{item.name}}</text>
          <text class="merchant-distance">{{item.distance}}km</text>
          <text class="merchant-rating">★{{item.rating}} ({{item.reviewCount}})</text>
        </view>
        <view class="merchant-services">
          <text class="service-tag" wx:for="{{item.services}}" wx:key="*this">{{item}}</text>
        </view>
      </view>
    </view>
  </view>
</view>
