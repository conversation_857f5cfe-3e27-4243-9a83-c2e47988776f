.container {
  padding: 0;
  background-color: #f5f5f5;
}

/* 轮播图 */
.banner {
  width: 100%;
  height: 400rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 搜索框 */
.search-container {
  padding: 20rpx;
  background-color: #fff;
}

.search-box {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 50rpx;
}

.search-text {
  margin-left: 20rpx;
  color: #999;
  font-size: 28rpx;
}

/* 服务分类 */
.category-section {
  margin-top: 20rpx;
  padding: 30rpx 20rpx;
  background-color: #fff;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.category-name {
  font-size: 24rpx;
  color: #666;
}

/* 推荐服务 */
.recommend-section {
  margin-top: 20rpx;
  padding: 30rpx 20rpx;
  background-color: #fff;
}

.recommend-scroll {
  white-space: nowrap;
}

.recommend-item {
  display: inline-block;
  width: 280rpx;
  margin-right: 20rpx;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.recommend-image {
  width: 100%;
  height: 200rpx;
}

.recommend-info {
  padding: 20rpx;
}

.recommend-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.recommend-price {
  display: block;
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.recommend-rating {
  font-size: 24rpx;
  color: #ffa500;
}

/* 附近商家 */
.nearby-section {
  margin-top: 20rpx;
  padding: 30rpx 20rpx;
  background-color: #fff;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.more-btn {
  font-size: 24rpx;
  color: #3cc51f;
}

.merchant-list {
  margin-top: 30rpx;
}

.merchant-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.merchant-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.merchant-info {
  flex: 1;
}

.merchant-name {
  display: block;
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.merchant-distance {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 5rpx;
}

.merchant-rating {
  font-size: 24rpx;
  color: #ffa500;
}

.merchant-services {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.service-tag {
  font-size: 20rpx;
  color: #3cc51f;
  background-color: #f0f9ff;
  padding: 5rpx 10rpx;
  border-radius: 10rpx;
  margin-bottom: 5rpx;
}
