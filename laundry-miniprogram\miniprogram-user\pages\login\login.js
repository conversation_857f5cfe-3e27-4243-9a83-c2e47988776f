// 用户端登录页面
const app = getApp();
const { authAPI } = require('../../utils/api.js');

Page({
  data: {
    phone: '',
    password: '',
    agreed: false,
    loginLoading: false,
    wxLoginLoading: false,
    canLogin: false,
    showPassword: false
  },

  onLoad() {
    // 检查是否已登录
    if (app.globalData.isLoggedIn) {
      wx.switchTab({
        url: '/pages/index/index'
      });
    }
  },

  // 手机号输入
  onPhoneInput(e) {
    const phone = e.detail.value;
    this.setData({
      phone
    });
    this.checkCanLogin();
  },

  // 密码输入
  onPasswordInput(e) {
    const password = e.detail.value;
    this.setData({
      password
    });
    this.checkCanLogin();
  },

  // 切换密码显示
  onTogglePassword() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  // 检查是否可以登录
  checkCanLogin() {
    const { phone, password, agreed } = this.data;
    const phoneRegex = /^1[3-9]\d{9}$/;
    const canLogin = phoneRegex.test(phone) && password.length >= 6 && agreed;

    this.setData({
      canLogin
    });
  },

  // 协议勾选
  onAgreementChange(e) {
    const agreed = e.detail.value.includes('agree');
    this.setData({
      agreed
    });
    this.checkCanLogin();
  },

  // 手机号密码登录
  async onLogin() {
    if (!this.data.canLogin || this.data.loginLoading) {
      return;
    }

    this.setData({
      loginLoading: true
    });

    try {
      const loginResult = await authAPI.login({
        phone: this.data.phone,
        password: this.data.password,
        loginType: 'PASSWORD'
      });

      // 保存登录信息
      app.saveLoginInfo(loginResult);

      app.showSuccess('登录成功');

      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        });
      }, 1500);

    } catch (error) {
      console.error('登录失败:', error);
      app.showError(error.message || '登录失败，请重试');
    } finally {
      this.setData({
        loginLoading: false
      });
    }
  },

  // 微信登录
  async onWechatLogin() {
    if (!this.data.agreed) {
      app.showError('请先同意用户协议');
      return;
    }

    if (this.data.wxLoginLoading) {
      return;
    }

    this.setData({
      wxLoginLoading: true
    });

    try {
      // 调用app的微信登录方法
      await app.wxLogin();

      app.showSuccess('登录成功');

      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        });
      }, 1500);

    } catch (error) {
      console.error('微信登录失败:', error);

      if (error.errMsg && error.errMsg.includes('getUserProfile:fail auth deny')) {
        app.showError('需要授权才能登录');
      } else {
        app.showError(error.message || '微信登录失败，请重试');
      }
    } finally {
      this.setData({
        wxLoginLoading: false
      });
    }
  },

  // 获取手机号登录
  async onGetPhoneNumber(e) {
    if (!this.data.agreed) {
      app.showError('请先同意用户协议');
      return;
    }

    if (e.detail.code) {
      try {
        app.showLoading('登录中...');

        // 先进行微信登录获取用户信息
        const wxLoginResult = await app.wxLogin();

        // 绑定手机号
        await authAPI.bindPhone(e.detail.code);

        app.hideLoading();
        app.showSuccess('登录成功');

        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }, 1500);

      } catch (error) {
        app.hideLoading();
        console.error('手机号登录失败:', error);
        app.showError(error.message || '登录失败，请重试');
      }
    } else {
      app.showError('需要授权手机号才能登录');
    }
  },

  // 注册
  onRegister() {
    wx.navigateTo({
      url: '/pages/register/register'
    });
  },

  // 忘记密码
  onForgotPassword() {
    wx.navigateTo({
      url: '/pages/forgot-password/forgot-password'
    });
  },

  // 用户协议
  onUserAgreement() {
    wx.navigateTo({
      url: '/pages/agreement/user-agreement'
    });
  },

  // 隐私政策
  onPrivacyPolicy() {
    wx.navigateTo({
      url: '/pages/agreement/privacy-policy'
    });
  }
});
