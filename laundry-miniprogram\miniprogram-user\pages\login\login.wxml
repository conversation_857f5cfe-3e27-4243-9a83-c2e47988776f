<view class="container">
  <view class="logo-section">
    <image src="/images/logo.png" class="logo"></image>
    <text class="app-name">洗护服务</text>
    <text class="app-desc">专业的洗护服务平台</text>
  </view>

  <view class="login-form">
    <view class="input-group">
      <input 
        type="text" 
        placeholder="请输入手机号" 
        value="{{phone}}" 
        bindinput="onPhoneInput"
        class="input-field"
        maxlength="11"
      />
    </view>
    
    <view class="input-group">
      <input 
        type="password" 
        placeholder="请输入密码" 
        value="{{password}}" 
        bindinput="onPasswordInput"
        class="input-field"
      />
    </view>

    <button class="login-btn" bindtap="onLogin" disabled="{{!canLogin}}">
      {{loginLoading ? '登录中...' : '登录'}}
    </button>

    <view class="login-options">
      <text class="register-link" bindtap="onRegister">还没有账号？立即注册</text>
      <text class="forgot-link" bindtap="onForgotPassword">忘记密码？</text>
    </view>
  </view>

  <view class="quick-login">
    <view class="divider">
      <text class="divider-text">或</text>
    </view>
    
    <button class="wechat-login-btn" open-type="getUserInfo" bindgetuserinfo="onWechatLogin">
      <image src="/images/wechat-icon.png" class="wechat-icon"></image>
      微信快速登录
    </button>
  </view>

  <view class="agreement">
    <checkbox-group bindchange="onAgreementChange">
      <checkbox value="agree" checked="{{agreed}}" class="agreement-checkbox"/>
    </checkbox-group>
    <text class="agreement-text">
      我已阅读并同意
      <text class="link" bindtap="onUserAgreement">《用户协议》</text>
      和
      <text class="link" bindtap="onPrivacyPolicy">《隐私政策》</text>
    </text>
  </view>
</view>
