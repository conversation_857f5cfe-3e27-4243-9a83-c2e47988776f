/* 用户端登录页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0 60rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Logo区域 */
.logo-section {
  text-align: center;
  margin-bottom: 100rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
}

.app-desc {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 登录表单 */
.login-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.input-group {
  margin-bottom: 40rpx;
  position: relative;
}

.input-field {
  width: 100%;
  height: 88rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 30rpx;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.input-field:focus {
  border-color: #3cc51f;
  background: #ffffff;
}

.input-field::placeholder {
  color: #999;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #3cc51f 0%, #2aa515 100%);
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 15rpx rgba(60, 197, 31, 0.3);
}

.login-btn:disabled {
  background: #cccccc;
  box-shadow: none;
}

.login-btn:not(:disabled):active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 10rpx rgba(60, 197, 31, 0.3);
}

/* 登录选项 */
.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.register-link,
.forgot-link {
  font-size: 28rpx;
  color: #666;
  text-decoration: none;
}

.register-link {
  color: #3cc51f;
}

/* 快速登录 */
.quick-login {
  margin-bottom: 40rpx;
}

.divider {
  text-align: center;
  margin: 40rpx 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2rpx;
  background: rgba(255, 255, 255, 0.3);
}

.divider-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: rgba(255, 255, 255, 0.8);
  padding: 0 20rpx;
  font-size: 24rpx;
  position: relative;
  z-index: 1;
}

.wechat-login-btn {
  width: 100%;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  border-radius: 12rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.wechat-login-btn:active {
  background: rgba(255, 255, 255, 1);
  transform: translateY(2rpx);
}

.wechat-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

/* 协议区域 */
.agreement {
  display: flex;
  align-items: flex-start;
  padding: 0 20rpx;
}

.agreement-checkbox {
  margin-right: 20rpx;
  margin-top: 6rpx;
}

.agreement-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  flex: 1;
}

.link {
  color: #ffffff;
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-height: 600px) {
  .container {
    padding-top: 40rpx;
    padding-bottom: 40rpx;
  }
  
  .logo-section {
    margin-bottom: 60rpx;
  }
  
  .logo {
    width: 100rpx;
    height: 100rpx;
  }
  
  .app-name {
    font-size: 40rpx;
  }
}

/* 动画效果 */
.login-form {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.logo-section {
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 加载状态 */
.login-btn[loading] {
  position: relative;
  color: transparent;
}

.login-btn[loading]::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40rpx;
  height: 40rpx;
  margin: -20rpx 0 0 -20rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top-color: #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
