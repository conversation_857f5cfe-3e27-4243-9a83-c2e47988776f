const app = getApp();
const { orderAPI } = require('../../utils/api.js');

Page({
  data: {
    orderId: null,
    order: {},
    statusSteps: [],
    loading: true,
    showCancelModal: false,
    cancelReason: '',
    cancelReasons: [
      '不需要了',
      '商家服务态度不好',
      '价格太贵',
      '时间不合适',
      '其他原因'
    ],
    showEvaluateModal: false,
    evaluation: {
      rating: 5,
      content: '',
      images: []
    }
  },

  onLoad(options) {
    if (options.id) {
      this.setData({
        orderId: options.id
      });
      this.loadOrderDetail();
    }
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    // 刷新订单详情
    if (this.data.orderId) {
      this.loadOrderDetail();
    }
  },

  // 加载订单详情
  async loadOrderDetail() {
    try {
      this.setData({ loading: true });

      const order = await orderAPI.getOrderDetail(this.data.orderId);
      const statusSteps = this.generateStatusSteps(order);

      this.setData({
        order,
        statusSteps,
        loading: false
      });

    } catch (error) {
      console.error('加载订单详情失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    }
  },

  // 生成状态步骤
  generateStatusSteps(order) {
    const steps = [
      { title: '订单提交', desc: '您已成功提交订单', completed: true },
      { title: '商家接单', desc: '商家已确认接单', completed: false },
      { title: '服务进行中', desc: '商家正在提供服务', completed: false },
      { title: '服务完成', desc: '服务已完成，请确认', completed: false }
    ];

    switch (order.status) {
      case 'PENDING':
        steps[0].completed = true;
        break;
      case 'ACCEPTED':
        steps[0].completed = true;
        steps[1].completed = true;
        break;
      case 'IN_PROGRESS':
        steps[0].completed = true;
        steps[1].completed = true;
        steps[2].completed = true;
        break;
      case 'COMPLETED':
        steps.forEach(step => step.completed = true);
        break;
    }

    return steps;
  },

  // 联系商家
  onContactMerchant() {
    wx.navigateTo({
      url: `/pages/chat/chat?merchantId=${this.data.order.merchantId}`
    });
  },

  // 取消订单
  onCancelOrder() {
    this.setData({
      showCancelModal: true,
      cancelReason: ''
    });
  },

  // 关闭取消弹窗
  onCloseCancelModal() {
    this.setData({
      showCancelModal: false
    });
  },

  // 选择取消原因
  onCancelReasonSelect(e) {
    const reason = e.currentTarget.dataset.reason;
    this.setData({
      cancelReason: reason
    });
  },

  // 确认取消订单
  async onConfirmCancel() {
    if (!this.data.cancelReason) {
      wx.showToast({
        title: '请选择取消原因',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({
        title: '取消中...'
      });

      await orderAPI.cancelOrder(this.data.orderId, this.data.cancelReason);

      wx.hideLoading();
      wx.showToast({
        title: '订单已取消',
        icon: 'success'
      });

      this.setData({
        showCancelModal: false
      });

      // 刷新订单详情
      this.loadOrderDetail();

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '取消失败',
        icon: 'none'
      });
    }
  },

  // 确认完成
  async onConfirmComplete() {
    wx.showModal({
      title: '确认完成',
      content: '确认服务已完成？',
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({
              title: '确认中...'
            });

            await orderAPI.confirmOrder(this.data.orderId);

            wx.hideLoading();
            wx.showToast({
              title: '确认成功',
              icon: 'success'
            });

            // 刷新订单详情
            this.loadOrderDetail();

          } catch (error) {
            wx.hideLoading();
            wx.showToast({
              title: error.message || '确认失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 评价订单
  onEvaluateOrder() {
    this.setData({
      showEvaluateModal: true,
      evaluation: {
        rating: 5,
        content: '',
        images: []
      }
    });
  },

  // 关闭评价弹窗
  onCloseEvaluateModal() {
    this.setData({
      showEvaluateModal: false
    });
  },

  // 评分选择
  onRatingSelect(e) {
    const rating = e.currentTarget.dataset.rating;
    this.setData({
      'evaluation.rating': rating
    });
  },

  // 评价内容输入
  onEvaluationInput(e) {
    this.setData({
      'evaluation.content': e.detail.value
    });
  },

  // 选择评价图片
  onChooseEvaluationImages() {
    wx.chooseImage({
      count: 3 - this.data.evaluation.images.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const images = [...this.data.evaluation.images, ...res.tempFilePaths];
        this.setData({
          'evaluation.images': images
        });
      }
    });
  },

  // 删除评价图片
  onDeleteEvaluationImage(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.evaluation.images.filter((_, i) => i !== index);
    this.setData({
      'evaluation.images': images
    });
  },

  // 提交评价
  async onSubmitEvaluation() {
    const { rating, content } = this.data.evaluation;

    if (!content.trim()) {
      wx.showToast({
        title: '请输入评价内容',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({
        title: '提交中...'
      });

      await orderAPI.evaluateOrder(this.data.orderId, {
        rating,
        content: content.trim(),
        images: this.data.evaluation.images
      });

      wx.hideLoading();
      wx.showToast({
        title: '评价成功',
        icon: 'success'
      });

      this.setData({
        showEvaluateModal: false
      });

      // 刷新订单详情
      this.loadOrderDetail();

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '评价失败',
        icon: 'none'
      });
    }
  },

  // 再次下单
  onOrderAgain() {
    wx.navigateTo({
      url: `/pages/service-detail/service-detail?id=${this.data.order.serviceId}`
    });
  },

  // 申请退款
  onRequestRefund() {
    wx.navigateTo({
      url: `/pages/refund/refund?orderId=${this.data.orderId}`
    });
  },

  // 查看物流
  onViewLogistics() {
    wx.navigateTo({
      url: `/pages/logistics/logistics?orderId=${this.data.orderId}`
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadOrderDetail();
    wx.stopPullDownRefresh();
  }
});