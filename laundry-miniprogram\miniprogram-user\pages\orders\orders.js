const app = getApp();
const { orderAPI } = require('../../utils/api.js');

Page({
  data: {
    currentTab: 0,
    tabs: [
      { key: 'all', name: '全部' },
      { key: 'pending', name: '待付款' },
      { key: 'processing', name: '进行中' },
      { key: 'completed', name: '已完成' },
      { key: 'cancelled', name: '已取消' }
    ],
    orders: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10
  },

  onLoad(options) {
    // 从参数获取初始tab
    if (options.tab) {
      const tabIndex = this.data.tabs.findIndex(tab => tab.key === options.tab);
      if (tabIndex !== -1) {
        this.setData({
          currentTab: tabIndex
        });
      }
    }

    this.loadOrders();
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    // 刷新订单列表
    this.resetAndLoad();
  },

  // Tab切换
  onTabChange(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentTab: index
    });
    this.resetAndLoad();
  },

  // 重置并加载
  resetAndLoad() {
    this.setData({
      orders: [],
      page: 1,
      hasMore: true
    });
    this.loadOrders();
  },

  // 加载订单
  loadOrders() {
    if (this.data.loading || !this.data.hasMore) {
      return;
    }

    this.setData({
      loading: true
    });

    const currentTab = this.data.tabs[this.data.currentTab];
    const params = {
      page: this.data.page,
      pageSize: this.data.pageSize,
      status: currentTab.key === 'all' ? undefined : currentTab.key
    };

    orderAPI.getOrders(params).then(res => {
      const newOrders = res.list || res || [];
      const orders = this.data.page === 1 ? newOrders : [...this.data.orders, ...newOrders];

      this.setData({
        orders,
        hasMore: newOrders.length === this.data.pageSize,
        page: this.data.page + 1
      });
    }).catch(err => {
      console.error('加载订单失败:', err);
      wx.showToast({
        title: err.message || '加载失败',
        icon: 'none'
      });
    }).finally(() => {
      this.setData({
        loading: false
      });
    });
  },

  // 订单详情
  onOrderTap(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?id=${orderId}`
    });
  },

  // 取消订单
  onCancelOrder(e) {
    const orderId = e.currentTarget.dataset.id;

    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？',
      success: (res) => {
        if (res.confirm) {
          this.cancelOrder(orderId);
        }
      }
    });
  },

  // 执行取消订单
  cancelOrder(orderId) {
    wx.showLoading({
      title: '取消中...'
    });

    orderAPI.cancelOrder(orderId, '用户主动取消').then(res => {
      wx.showToast({
        title: '取消成功',
        icon: 'success'
      });
      this.resetAndLoad();
    }).catch(err => {
      wx.showToast({
        title: err.message || '取消失败',
        icon: 'none'
      });
    }).finally(() => {
      wx.hideLoading();
    });
  },

  // 支付订单
  onPayOrder(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/payment/payment?orderId=${orderId}`
    });
  },

  // 确认完成
  onConfirmOrder(e) {
    const orderId = e.currentTarget.dataset.id;

    wx.showModal({
      title: '确认完成',
      content: '确认服务已完成？',
      success: (res) => {
        if (res.confirm) {
          this.confirmOrder(orderId);
        }
      }
    });
  },

  // 执行确认完成
  confirmOrder(orderId) {
    wx.showLoading({
      title: '确认中...'
    });

    orderAPI.confirmOrder(orderId).then(res => {
      wx.showToast({
        title: '确认成功',
        icon: 'success'
      });
      this.resetAndLoad();
    }).catch(err => {
      wx.showToast({
        title: err.message || '确认失败',
        icon: 'none'
      });
    }).finally(() => {
      wx.hideLoading();
    });
  },

  // 评价订单
  onEvaluateOrder(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/evaluation/evaluation?orderId=${orderId}`
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.resetAndLoad();
    wx.stopPullDownRefresh();
  },

  // 触底加载
  onReachBottom() {
    this.loadOrders();
  }
});