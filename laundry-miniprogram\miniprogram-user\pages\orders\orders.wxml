<view class="container">
  <!-- 订单状态标签 -->
  <view class="status-tabs">
    <view 
      class="status-tab {{currentStatus === item.value ? 'active' : ''}}"
      wx:for="{{statusTabs}}"
      wx:key="value"
      bindtap="onStatusTabTap"
      data-status="{{item.value}}"
    >
      <text class="tab-text">{{item.label}}</text>
      <view class="tab-badge" wx:if="{{item.count > 0}}">{{item.count}}</view>
    </view>
  </view>

  <!-- 订单列表 -->
  <scroll-view class="order-list" scroll-y="true" bindscrolltolower="onLoadMore">
    <view class="order-item" wx:for="{{orders}}" wx:key="id" bindtap="onOrderTap" data-id="{{item.id}}">
      <!-- 订单头部 -->
      <view class="order-header">
        <text class="order-number">订单号：{{item.orderNumber}}</text>
        <text class="order-status {{item.status}}">{{item.statusText}}</text>
      </view>

      <!-- 服务信息 -->
      <view class="service-info">
        <image src="{{item.service.image}}" class="service-image" mode="aspectFill"></image>
        <view class="service-details">
          <text class="service-title">{{item.service.title}}</text>
          <text class="service-desc">{{item.service.description}}</text>
          <view class="service-specs">
            <text class="spec-item" wx:for="{{item.specifications}}" wx:key="name">
              {{item.name}}: {{item.value}}
            </text>
          </view>
        </view>
        <view class="service-price">
          <text class="price-amount">¥{{item.totalAmount}}</text>
          <text class="price-quantity">x{{item.quantity}}</text>
        </view>
      </view>

      <!-- 商家信息 -->
      <view class="merchant-info">
        <image src="{{item.merchant.avatar}}" class="merchant-avatar"></image>
        <text class="merchant-name">{{item.merchant.name}}</text>
        <text class="merchant-phone" bindtap="onCallMerchant" data-phone="{{item.merchant.phone}}">
          <icon type="success" size="12"></icon>
          联系商家
        </text>
      </view>

      <!-- 订单时间 -->
      <view class="order-time">
        <text class="time-label">下单时间：</text>
        <text class="time-value">{{item.createTime}}</text>
      </view>

      <!-- 订单操作 -->
      <view class="order-actions">
        <!-- 待支付 -->
        <block wx:if="{{item.status === 'PENDING_PAYMENT'}}">
          <button class="action-btn cancel" bindtap="onCancelOrder" data-id="{{item.id}}">取消订单</button>
          <button class="action-btn primary" bindtap="onPayOrder" data-id="{{item.id}}">立即支付</button>
        </block>

        <!-- 待接单 -->
        <block wx:if="{{item.status === 'PENDING_ACCEPT'}}">
          <button class="action-btn cancel" bindtap="onCancelOrder" data-id="{{item.id}}">取消订单</button>
          <button class="action-btn" bindtap="onContactMerchant" data-id="{{item.id}}">联系商家</button>
        </block>

        <!-- 进行中 -->
        <block wx:if="{{item.status === 'IN_PROGRESS'}}">
          <button class="action-btn" bindtap="onViewProgress" data-id="{{item.id}}">查看进度</button>
          <button class="action-btn" bindtap="onContactMerchant" data-id="{{item.id}}">联系商家</button>
        </block>

        <!-- 待确认完成 -->
        <block wx:if="{{item.status === 'PENDING_COMPLETION'}}">
          <button class="action-btn" bindtap="onContactMerchant" data-id="{{item.id}}">联系商家</button>
          <button class="action-btn primary" bindtap="onConfirmCompletion" data-id="{{item.id}}">确认完成</button>
        </block>

        <!-- 已完成 -->
        <block wx:if="{{item.status === 'COMPLETED'}}">
          <button class="action-btn" bindtap="onReorder" data-id="{{item.id}}">再次下单</button>
          <button class="action-btn primary" bindtap="onReview" data-id="{{item.id}}" wx:if="{{!item.reviewed}}">评价</button>
        </block>

        <!-- 已取消/已退款 -->
        <block wx:if="{{item.status === 'CANCELLED' || item.status === 'REFUNDED'}}">
          <button class="action-btn" bindtap="onReorder" data-id="{{item.id}}">再次下单</button>
          <button class="action-btn" bindtap="onDeleteOrder" data-id="{{item.id}}">删除订单</button>
        </block>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore}}">
      <text wx:if="{{loading}}">加载中...</text>
      <text wx:else>上拉加载更多</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!orders.length && !loading}}">
      <image src="/images/empty-order.png" class="empty-image"></image>
      <text class="empty-text">暂无{{currentStatusText}}订单</text>
      <button class="empty-btn" bindtap="onGoShopping">去下单</button>
    </view>
  </scroll-view>
</view>
