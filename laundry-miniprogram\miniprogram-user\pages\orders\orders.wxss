/* 用户订单列表页面样式 */
.container {
  background: #f5f5f5;
  min-height: 100vh;
}

/* 状态标签栏 */
.status-tabs {
  background: #ffffff;
  display: flex;
  padding: 0 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.status-tab {
  flex: 1;
  text-align: center;
  padding: 30rpx 20rpx;
  position: relative;
  transition: all 0.3s ease;
}

.status-tab.active {
  color: #3cc51f;
}

.status-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #3cc51f;
  border-radius: 2rpx;
}

.tab-text {
  font-size: 28rpx;
  font-weight: 500;
}

.tab-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: #ff4d4f;
  color: #ffffff;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1.2;
}

/* 订单列表 */
.order-list {
  height: calc(100vh - 120rpx);
  padding: 20rpx;
}

.order-item {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.order-number {
  font-size: 28rpx;
  color: #666;
}

.order-status {
  font-size: 28rpx;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.order-status.PENDING_PAYMENT {
  color: #fa8c16;
  background: rgba(250, 140, 22, 0.1);
}

.order-status.PENDING_ACCEPT {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

.order-status.IN_PROGRESS {
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
}

.order-status.PENDING_COMPLETION {
  color: #722ed1;
  background: rgba(114, 46, 209, 0.1);
}

.order-status.COMPLETED {
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
}

.order-status.CANCELLED,
.order-status.REFUNDED {
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
}

/* 服务信息 */
.service-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.service-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.service-details {
  flex: 1;
  margin-right: 20rpx;
}

.service-title {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.service-desc {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.service-specs {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.spec-item {
  font-size: 22rpx;
  color: #666;
  background: #f5f5f5;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.service-price {
  text-align: right;
  flex-shrink: 0;
}

.price-amount {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #ff4d4f;
  margin-bottom: 4rpx;
}

.price-quantity {
  font-size: 24rpx;
  color: #999;
}

/* 商家信息 */
.merchant-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.merchant-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin-right: 20rpx;
}

.merchant-name {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.merchant-phone {
  font-size: 24rpx;
  color: #3cc51f;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

/* 订单时间 */
.order-time {
  margin-bottom: 20rpx;
  font-size: 24rpx;
  color: #999;
}

.time-label {
  margin-right: 10rpx;
}

/* 订单操作 */
.order-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #f5f5f5;
}

.action-btn {
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  border: 2rpx solid #e8e8e8;
  background: #ffffff;
  color: #666;
  min-width: 120rpx;
}

.action-btn.primary {
  background: #3cc51f;
  border-color: #3cc51f;
  color: #ffffff;
}

.action-btn.cancel {
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.action-btn:active {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx 0;
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.empty-btn {
  background: #3cc51f;
  color: #ffffff;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
}

/* 动画效果 */
.order-item {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 375px) {
  .order-list {
    padding: 15rpx;
  }
  
  .order-item {
    padding: 25rpx;
    margin-bottom: 15rpx;
  }
  
  .service-image {
    width: 100rpx;
    height: 100rpx;
  }
  
  .service-title {
    font-size: 28rpx;
  }
  
  .price-amount {
    font-size: 28rpx;
  }
  
  .action-btn {
    padding: 12rpx 24rpx;
    font-size: 24rpx;
    min-width: 100rpx;
  }
}
