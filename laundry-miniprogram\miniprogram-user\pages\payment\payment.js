const app = getApp();
const { orderAPI, paymentAPI } = require('../../utils/api.js');

Page({
  data: {
    orderId: null,
    order: {},
    paymentMethods: [
      {
        id: 'wechat',
        name: '微信支付',
        icon: '/images/wechat-pay.png',
        selected: true
      },
      {
        id: 'alipay',
        name: '支付宝',
        icon: '/images/alipay.png',
        selected: false
      },
      {
        id: 'balance',
        name: '余额支付',
        icon: '/images/balance.png',
        selected: false,
        balance: 0
      }
    ],
    selectedPaymentMethod: 'wechat',
    countdown: 0,
    countdownTimer: null,
    paying: false,
    loading: true
  },

  onLoad(options) {
    if (options.orderId) {
      this.setData({
        orderId: options.orderId
      });
      this.loadOrderDetail();
      this.startCountdown();
    }
  },

  onUnload() {
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer);
    }
  },

  // 加载订单详情
  async loadOrderDetail() {
    try {
      this.setData({ loading: true });

      const order = await orderAPI.getOrderDetail(this.data.orderId);

      // 获取用户余额
      const userInfo = await app.getUserInfo();
      const paymentMethods = this.data.paymentMethods.map(method => {
        if (method.id === 'balance') {
          return {
            ...method,
            balance: userInfo.balance || 0
          };
        }
        return method;
      });

      this.setData({
        order,
        paymentMethods,
        loading: false
      });

    } catch (error) {
      console.error('加载订单详情失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    }
  },

  // 开始倒计时
  startCountdown() {
    // 设置30分钟倒计时
    this.setData({
      countdown: 30 * 60
    });

    const timer = setInterval(() => {
      const countdown = this.data.countdown - 1;
      this.setData({
        countdown
      });

      if (countdown <= 0) {
        clearInterval(timer);
        this.onPaymentTimeout();
      }
    }, 1000);

    this.setData({
      countdownTimer: timer
    });
  },

  // 支付超时
  onPaymentTimeout() {
    wx.showModal({
      title: '支付超时',
      content: '订单支付已超时，请重新下单',
      showCancel: false,
      success: () => {
        wx.navigateBack();
      }
    });
  },

  // 格式化倒计时
  formatCountdown(seconds) {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  },

  // 选择支付方式
  onPaymentMethodSelect(e) {
    const methodId = e.currentTarget.dataset.id;

    const paymentMethods = this.data.paymentMethods.map(method => ({
      ...method,
      selected: method.id === methodId
    }));

    this.setData({
      paymentMethods,
      selectedPaymentMethod: methodId
    });
  },

  // 立即支付
  async onPay() {
    if (this.data.paying) return;

    const { selectedPaymentMethod, order } = this.data;

    // 检查余额支付
    if (selectedPaymentMethod === 'balance') {
      const balanceMethod = this.data.paymentMethods.find(m => m.id === 'balance');
      if (balanceMethod.balance < order.finalAmount) {
        wx.showToast({
          title: '余额不足',
          icon: 'none'
        });
        return;
      }
    }

    this.setData({ paying: true });

    try {
      wx.showLoading({
        title: '支付中...'
      });

      if (selectedPaymentMethod === 'wechat') {
        await this.wechatPay();
      } else if (selectedPaymentMethod === 'alipay') {
        await this.alipayPay();
      } else if (selectedPaymentMethod === 'balance') {
        await this.balancePay();
      }

    } catch (error) {
      console.error('支付失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: error.message || '支付失败',
        icon: 'none'
      });
    } finally {
      this.setData({ paying: false });
    }
  },

  // 微信支付
  async wechatPay() {
    try {
      const paymentData = await paymentAPI.createWechatPayment({
        orderId: this.data.orderId,
        paymentMethod: 'WECHAT'
      });

      wx.requestPayment({
        timeStamp: paymentData.timeStamp,
        nonceStr: paymentData.nonceStr,
        package: paymentData.package,
        signType: paymentData.signType,
        paySign: paymentData.paySign,
        success: () => {
          this.onPaymentSuccess();
        },
        fail: (error) => {
          if (error.errMsg !== 'requestPayment:fail cancel') {
            wx.showToast({
              title: '支付失败',
              icon: 'none'
            });
          }
        }
      });

    } catch (error) {
      throw error;
    }
  },

  // 支付宝支付
  async alipayPay() {
    try {
      const paymentData = await paymentAPI.createAlipayPayment({
        orderId: this.data.orderId,
        paymentMethod: 'ALIPAY'
      });

      // 调用支付宝支付
      wx.requestPayment({
        provider: 'alipay',
        orderInfo: paymentData.orderInfo,
        success: () => {
          this.onPaymentSuccess();
        },
        fail: (error) => {
          if (error.errMsg !== 'requestPayment:fail cancel') {
            wx.showToast({
              title: '支付失败',
              icon: 'none'
            });
          }
        }
      });

    } catch (error) {
      throw error;
    }
  },

  // 余额支付
  async balancePay() {
    try {
      await paymentAPI.balancePay({
        orderId: this.data.orderId,
        paymentMethod: 'BALANCE'
      });

      this.onPaymentSuccess();

    } catch (error) {
      throw error;
    }
  },

  // 支付成功
  onPaymentSuccess() {
    wx.hideLoading();

    // 清除倒计时
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer);
    }

    wx.showToast({
      title: '支付成功',
      icon: 'success'
    });

    setTimeout(() => {
      wx.redirectTo({
        url: `/pages/order-detail/order-detail?id=${this.data.orderId}`
      });
    }, 1500);
  },

  // 查看订单详情
  onViewOrderDetail() {
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?id=${this.data.orderId}`
    });
  },

  // 取消支付
  onCancel() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消支付吗？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  }
});