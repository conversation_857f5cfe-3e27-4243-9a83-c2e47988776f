// 用户个人中心页面
const app = getApp();
const { authAPI, orderAPI, serviceAPI } = require('../../utils/api.js');

Page({
  data: {
    userInfo: {},
    orderStats: {
      pending: 0,
      processing: 0,
      completed: 0,
      refund: 0,
      total: 0
    },
    menuItems: [],
    loading: true
  },

  onLoad() {
    this.initMenuItems();
    this.loadUserInfo();
    this.loadOrderStats();
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    // 每次显示页面时刷新数据
    this.loadUserInfo();
    this.loadOrderStats();
  },

  // 初始化菜单项
  initMenuItems() {
    const menuItems = [
      {
        id: 1,
        icon: 'location',
        title: '收货地址',
        url: '/pages/address/address'
      },
      {
        id: 2,
        icon: 'coupon',
        title: '优惠券',
        url: '/pages/coupon/coupon'
      },
      {
        id: 3,
        icon: 'gift',
        title: '积分商城',
        url: '/pages/points/points'
      },
      {
        id: 4,
        icon: 'heart',
        title: '我的收藏',
        url: '/pages/favorite/favorite'
      },
      {
        id: 5,
        icon: 'history',
        title: '浏览历史',
        url: '/pages/history/history'
      },
      {
        id: 6,
        icon: 'help',
        title: '帮助中心',
        url: '/pages/help/help'
      },
      {
        id: 7,
        icon: 'feedback',
        title: '意见反馈',
        url: '/pages/feedback/feedback'
      },
      {
        id: 8,
        icon: 'info',
        title: '关于我们',
        url: '/pages/about/about'
      }
    ];

    this.setData({
      menuItems
    });
  },

  // 加载用户信息
  loadUserInfo() {
    this.setData({
      loading: true
    });

    // 先显示本地缓存的用户信息
    const userInfo = app.globalData.userInfo || {};
    this.setData({
      userInfo: {
        ...userInfo,
        points: userInfo.points || 0,
        coupons: userInfo.coupons || 0,
        balance: userInfo.balance || '0.00',
        isVip: userInfo.isVip || false
      }
    });

    // 从服务器获取最新用户信息
    authAPI.getUserInfo().then(res => {
      this.setData({
        userInfo: res,
        loading: false
      });
      // 更新全局用户信息
      app.globalData.userInfo = res;
    }).catch(err => {
      console.error('获取用户信息失败:', err);
      this.setData({
        loading: false
      });
    });
  },

  // 加载订单统计
  loadOrderStats() {
    orderAPI.getOrders({
      page: 1,
      pageSize: 1,
      statsOnly: true
    }).then(res => {
      // 如果返回的是统计数据
      if (res.stats) {
        this.setData({
          orderStats: res.stats
        });
      } else {
        // 如果没有专门的统计接口，可以从订单列表中计算
        this.calculateOrderStats();
      }
    }).catch(err => {
      console.error('获取订单统计失败:', err);
      // 使用默认统计数据
      this.setData({
        orderStats: {
          pending: 0,
          processing: 0,
          completed: 0,
          refund: 0,
          total: 0
        }
      });
    });
  },

  // 计算订单统计（如果后端没有提供统计接口）
  calculateOrderStats() {
    Promise.all([
      orderAPI.getOrders({ status: 'pending', page: 1, pageSize: 1 }),
      orderAPI.getOrders({ status: 'processing', page: 1, pageSize: 1 }),
      orderAPI.getOrders({ status: 'completed', page: 1, pageSize: 1 }),
      orderAPI.getOrders({ page: 1, pageSize: 1 })
    ]).then(([pending, processing, completed, total]) => {
      this.setData({
        orderStats: {
          pending: pending.total || 0,
          processing: processing.total || 0,
          completed: completed.total || 0,
          refund: 0,
          total: total.total || 0
        }
      });
    }).catch(err => {
      console.error('计算订单统计失败:', err);
    });
  },

  // 编辑个人资料
  onEditProfile() {
    wx.navigateTo({
      url: '/pages/edit-profile/edit-profile'
    });
  },

  // 积分点击
  onPointsClick() {
    wx.navigateTo({
      url: '/pages/points/points'
    });
  },

  // 优惠券点击
  onCouponsClick() {
    wx.navigateTo({
      url: '/pages/coupon/coupon'
    });
  },

  // 余额点击
  onBalanceClick() {
    wx.navigateTo({
      url: '/pages/wallet/wallet'
    });
  },

  // 订单点击
  onOrdersClick(e) {
    const status = e.currentTarget.dataset.status;
    wx.navigateTo({
      url: `/pages/orders/orders?status=${status}`
    });
  },

  // 查看全部订单
  onAllOrdersClick() {
    wx.navigateTo({
      url: '/pages/orders/orders'
    });
  },

  // 菜单项点击
  onMenuClick(e) {
    const url = e.currentTarget.dataset.url;
    if (url) {
      wx.navigateTo({
        url: url
      });
    }
  },

  // 联系客服
  onContactService() {
    wx.makePhoneCall({
      phoneNumber: '************'
    });
  },

  // 设置
  onSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  },

  // 退出登录
  onLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          app.logout();
          wx.reLaunch({
            url: '/pages/login/login'
          });
        }
      }
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadUserInfo();
    this.loadOrderStats();

    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  }
});