// 用户个人中心页面
const app = getApp();

Page({
  data: {
    userInfo: {},
    orderStats: {
      pending: 0,
      processing: 0,
      completed: 0,
      refund: 0,
      total: 0
    },
    menuItems: [
      {
        id: 1,
        icon: '/images/address-icon.png',
        title: '收货地址',
        url: '/pages/address/address'
      },
      {
        id: 2,
        icon: '/images/coupon-icon.png',
        title: '优惠券',
        url: '/pages/coupon/coupon',
        badge: 3
      },
      {
        id: 3,
        icon: '/images/points-icon.png',
        title: '积分商城',
        url: '/pages/points/points'
      },
      {
        id: 4,
        icon: '/images/favorite-icon.png',
        title: '我的收藏',
        url: '/pages/favorite/favorite'
      },
      {
        id: 5,
        icon: '/images/history-icon.png',
        title: '浏览历史',
        url: '/pages/history/history'
      },
      {
        id: 6,
        icon: '/images/help-icon.png',
        title: '帮助中心',
        url: '/pages/help/help'
      },
      {
        id: 7,
        icon: '/images/feedback-icon.png',
        title: '意见反馈',
        url: '/pages/feedback/feedback'
      },
      {
        id: 8,
        icon: '/images/about-icon.png',
        title: '关于我们',
        url: '/pages/about/about'
      }
    ]
  },

  onLoad() {
    this.loadUserInfo();
    this.loadOrderStats();
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.loadUserInfo();
    this.loadOrderStats();
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = app.globalData.userInfo || {};
    this.setData({
      userInfo: {
        ...userInfo,
        points: userInfo.points || 0,
        coupons: userInfo.coupons || 0,
        balance: userInfo.balance || '0.00',
        isVip: userInfo.isVip || false
      }
    });

    // 从服务器获取最新用户信息
    app.request({
      url: '/user/profile',
      method: 'GET'
    }).then(res => {
      if (res.success) {
        this.setData({
          userInfo: res.data
        });
        // 更新全局用户信息
        app.globalData.userInfo = res.data;
      }
    }).catch(err => {
      console.error('获取用户信息失败:', err);
    });
  },

  // 加载订单统计
  loadOrderStats() {
    app.request({
      url: '/order/stats',
      method: 'GET'
    }).then(res => {
      if (res.success) {
        this.setData({
          orderStats: res.data
        });
      }
    }).catch(err => {
      console.error('获取订单统计失败:', err);
    });
  },

  // 编辑个人资料
  onEditProfile() {
    wx.navigateTo({
      url: '/pages/edit-profile/edit-profile'
    });
  },

  // 积分点击
  onPointsClick() {
    wx.navigateTo({
      url: '/pages/points/points'
    });
  },

  // 优惠券点击
  onCouponsClick() {
    wx.navigateTo({
      url: '/pages/coupon/coupon'
    });
  },

  // 余额点击
  onBalanceClick() {
    wx.navigateTo({
      url: '/pages/wallet/wallet'
    });
  },

  // 订单点击
  onOrdersClick(e) {
    const status = e.currentTarget.dataset.status;
    wx.navigateTo({
      url: `/pages/orders/orders?status=${status}`
    });
  },

  // 查看全部订单
  onAllOrdersClick() {
    wx.navigateTo({
      url: '/pages/orders/orders'
    });
  },

  // 菜单项点击
  onMenuClick(e) {
    const url = e.currentTarget.dataset.url;
    if (url) {
      wx.navigateTo({
        url: url
      });
    }
  },

  // 联系客服
  onContactService() {
    wx.makePhoneCall({
      phoneNumber: '************'
    });
  },

  // 设置
  onSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  },

  // 退出登录
  onLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          app.logout();
          wx.reLaunch({
            url: '/pages/login/login'
          });
        }
      }
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadUserInfo();
    this.loadOrderStats();

    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  }
});