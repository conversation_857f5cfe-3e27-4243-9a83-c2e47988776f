<!--用户个人中心页面-->
<view class="container">
  <!-- 用户信息头部 -->
  <view class="header">
    <view class="user-info">
      <image class="avatar" src="{{userInfo.avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
      <view class="info">
        <text class="nickname">{{userInfo.nickname || '未设置昵称'}}</text>
        <text class="phone">{{userInfo.phone || '未绑定手机'}}</text>
        <view class="vip-badge" wx:if="{{userInfo.isVip}}">
          <text class="vip-text">VIP</text>
        </view>
      </view>
    </view>
    <view class="edit-btn" bindtap="onEditProfile">
      <image src="/images/edit-icon.png" class="edit-icon"></image>
    </view>
  </view>

  <!-- 积分和优惠券 -->
  <view class="wallet-section">
    <view class="wallet-item" bindtap="onPointsClick">
      <view class="wallet-number">{{userInfo.points || 0}}</view>
      <view class="wallet-label">积分</view>
    </view>
    <view class="wallet-item" bindtap="onCouponsClick">
      <view class="wallet-number">{{userInfo.coupons || 0}}</view>
      <view class="wallet-label">优惠券</view>
    </view>
    <view class="wallet-item" bindtap="onBalanceClick">
      <view class="wallet-number">¥{{userInfo.balance || '0.00'}}</view>
      <view class="wallet-label">余额</view>
    </view>
  </view>

  <!-- 订单统计 -->
  <view class="stats">
    <view class="stats-header">
      <text class="stats-title">我的订单</text>
      <text class="stats-more" bindtap="onAllOrdersClick">查看全部</text>
    </view>
    <view class="stats-content">
      <view class="stat-item" bindtap="onOrdersClick" data-status="pending">
        <image class="stat-icon" src="/images/order-pending.png"></image>
        <text class="stat-number">{{orderStats.pending}}</text>
        <text class="stat-label">待付款</text>
      </view>
      <view class="stat-item" bindtap="onOrdersClick" data-status="processing">
        <image class="stat-icon" src="/images/order-processing.png"></image>
        <text class="stat-number">{{orderStats.processing}}</text>
        <text class="stat-label">服务中</text>
      </view>
      <view class="stat-item" bindtap="onOrdersClick" data-status="completed">
        <image class="stat-icon" src="/images/order-completed.png"></image>
        <text class="stat-number">{{orderStats.completed}}</text>
        <text class="stat-label">已完成</text>
      </view>
      <view class="stat-item" bindtap="onOrdersClick" data-status="refund">
        <image class="stat-icon" src="/images/order-refund.png"></image>
        <text class="stat-number">{{orderStats.refund}}</text>
        <text class="stat-label">退款/售后</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-group">
      <view class="menu-item" wx:for="{{menuItems}}" wx:key="id" bindtap="onMenuClick" data-url="{{item.url}}">
        <image class="menu-icon" src="{{item.icon}}"></image>
        <text class="menu-title">{{item.title}}</text>
        <view class="menu-badge" wx:if="{{item.badge}}">{{item.badge}}</view>
        <image class="arrow-icon" src="/images/arrow-right.png"></image>
      </view>
    </view>
  </view>

  <!-- 客服和设置 -->
  <view class="service-section">
    <view class="service-item" bindtap="onContactService">
      <image class="service-icon" src="/images/service-icon.png"></image>
      <text class="service-title">联系客服</text>
      <text class="service-desc">7×24小时在线服务</text>
      <image class="arrow-icon" src="/images/arrow-right.png"></image>
    </view>
    <view class="service-item" bindtap="onSettings">
      <image class="service-icon" src="/images/settings-icon.png"></image>
      <text class="service-title">设置</text>
      <text class="service-desc">账号安全、消息通知</text>
      <image class="arrow-icon" src="/images/arrow-right.png"></image>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section">
    <button class="logout-btn" bindtap="onLogout">退出登录</button>
  </view>
</view>