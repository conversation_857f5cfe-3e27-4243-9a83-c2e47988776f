/* 用户个人中心页面样式 */
.container {
  background: #f5f5f5;
  min-height: 100vh;
}

/* 用户信息头部 */
.header {
  background: linear-gradient(135deg, #3cc51f 0%, #2aa515 100%);
  padding: 60rpx 40rpx 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  margin-right: 30rpx;
}

.info {
  flex: 1;
  position: relative;
}

.nickname {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
}

.phone {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.vip-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  transform: translateY(-10rpx);
}

.vip-text {
  font-size: 20rpx;
  font-weight: bold;
  color: #333;
}

.edit-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 钱包区域 */
.wallet-section {
  background: #ffffff;
  margin: -20rpx 40rpx 20rpx;
  border-radius: 20rpx;
  padding: 40rpx;
  display: flex;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.wallet-item {
  flex: 1;
  text-align: center;
  position: relative;
}

.wallet-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  bottom: 20%;
  width: 2rpx;
  background: #e8e8e8;
}

.wallet-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.wallet-label {
  font-size: 24rpx;
  color: #999;
}

/* 订单统计 */
.stats {
  background: #ffffff;
  margin: 20rpx 40rpx;
  border-radius: 20rpx;
  padding: 40rpx;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.stats-more {
  font-size: 28rpx;
  color: #3cc51f;
}

.stats-content {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
}

.stat-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 10rpx;
}

.stat-number {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #3cc51f;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 功能菜单 */
.menu-section {
  margin: 20rpx 40rpx;
}

.menu-group {
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 2rpx solid #f5f5f5;
  position: relative;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: #f8f8f8;
}

.menu-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 30rpx;
}

.menu-title {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.menu-badge {
  background: #ff4d4f;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  margin-right: 20rpx;
  min-width: 32rpx;
  text-align: center;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

/* 客服和设置 */
.service-section {
  margin: 20rpx 40rpx;
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
}

.service-item {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 2rpx solid #f5f5f5;
  position: relative;
}

.service-item:last-child {
  border-bottom: none;
}

.service-item:active {
  background: #f8f8f8;
}

.service-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 30rpx;
}

.service-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.service-desc {
  font-size: 24rpx;
  color: #999;
  margin-left: 78rpx;
  margin-right: auto;
}

/* 退出登录 */
.logout-section {
  margin: 40rpx 40rpx 60rpx;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background: #ffffff;
  color: #ff4d4f;
  border: 2rpx solid #ff4d4f;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logout-btn:active {
  background: #ff4d4f;
  color: #ffffff;
}

/* 动画效果 */
.wallet-section {
  animation: slideUp 0.5s ease-out;
}

.stats,
.menu-section,
.service-section {
  animation: fadeIn 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 375px) {
  .header {
    padding: 50rpx 30rpx 30rpx;
  }
  
  .wallet-section,
  .stats,
  .menu-section,
  .service-section {
    margin-left: 30rpx;
    margin-right: 30rpx;
  }
  
  .wallet-number {
    font-size: 28rpx;
  }
  
  .wallet-label {
    font-size: 22rpx;
  }
}
