// 用户注册页面
const app = getApp();
const { authAPI } = require('../../utils/api.js');

Page({
  data: {
    phone: '',
    code: '',
    password: '',
    confirmPassword: '',
    nickname: '',
    agreed: false,
    canSendCode: false,
    canRegister: false,
    countdown: 0,
    registerLoading: false
  },

  onLoad() {
    // 页面加载
  },

  // 返回上一页
  onBack() {
    wx.navigateBack();
  },

  // 手机号输入
  onPhoneInput(e) {
    const phone = e.detail.value;
    this.setData({
      phone
    });
    this.checkCanSendCode();
    this.checkCanRegister();
  },

  // 验证码输入
  onCodeInput(e) {
    const code = e.detail.value;
    this.setData({
      code
    });
    this.checkCanRegister();
  },

  // 密码输入
  onPasswordInput(e) {
    const password = e.detail.value;
    this.setData({
      password
    });
    this.checkCanRegister();
  },

  // 确认密码输入
  onConfirmPasswordInput(e) {
    const confirmPassword = e.detail.value;
    this.setData({
      confirmPassword
    });
    this.checkCanRegister();
  },

  // 昵称输入
  onNicknameInput(e) {
    const nickname = e.detail.value;
    this.setData({
      nickname
    });
    this.checkCanRegister();
  },

  // 协议勾选
  onAgreementChange(e) {
    const agreed = e.detail.value.includes('agree');
    this.setData({
      agreed
    });
    this.checkCanRegister();
  },

  // 检查是否可以发送验证码
  checkCanSendCode() {
    const { phone } = this.data;
    const phoneRegex = /^1[3-9]\d{9}$/;
    const canSendCode = phoneRegex.test(phone);

    this.setData({
      canSendCode
    });
  },

  // 检查是否可以注册
  checkCanRegister() {
    const { phone, code, password, confirmPassword, nickname, agreed } = this.data;
    const phoneRegex = /^1[3-9]\d{9}$/;
    const codeRegex = /^\d{6}$/;

    const canRegister = phoneRegex.test(phone) &&
                       codeRegex.test(code) &&
                       password.length >= 6 &&
                       password === confirmPassword &&
                       nickname.trim().length >= 2 &&
                       agreed;

    this.setData({
      canRegister
    });
  },

  // 发送验证码
  async onSendCode() {
    if (!this.data.canSendCode || this.data.countdown > 0) {
      return;
    }

    try {
      await authAPI.sendSMS(this.data.phone, 'REGISTER');

      app.showSuccess('验证码已发送');

      // 开始倒计时
      this.startCountdown();

    } catch (error) {
      console.error('发送验证码失败:', error);
      app.showError(error.message || '发送验证码失败');
    }
  },

  // 开始倒计时
  startCountdown() {
    this.setData({
      countdown: 60
    });

    const timer = setInterval(() => {
      const countdown = this.data.countdown - 1;
      this.setData({
        countdown
      });

      if (countdown <= 0) {
        clearInterval(timer);
      }
    }, 1000);
  },

  // 注册
  async onRegister() {
    if (!this.data.canRegister || this.data.registerLoading) {
      return;
    }

    this.setData({
      registerLoading: true
    });

    try {
      const { phone, code, password, nickname } = this.data;

      const result = await authAPI.register({
        phone,
        code,
        password,
        nickname
      });

      // 保存登录信息
      app.saveLoginInfo(result);

      app.showSuccess('注册成功');

      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        });
      }, 1500);

    } catch (error) {
      console.error('注册失败:', error);
      app.showError(error.message || '注册失败，请重试');
    } finally {
      this.setData({
        registerLoading: false
      });
    }
  },

  // 去登录
  onGoLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  // 用户协议
  onUserAgreement() {
    wx.showModal({
      title: '用户协议',
      content: '这里是用户协议的内容...',
      showCancel: false
    });
  },

  // 隐私政策
  onPrivacyPolicy() {
    wx.showModal({
      title: '隐私政策',
      content: '这里是隐私政策的内容...',
      showCancel: false
    });
  }
});