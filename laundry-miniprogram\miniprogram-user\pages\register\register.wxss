/* 用户注册页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0 40rpx;
}

/* 头部 */
.header {
  display: flex;
  align-items: center;
  padding: 40rpx 0 60rpx;
  position: relative;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
}

.back-icon {
  width: 32rpx;
  height: 32rpx;
}

.page-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
}

/* 注册表单 */
.register-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.input-group {
  margin-bottom: 40rpx;
}

.input-field {
  width: 100%;
  height: 88rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 30rpx;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.input-field:focus {
  border-color: #3cc51f;
  background: #ffffff;
  box-shadow: 0 0 0 4rpx rgba(60, 197, 31, 0.1);
}

.input-field::placeholder {
  color: #999;
}

/* 验证码输入 */
.code-input-wrapper {
  display: flex;
  gap: 20rpx;
}

.code-input {
  flex: 1;
  height: 88rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 30rpx;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.code-input:focus {
  border-color: #3cc51f;
  background: #ffffff;
  box-shadow: 0 0 0 4rpx rgba(60, 197, 31, 0.1);
}

.code-btn {
  width: 200rpx;
  height: 88rpx;
  background: #3cc51f;
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 26rpx;
  transition: all 0.3s ease;
}

.code-btn.disabled {
  background: #cccccc;
  color: #999;
}

.code-btn:not(.disabled):active {
  transform: translateY(2rpx);
  background: #2aa515;
}

/* 注册按钮 */
.register-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #3cc51f 0%, #2aa515 100%);
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 15rpx rgba(60, 197, 31, 0.3);
}

.register-btn.disabled {
  background: #cccccc;
  box-shadow: none;
}

.register-btn:not(.disabled):active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 10rpx rgba(60, 197, 31, 0.3);
}

/* 登录链接 */
.login-link {
  text-align: center;
  font-size: 28rpx;
  color: #666;
}

.link-text {
  color: #3cc51f;
  font-weight: 500;
}

/* 协议区域 */
.agreement {
  display: flex;
  align-items: flex-start;
  padding: 0 20rpx;
  margin-bottom: 40rpx;
}

.agreement-checkbox {
  margin-right: 20rpx;
  margin-top: 6rpx;
}

.agreement-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  flex: 1;
}

.link {
  color: #ffffff;
  text-decoration: underline;
}

/* 动画效果 */
.register-form {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.header {
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-height: 600px) {
  .container {
    padding: 20rpx 40rpx;
  }
  
  .header {
    padding: 20rpx 0 40rpx;
  }
  
  .register-form {
    padding: 40rpx 30rpx;
  }
  
  .input-group {
    margin-bottom: 30rpx;
  }
  
  .input-field,
  .code-input,
  .register-btn {
    height: 80rpx;
    font-size: 30rpx;
  }
  
  .code-btn {
    height: 80rpx;
    font-size: 24rpx;
  }
}

/* 加载状态 */
.register-btn[loading] {
  position: relative;
  color: transparent;
}

.register-btn[loading]::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40rpx;
  height: 40rpx;
  margin: -20rpx 0 0 -20rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top-color: #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
