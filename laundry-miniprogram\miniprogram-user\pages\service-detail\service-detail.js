const app = getApp();
const { serviceAPI, orderAPI } = require('../../utils/api.js');

Page({
  data: {
    serviceId: null,
    service: {},
    merchant: {},
    reviews: [],
    currentImageIndex: 0,
    showImagePreview: false,
    quantity: 1,
    selectedSpecs: {},
    totalPrice: 0,
    isFavorite: false,
    loading: true,
    showBookingModal: false,
    bookingData: {
      appointmentTime: '',
      address: {},
      remark: '',
      contactPhone: ''
    },
    timeSlots: [
      '09:00-10:00',
      '10:00-11:00',
      '11:00-12:00',
      '14:00-15:00',
      '15:00-16:00',
      '16:00-17:00',
      '17:00-18:00',
      '18:00-19:00'
    ]
  },

  onLoad(options) {
    if (options.id) {
      this.setData({
        serviceId: options.id
      });
      this.loadServiceDetail();
    }
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
  },

  // 加载服务详情
  async loadServiceDetail() {
    try {
      this.setData({ loading: true });

      const service = await serviceAPI.getServiceDetail(this.data.serviceId);

      this.setData({
        service,
        merchant: service.merchant || {},
        totalPrice: service.price || 0,
        loading: false
      });

      // 加载评价
      this.loadReviews();
      // 检查收藏状态
      this.checkFavoriteStatus();

    } catch (error) {
      console.error('加载服务详情失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    }
  },

  // 加载评价
  async loadReviews() {
    try {
      const reviews = await serviceAPI.getServiceReviews(this.data.serviceId, {
        page: 1,
        pageSize: 10
      });

      this.setData({
        reviews: reviews.list || reviews || []
      });
    } catch (error) {
      console.error('加载评价失败:', error);
    }
  },

  // 检查收藏状态
  async checkFavoriteStatus() {
    try {
      const isFavorite = await serviceAPI.checkFavorite(this.data.serviceId);
      this.setData({ isFavorite });
    } catch (error) {
      console.error('检查收藏状态失败:', error);
    }
  },

  // 图片预览
  onImageTap(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.service.images || [];

    wx.previewImage({
      current: images[index],
      urls: images
    });
  },

  // 数量变更
  onQuantityChange(e) {
    const type = e.currentTarget.dataset.type;
    let quantity = this.data.quantity;

    if (type === 'minus' && quantity > 1) {
      quantity--;
    } else if (type === 'plus') {
      quantity++;
    }

    this.setData({
      quantity,
      totalPrice: (this.data.service.price * quantity).toFixed(2)
    });
  },

  // 收藏/取消收藏
  async onToggleFavorite() {
    try {
      if (this.data.isFavorite) {
        await serviceAPI.removeFavorite(this.data.serviceId);
        wx.showToast({
          title: '已取消收藏',
          icon: 'success'
        });
      } else {
        await serviceAPI.addFavorite(this.data.serviceId);
        wx.showToast({
          title: '收藏成功',
          icon: 'success'
        });
      }

      this.setData({
        isFavorite: !this.data.isFavorite
      });
    } catch (error) {
      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      });
    }
  },

  // 联系商家
  onContactMerchant() {
    wx.navigateTo({
      url: `/pages/chat/chat?merchantId=${this.data.merchant.id}`
    });
  },

  // 立即预订
  onBookNow() {
    this.setData({
      showBookingModal: true
    });
  },

  // 关闭预订弹窗
  onCloseBookingModal() {
    this.setData({
      showBookingModal: false
    });
  },

  // 选择时间
  onTimeSlotSelect(e) {
    const time = e.currentTarget.dataset.time;
    this.setData({
      'bookingData.appointmentTime': time
    });
  },

  // 选择地址
  onSelectAddress() {
    wx.navigateTo({
      url: '/pages/address/address?select=true'
    });
  },

  // 备注输入
  onRemarkInput(e) {
    this.setData({
      'bookingData.remark': e.detail.value
    });
  },

  // 联系电话输入
  onPhoneInput(e) {
    this.setData({
      'bookingData.contactPhone': e.detail.value
    });
  },

  // 确认预订
  async onConfirmBooking() {
    const { bookingData, service, quantity, totalPrice } = this.data;

    if (!bookingData.appointmentTime) {
      wx.showToast({
        title: '请选择服务时间',
        icon: 'none'
      });
      return;
    }

    if (!bookingData.contactPhone) {
      wx.showToast({
        title: '请输入联系电话',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({
        title: '提交中...'
      });

      const orderData = {
        serviceId: service.id,
        merchantId: service.merchantId,
        quantity,
        totalAmount: totalPrice,
        appointmentTime: bookingData.appointmentTime,
        address: bookingData.address,
        remark: bookingData.remark,
        contactPhone: bookingData.contactPhone
      };

      const order = await orderAPI.createOrder(orderData);

      wx.hideLoading();
      wx.showToast({
        title: '预订成功',
        icon: 'success'
      });

      setTimeout(() => {
        wx.navigateTo({
          url: `/pages/order-detail/order-detail?id=${order.id}`
        });
      }, 1500);

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '预订失败',
        icon: 'none'
      });
    }
  },

  // 查看更多评价
  onViewMoreReviews() {
    wx.navigateTo({
      url: `/pages/reviews/reviews?serviceId=${this.data.serviceId}`
    });
  },

  // 分享
  onShareAppMessage() {
    return {
      title: this.data.service.name,
      path: `/pages/service-detail/service-detail?id=${this.data.serviceId}`,
      imageUrl: this.data.service.images?.[0]
    };
  }
});