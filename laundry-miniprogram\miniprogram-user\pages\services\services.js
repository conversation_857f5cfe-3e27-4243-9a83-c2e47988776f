const app = getApp();

Page({
  data: {
    searchKeyword: '',
    showFilter: false,
    selectedCategory: null,
    minPrice: '',
    maxPrice: '',
    sortBy: 'default',
    services: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    categories: [
      { id: null, name: '全部' },
      { id: 1, name: '衣物清洗' },
      { id: 2, name: '干洗服务' },
      { id: 3, name: '鞋类护理' },
      { id: 4, name: '家纺清洗' },
      { id: 5, name: '特殊护理' }
    ],
    sortOptions: [
      { value: 'default', label: '默认排序' },
      { value: 'price_asc', label: '价格从低到高' },
      { value: 'price_desc', label: '价格从高到低' },
      { value: 'rating', label: '评分最高' },
      { value: 'distance', label: '距离最近' }
    ]
  },

  onLoad(options) {
    // 从首页传来的分类ID
    if (options.categoryId) {
      this.setData({
        selectedCategory: parseInt(options.categoryId)
      });
    }
    
    this.loadServices();
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 搜索
  onSearch() {
    this.resetAndLoad();
  },

  // 筛选切换
  onFilterTap() {
    this.setData({
      showFilter: !this.data.showFilter
    });
  },

  // 分类选择
  onCategorySelect(e) {
    const categoryId = e.currentTarget.dataset.id;
    this.setData({
      selectedCategory: categoryId
    });
  },

  // 价格输入
  onMinPriceInput(e) {
    this.setData({
      minPrice: e.detail.value
    });
  },

  onMaxPriceInput(e) {
    this.setData({
      maxPrice: e.detail.value
    });
  },

  // 排序选择
  onSortSelect(e) {
    const sortBy = e.currentTarget.dataset.value;
    this.setData({
      sortBy
    });
  },

  // 重置筛选
  onResetFilter() {
    this.setData({
      selectedCategory: null,
      minPrice: '',
      maxPrice: '',
      sortBy: 'default'
    });
  },

  // 应用筛选
  onApplyFilter() {
    this.setData({
      showFilter: false
    });
    this.resetAndLoad();
  },

  // 重置并加载
  resetAndLoad() {
    this.setData({
      services: [],
      page: 1,
      hasMore: true
    });
    this.loadServices();
  },

  // 加载服务
  loadServices() {
    if (this.data.loading || !this.data.hasMore) {
      return;
    }

    this.setData({
      loading: true
    });

    const params = {
      page: this.data.page,
      pageSize: this.data.pageSize,
      keyword: this.data.searchKeyword,
      categoryId: this.data.selectedCategory,
      minPrice: this.data.minPrice,
      maxPrice: this.data.maxPrice,
      sortBy: this.data.sortBy
    };

    app.request({
      url: '/services/search',
      method: 'GET',
      data: params
    }).then(res => {
      if (res.success) {
        const newServices = res.data.list || [];
        const services = this.data.page === 1 ? newServices : [...this.data.services, ...newServices];
        
        this.setData({
          services,
          hasMore: newServices.length === this.data.pageSize,
          page: this.data.page + 1
        });
      }
    }).catch(err => {
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }).finally(() => {
      this.setData({
        loading: false
      });
    });
  },

  // 加载更多
  onLoadMore() {
    this.loadServices();
  },

  // 服务点击
  onServiceTap(e) {
    const serviceId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/service-detail/service-detail?id=${serviceId}`
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.resetAndLoad();
    wx.stopPullDownRefresh();
  },

  // 触底加载
  onReachBottom() {
    this.loadServices();
  }
});
