<view class="container">
  <!-- 搜索和筛选 -->
  <view class="search-filter">
    <view class="search-box">
      <input 
        type="text" 
        placeholder="搜索服务" 
        value="{{searchKeyword}}" 
        bindinput="onSearchInput"
        confirm-type="search"
        bindconfirm="onSearch"
      />
      <icon type="search" size="16" color="#999" bindtap="onSearch"></icon>
    </view>
    <view class="filter-btn" bindtap="onFilterTap">
      <text>筛选</text>
      <icon type="{{showFilter ? 'clear' : 'search'}}" size="14"></icon>
    </view>
  </view>

  <!-- 筛选面板 -->
  <view class="filter-panel" wx:if="{{showFilter}}">
    <view class="filter-section">
      <text class="filter-title">服务分类</text>
      <view class="filter-tags">
        <text 
          class="filter-tag {{selectedCategory === item.id ? 'active' : ''}}" 
          wx:for="{{categories}}" 
          wx:key="id"
          bindtap="onCategorySelect"
          data-id="{{item.id}}"
        >
          {{item.name}}
        </text>
      </view>
    </view>
    
    <view class="filter-section">
      <text class="filter-title">价格范围</text>
      <view class="price-range">
        <input type="number" placeholder="最低价" value="{{minPrice}}" bindinput="onMinPriceInput" />
        <text>-</text>
        <input type="number" placeholder="最高价" value="{{maxPrice}}" bindinput="onMaxPriceInput" />
      </view>
    </view>

    <view class="filter-section">
      <text class="filter-title">排序方式</text>
      <view class="filter-tags">
        <text 
          class="filter-tag {{sortBy === item.value ? 'active' : ''}}" 
          wx:for="{{sortOptions}}" 
          wx:key="value"
          bindtap="onSortSelect"
          data-value="{{item.value}}"
        >
          {{item.label}}
        </text>
      </view>
    </view>

    <view class="filter-actions">
      <button class="reset-btn" bindtap="onResetFilter">重置</button>
      <button class="apply-btn" bindtap="onApplyFilter">应用</button>
    </view>
  </view>

  <!-- 服务列表 -->
  <view class="service-list">
    <view class="service-item" wx:for="{{services}}" wx:key="id" bindtap="onServiceTap" data-id="{{item.id}}">
      <image src="{{item.image}}" class="service-image" mode="aspectFill"></image>
      <view class="service-info">
        <text class="service-title">{{item.title}}</text>
        <text class="service-desc">{{item.description}}</text>
        <view class="service-tags">
          <text class="service-tag" wx:for="{{item.tags}}" wx:key="*this">{{item}}</text>
        </view>
        <view class="service-bottom">
          <view class="service-price">
            <text class="price-symbol">¥</text>
            <text class="price-amount">{{item.price}}</text>
            <text class="price-unit">起</text>
          </view>
          <view class="service-rating">
            <text class="rating-score">{{item.rating}}</text>
            <text class="rating-count">({{item.reviewCount}})</text>
          </view>
        </view>
        <view class="merchant-info">
          <image src="{{item.merchant.avatar}}" class="merchant-avatar"></image>
          <text class="merchant-name">{{item.merchant.name}}</text>
          <text class="merchant-distance">{{item.merchant.distance}}km</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more">
  <text wx:if="{{loading}}" class="loading-text">加载中...</text>
  <text wx:else class="more-text">加载更多</text>
</view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!services.length && !loading}}">
    <image src="/images/empty-service.png" class="empty-image"></image>
    <text class="empty-text">暂无相关服务</text>
  </view>
</view>
