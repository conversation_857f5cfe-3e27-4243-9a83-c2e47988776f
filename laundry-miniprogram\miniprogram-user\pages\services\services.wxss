/* 用户端服务列表页面样式 */
.container {
  background: #f5f5f5;
  min-height: 100vh;
}

/* 搜索和筛选 */
.search-filter {
  background: #ffffff;
  padding: 20rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-box {
  flex: 1;
  position: relative;
  background: #f8f9fa;
  border-radius: 40rpx;
  padding: 0 40rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
}

.search-box input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  height: 100%;
}

.search-box input::placeholder {
  color: #999;
}

.search-box icon {
  margin-left: 20rpx;
}

.filter-btn {
  background: #3cc51f;
  color: #ffffff;
  padding: 20rpx 30rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.filter-btn:active {
  background: #2aa515;
}

/* 筛选面板 */
.filter-panel {
  background: #ffffff;
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filter-section {
  margin-bottom: 40rpx;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.filter-tag {
  padding: 16rpx 32rpx;
  background: #f8f9fa;
  color: #666;
  border-radius: 40rpx;
  font-size: 26rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.filter-tag.active {
  background: #3cc51f;
  color: #ffffff;
  border-color: #3cc51f;
}

.filter-tag:active {
  transform: scale(0.98);
}

/* 价格范围 */
.price-range {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.price-range input {
  flex: 1;
  height: 70rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
}

.price-range input:focus {
  border-color: #3cc51f;
}

.price-range text {
  font-size: 28rpx;
  color: #666;
}

/* 筛选操作 */
.filter-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.reset-btn,
.apply-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.reset-btn {
  background: #f8f9fa;
  color: #666;
}

.apply-btn {
  background: #3cc51f;
  color: #ffffff;
}

/* 服务列表 */
.service-list {
  padding: 20rpx;
}

.service-item {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.service-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.service-image {
  width: 100%;
  height: 300rpx;
}

.service-info {
  padding: 30rpx;
}

.service-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.service-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 服务标签 */
.service-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.service-tag {
  padding: 8rpx 16rpx;
  background: #f0f8ff;
  color: #1890ff;
  border-radius: 20rpx;
  font-size: 22rpx;
}

/* 服务底部信息 */
.service-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.service-price {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 24rpx;
  color: #ff4d4f;
  margin-right: 4rpx;
}

.price-amount {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff4d4f;
}

.price-unit {
  font-size: 24rpx;
  color: #999;
  margin-left: 4rpx;
}

.service-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.rating-score {
  font-size: 28rpx;
  color: #faad14;
  font-weight: bold;
}

.rating-count {
  font-size: 24rpx;
  color: #999;
}

/* 商家信息 */
.merchant-info {
  display: flex;
  align-items: center;
  padding: 20rpx 0 0;
  border-top: 2rpx solid #f5f5f5;
}

.merchant-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin-right: 20rpx;
}

.merchant-name {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.merchant-distance {
  font-size: 24rpx;
  color: #999;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx 0;
  font-size: 28rpx;
  color: #999;
}

/* 简化选择器 */
.load-more text {
  color: #3cc51f;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999;
}

/* 动画效果 */
.service-item {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 375px) {
  .service-list {
    padding: 15rpx;
  }
  
  .service-item {
    margin-bottom: 15rpx;
  }
  
  .service-info {
    padding: 25rpx;
  }
  
  .service-title {
    font-size: 30rpx;
  }
  
  .service-desc {
    font-size: 24rpx;
  }
  
  .price-amount {
    font-size: 32rpx;
  }
  
  .filter-panel {
    padding: 25rpx;
  }
  
  .filter-tag {
    padding: 12rpx 24rpx;
    font-size: 24rpx;
  }
}
