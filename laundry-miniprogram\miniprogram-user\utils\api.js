// 用户端小程序API接口封装
const { request, API_CONFIG, getApiUrl } = require('./request.js');

// 获取API端点
const ENDPOINTS = API_CONFIG.ENDPOINTS;

// 用户认证相关API
const authAPI = {
  // 微信登录
  wxLogin(code, userInfo) {
    return request.post(ENDPOINTS.AUTH.WX_LOGIN, {
      code,
      userInfo
    });
  },

  // 手机号密码登录
  login(loginData) {
    return request.post(ENDPOINTS.AUTH.LOGIN, loginData);
  },

  // 用户注册
  register(registerData) {
    return request.post(ENDPOINTS.AUTH.REGISTER, registerData);
  },

  // 获取用户信息
  getUserInfo() {
    return request.get(ENDPOINTS.AUTH.GET_INFO);
  },

  // 更新用户信息
  updateUserInfo(userInfo) {
    return request.put(ENDPOINTS.AUTH.UPDATE_INFO, userInfo);
  },

  // 绑定手机号
  bindPhone(phoneCode) {
    return request.post(ENDPOINTS.AUTH.BIND_PHONE, {
      phoneCode
    });
  },

  // 退出登录
  logout() {
    return request.post(ENDPOINTS.AUTH.LOGOUT);
  }
};

// 服务相关API
const serviceAPI = {
  // 获取服务列表
  getServices(params = {}) {
    return request.get(ENDPOINTS.SERVICES.LIST, params);
  },

  // 获取服务详情
  getServiceDetail(serviceId) {
    const url = getApiUrl(ENDPOINTS.SERVICES.DETAIL, { id: serviceId });
    return request.get(url);
  },

  // 获取服务分类
  getServiceCategories() {
    return request.get(ENDPOINTS.SERVICES.CATEGORIES);
  },

  // 搜索服务
  searchServices(keyword, filters = {}) {
    return request.get(ENDPOINTS.SERVICES.SEARCH, {
      keyword,
      ...filters
    });
  },

  // 获取推荐服务
  getRecommendServices() {
    return request.get(ENDPOINTS.SERVICES.RECOMMEND);
  },

  // 获取收藏的服务
  getFavoriteServices() {
    return request.get(ENDPOINTS.SERVICES.FAVORITES);
  },

  // 收藏/取消收藏服务
  toggleFavorite(serviceId) {
    const url = getApiUrl(ENDPOINTS.SERVICES.FAVORITE, { id: serviceId });
    return request.post(url);
  }
};

// 订单相关API
const orderAPI = {
  // 获取订单列表
  getOrders(params = {}) {
    return request.get(ENDPOINTS.ORDERS.LIST, params);
  },

  // 获取订单详情
  getOrderDetail(orderId) {
    const url = getApiUrl(ENDPOINTS.ORDERS.DETAIL, { id: orderId });
    return request.get(url);
  },

  // 创建订单
  createOrder(orderData) {
    return request.post(ENDPOINTS.ORDERS.CREATE, orderData);
  },

  // 取消订单
  cancelOrder(orderId, reason) {
    const url = getApiUrl(ENDPOINTS.ORDERS.CANCEL, { id: orderId });
    return request.post(url, { reason });
  },

  // 支付订单
  payOrder(orderId, paymentData) {
    const url = getApiUrl(ENDPOINTS.ORDERS.PAY, { id: orderId });
    return request.post(url, paymentData);
  },

  // 确认完成订单
  confirmOrder(orderId) {
    const url = getApiUrl(ENDPOINTS.ORDERS.CONFIRM, { id: orderId });
    return request.post(url);
  },

  // 评价订单
  evaluateOrder(orderId, evaluation) {
    const url = getApiUrl(ENDPOINTS.ORDERS.EVALUATE, { id: orderId });
    return request.post(url, evaluation);
  }
};

// 地址相关API
const addressAPI = {
  // 获取地址列表
  getAddresses() {
    return request.get(ENDPOINTS.ADDRESS.LIST);
  },

  // 获取地址详情
  getAddressDetail(addressId) {
    const url = getApiUrl(ENDPOINTS.ADDRESS.DETAIL, { id: addressId });
    return request.get(url);
  },

  // 创建地址
  createAddress(addressData) {
    return request.post(ENDPOINTS.ADDRESS.CREATE, addressData);
  },

  // 更新地址
  updateAddress(addressId, addressData) {
    const url = getApiUrl(ENDPOINTS.ADDRESS.UPDATE, { id: addressId });
    return request.put(url, addressData);
  },

  // 删除地址
  deleteAddress(addressId) {
    const url = getApiUrl(ENDPOINTS.ADDRESS.DELETE, { id: addressId });
    return request.delete(url);
  },

  // 设置默认地址
  setDefaultAddress(addressId) {
    const url = getApiUrl(ENDPOINTS.ADDRESS.SET_DEFAULT, { id: addressId });
    return request.post(url);
  }
};

// 商家相关API
const merchantAPI = {
  // 获取商家列表
  getMerchants(params = {}) {
    return request.get(ENDPOINTS.MERCHANTS.LIST, params);
  },

  // 获取商家详情
  getMerchantDetail(merchantId) {
    const url = getApiUrl(ENDPOINTS.MERCHANTS.DETAIL, { id: merchantId });
    return request.get(url);
  },

  // 获取商家服务
  getMerchantServices(merchantId, params = {}) {
    const url = getApiUrl(ENDPOINTS.MERCHANTS.SERVICES, { id: merchantId });
    return request.get(url, params);
  },

  // 获取附近商家
  getNearbyMerchants(location) {
    return request.get(ENDPOINTS.MERCHANTS.NEARBY, {
      latitude: location.latitude,
      longitude: location.longitude,
      radius: location.radius || 5000
    });
  }
};

// 聊天相关API
const chatAPI = {
  // 获取商家信息
  getMerchantInfo(merchantId) {
    return request.get(`/merchants/${merchantId}`);
  },

  // 获取聊天历史
  getChatHistory(params) {
    return request.get('/chat/history', params);
  },

  // 发送消息
  sendMessage(messageData) {
    return request.post('/chat/send', messageData);
  },

  // 获取未读消息数量
  getUnreadCount() {
    return request.get('/chat/unread-count');
  }
};

// 投诉相关API
const complaintAPI = {
  // 获取投诉列表
  getComplaints(params) {
    return request.get('/complaints', params);
  },

  // 获取投诉详情
  getComplaintDetail(id) {
    return request.get(`/complaints/${id}`);
  },

  // 创建投诉
  createComplaint(complaintData) {
    return request.post('/complaints', complaintData);
  },

  // 更新投诉
  updateComplaint(id, complaintData) {
    return request.put(`/complaints/${id}`, complaintData);
  }
};

// 公共API
const commonAPI = {
  // 上传文件
  uploadFile(filePath, formData = {}) {
    return request.upload('/upload', filePath, formData);
  },

  // 获取轮播图
  getBanners() {
    return request.get(API_CONFIG.ENDPOINTS.COMMON.BANNERS);
  },

  // 获取地区数据
  getRegions(parentId = 0) {
    return request.get(API_CONFIG.ENDPOINTS.COMMON.REGIONS, { parentId });
  },

  // 发送短信验证码
  sendSMS(phone, type = 'LOGIN') {
    return request.post(API_CONFIG.ENDPOINTS.COMMON.SMS, {
      phone,
      type
    });
  },

  // 获取验证码
  getCaptcha() {
    return request.get(API_CONFIG.ENDPOINTS.COMMON.CAPTCHA);
  }
};

// 导出所有API
module.exports = {
  authAPI,
  serviceAPI,
  orderAPI,
  addressAPI,
  merchantAPI,
  chatAPI,
  complaintAPI,
  commonAPI,

  // 兼容旧版本
  request: request.request.bind(request),
  get: request.get.bind(request),
  post: request.post.bind(request),
  put: request.put.bind(request),
  delete: request.delete.bind(request)
};
