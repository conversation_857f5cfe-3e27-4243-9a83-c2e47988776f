// 用户端小程序API接口封装
const { request, API_CONFIG, getApiUrl } = require('./request.js');

// 获取API端点
const ENDPOINTS = API_CONFIG.ENDPOINTS;

// 用户认证相关API
const authAPI = {
  // 微信登录
  wxLogin(code, userInfo) {
    return request.post(ENDPOINTS.AUTH.WX_LOGIN, {
      code,
      userInfo
    });
  },

  // 手机号密码登录
  login(loginData) {
    return request.post(ENDPOINTS.AUTH.LOGIN, loginData);
  },

  // 用户注册
  register(registerData) {
    return request.post(ENDPOINTS.AUTH.REGISTER, registerData);
  },

  // 获取用户信息
  getUserInfo() {
    return request.get(ENDPOINTS.AUTH.GET_INFO);
  },

  // 更新用户信息
  updateUserInfo(userInfo) {
    return request.put(ENDPOINTS.AUTH.UPDATE_INFO, userInfo);
  },

  // 绑定手机号
  bindPhone(phoneCode) {
    return request.post(ENDPOINTS.AUTH.BIND_PHONE, {
      phoneCode
    });
  },

  // 退出登录
  logout() {
    return request.post(ENDPOINTS.AUTH.LOGOUT);
  }
};

// 服务相关API
const serviceAPI = {
  // 获取服务列表
  getServices(params = {}) {
    return request.get(ENDPOINTS.SERVICES.LIST, params);
  },

  // 获取服务详情
  getServiceDetail(serviceId) {
    const url = getApiUrl(ENDPOINTS.SERVICES.DETAIL, { id: serviceId });
    return request.get(url);
  },

  // 获取服务分类
  getServiceCategories() {
    return request.get(ENDPOINTS.SERVICES.CATEGORIES);
  },

  // 搜索服务
  searchServices(keyword, filters = {}) {
    return request.get(ENDPOINTS.SERVICES.SEARCH, {
      keyword,
      ...filters
    });
  },

  // 获取推荐服务
  getRecommendServices() {
    return request.get(ENDPOINTS.SERVICES.RECOMMEND);
  },

  // 获取收藏的服务
  getFavoriteServices() {
    return request.get(ENDPOINTS.SERVICES.FAVORITES);
  },

  // 收藏/取消收藏服务
  toggleFavorite(serviceId) {
    const url = getApiUrl(ENDPOINTS.SERVICES.FAVORITE, { id: serviceId });
    return request.post(url);
  }
};

// 订单相关API
const orderAPI = {
  // 获取订单列表
  getOrders(params = {}) {
    return userRequest.get(ENDPOINTS.ORDERS.LIST, params);
  },
  
  // 获取订单详情
  getOrderDetail(orderId) {
    const url = getApiUrl(ENDPOINTS.ORDERS.DETAIL, { id: orderId });
    return userRequest.get(url);
  },
  
  // 创建订单
  createOrder(orderData) {
    return userRequest.post(ENDPOINTS.ORDERS.CREATE, orderData);
  },
  
  // 取消订单
  cancelOrder(orderId, reason) {
    const url = getApiUrl(ENDPOINTS.ORDERS.CANCEL, { id: orderId });
    return userRequest.post(url, { reason });
  },
  
  // 支付订单
  payOrder(orderId, paymentData) {
    const url = getApiUrl(ENDPOINTS.ORDERS.PAY, { id: orderId });
    return userRequest.post(url, paymentData);
  },
  
  // 确认完成订单
  confirmOrder(orderId) {
    const url = getApiUrl(ENDPOINTS.ORDERS.CONFIRM, { id: orderId });
    return userRequest.post(url);
  },
  
  // 评价订单
  evaluateOrder(orderId, evaluation) {
    const url = getApiUrl(ENDPOINTS.ORDERS.EVALUATE, { id: orderId });
    return userRequest.post(url, evaluation);
  }
};

// 地址相关API
const addressAPI = {
  // 获取地址列表
  getAddresses() {
    return userRequest.get(ENDPOINTS.ADDRESS.LIST);
  },
  
  // 获取地址详情
  getAddressDetail(addressId) {
    const url = getApiUrl(ENDPOINTS.ADDRESS.DETAIL, { id: addressId });
    return userRequest.get(url);
  },
  
  // 创建地址
  createAddress(addressData) {
    return userRequest.post(ENDPOINTS.ADDRESS.CREATE, addressData);
  },
  
  // 更新地址
  updateAddress(addressId, addressData) {
    const url = getApiUrl(ENDPOINTS.ADDRESS.UPDATE, { id: addressId });
    return userRequest.put(url, addressData);
  },
  
  // 删除地址
  deleteAddress(addressId) {
    const url = getApiUrl(ENDPOINTS.ADDRESS.DELETE, { id: addressId });
    return userRequest.delete(url);
  },
  
  // 设置默认地址
  setDefaultAddress(addressId) {
    const url = getApiUrl(ENDPOINTS.ADDRESS.SET_DEFAULT, { id: addressId });
    return userRequest.post(url);
  }
};

// 商家相关API
const merchantAPI = {
  // 获取商家列表
  getMerchants(params = {}) {
    return userRequest.get(ENDPOINTS.MERCHANTS.LIST, params);
  },
  
  // 获取商家详情
  getMerchantDetail(merchantId) {
    const url = getApiUrl(ENDPOINTS.MERCHANTS.DETAIL, { id: merchantId });
    return userRequest.get(url);
  },
  
  // 获取商家服务
  getMerchantServices(merchantId, params = {}) {
    const url = getApiUrl(ENDPOINTS.MERCHANTS.SERVICES, { id: merchantId });
    return userRequest.get(url, params);
  },
  
  // 获取附近商家
  getNearbyMerchants(location) {
    return userRequest.get(ENDPOINTS.MERCHANTS.NEARBY, {
      latitude: location.latitude,
      longitude: location.longitude,
      radius: location.radius || 5000
    });
  }
};

// 公共API
const commonAPI = {
  // 上传文件
  uploadFile(filePath, formData = {}) {
    return userRequest.upload(API_CONFIG.ENDPOINTS.COMMON.UPLOAD, filePath, formData);
  },
  
  // 获取轮播图
  getBanners() {
    return userRequest.get(API_CONFIG.ENDPOINTS.COMMON.BANNERS);
  },
  
  // 获取地区数据
  getRegions(parentId = 0) {
    return userRequest.get(API_CONFIG.ENDPOINTS.COMMON.REGIONS, { parentId });
  },
  
  // 发送短信验证码
  sendSMS(phone, type = 'LOGIN') {
    return userRequest.post(API_CONFIG.ENDPOINTS.COMMON.SMS, {
      phone,
      type
    });
  },
  
  // 获取验证码
  getCaptcha() {
    return userRequest.get(API_CONFIG.ENDPOINTS.COMMON.CAPTCHA);
  }
};

// 导出所有API
module.exports = {
  authAPI,
  serviceAPI,
  orderAPI,
  addressAPI,
  merchantAPI,
  commonAPI,
  
  // 兼容旧版本
  request: userRequest.request.bind(userRequest),
  get: userRequest.get.bind(userRequest),
  post: userRequest.post.bind(userRequest),
  put: userRequest.put.bind(userRequest),
  delete: userRequest.delete.bind(userRequest)
};
