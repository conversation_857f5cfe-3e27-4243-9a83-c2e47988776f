// Web端用户前台API配置
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'

// API基础配置
const API_BASE_URL = 'http://localhost:8081/api'

// 创建axios实例
const request = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 添加token
    const userStore = useUserStore()
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`
    }
    
    console.log('API请求:', config.url, config)
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    console.log('API响应:', response)
    
    const { data } = response
    
    // 后端统一返回格式: { success: boolean, data: any, message: string, code: number }
    if (data && typeof data === 'object') {
      if (data.success === false) {
        // 业务错误
        ElMessage.error(data.message || '操作失败')
        return Promise.reject(new Error(data.message || '操作失败'))
      }
      
      // 成功响应，返回data字段
      return data.data || data
    }
    
    return data
  },
  (error) => {
    console.error('API响应错误:', error)
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          ElMessage.error('登录已过期，请重新登录')
          const userStore = useUserStore()
          userStore.logout()
          break
        case 403:
          ElMessage.error('没有权限访问')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器错误，请稍后重试')
          break
        default:
          ElMessage.error(data?.message || '网络错误，请重试')
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，请重试')
    } else {
      ElMessage.error('网络连接失败，请检查网络设置')
    }
    
    return Promise.reject(error)
  }
)

// 用户认证API
export const authAPI = {
  // 用户登录
  login(loginData) {
    return request.post('/user/login', loginData)
  },
  
  // 用户注册
  register(registerData) {
    return request.post('/user/register', registerData)
  },
  
  // 获取用户信息
  getUserInfo() {
    return request.get('/user/info')
  },
  
  // 更新用户信息
  updateUserInfo(userInfo) {
    return request.put('/user/update', userInfo)
  },
  
  // 退出登录
  logout() {
    return request.post('/user/logout')
  },
  
  // 发送验证码
  sendSMS(phone, type = 'LOGIN') {
    return request.post('/user/send-sms', { phone, type })
  },
  
  // 验证码登录
  smsLogin(phone, code) {
    return request.post('/user/sms-login', { phone, code })
  }
}

// 服务相关API
export const serviceAPI = {
  // 获取服务列表
  getServices(params = {}) {
    return request.get('/services', { params })
  },
  
  // 获取服务详情
  getServiceDetail(serviceId) {
    return request.get(`/services/${serviceId}`)
  },
  
  // 获取服务分类
  getServiceCategories() {
    return request.get('/services/categories')
  },
  
  // 搜索服务
  searchServices(keyword, filters = {}) {
    return request.get('/services/search', {
      params: { keyword, ...filters }
    })
  },
  
  // 获取推荐服务
  getRecommendServices() {
    return request.get('/services/recommend')
  },
  
  // 获取收藏的服务
  getFavoriteServices() {
    return request.get('/services/favorites')
  },
  
  // 收藏/取消收藏服务
  toggleFavorite(serviceId) {
    return request.post(`/services/${serviceId}/favorite`)
  }
}

// 订单相关API
export const orderAPI = {
  // 获取订单列表
  getOrders(params = {}) {
    return request.get('/orders', { params })
  },
  
  // 获取订单详情
  getOrderDetail(orderId) {
    return request.get(`/orders/${orderId}`)
  },
  
  // 创建订单
  createOrder(orderData) {
    return request.post('/orders', orderData)
  },
  
  // 取消订单
  cancelOrder(orderId, reason) {
    return request.post(`/orders/${orderId}/cancel`, { reason })
  },
  
  // 支付订单
  payOrder(orderId, paymentData) {
    return request.post(`/orders/${orderId}/pay`, paymentData)
  },
  
  // 确认完成订单
  confirmOrder(orderId) {
    return request.post(`/orders/${orderId}/confirm`)
  },
  
  // 评价订单
  evaluateOrder(orderId, evaluation) {
    return request.post(`/orders/${orderId}/evaluate`, evaluation)
  }
}

// 地址相关API
export const addressAPI = {
  // 获取地址列表
  getAddresses() {
    return request.get('/addresses')
  },
  
  // 获取地址详情
  getAddressDetail(addressId) {
    return request.get(`/addresses/${addressId}`)
  },
  
  // 创建地址
  createAddress(addressData) {
    return request.post('/addresses', addressData)
  },
  
  // 更新地址
  updateAddress(addressId, addressData) {
    return request.put(`/addresses/${addressId}`, addressData)
  },
  
  // 删除地址
  deleteAddress(addressId) {
    return request.delete(`/addresses/${addressId}`)
  },
  
  // 设置默认地址
  setDefaultAddress(addressId) {
    return request.post(`/addresses/${addressId}/default`)
  }
}

// 商家相关API
export const merchantAPI = {
  // 获取商家列表
  getMerchants(params = {}) {
    return request.get('/merchants', { params })
  },
  
  // 获取商家详情
  getMerchantDetail(merchantId) {
    return request.get(`/merchants/${merchantId}`)
  },
  
  // 获取商家服务
  getMerchantServices(merchantId, params = {}) {
    return request.get(`/merchants/${merchantId}/services`, { params })
  },
  
  // 获取附近商家
  getNearbyMerchants(location) {
    return request.get('/merchants/nearby', {
      params: {
        latitude: location.latitude,
        longitude: location.longitude,
        radius: location.radius || 5000
      }
    })
  },
  
  // 获取推荐商家
  getRecommendMerchants() {
    return request.get('/merchants/recommend')
  }
}

// 公共API
export const commonAPI = {
  // 上传文件
  uploadFile(file, onProgress) {
    const formData = new FormData()
    formData.append('file', file)
    
    return request.post('/common/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: onProgress
    })
  },
  
  // 获取轮播图
  getBanners() {
    return request.get('/common/banners')
  },
  
  // 获取地区数据
  getRegions(parentId = 0) {
    return request.get('/common/regions', {
      params: { parentId }
    })
  },
  
  // 发送短信验证码
  sendSMS(phone, type = 'LOGIN') {
    return request.post('/common/sms/send', {
      phone,
      type
    })
  },
  
  // 获取验证码
  getCaptcha() {
    return request.get('/common/captcha')
  }
}

// 导出默认请求实例
export default request
