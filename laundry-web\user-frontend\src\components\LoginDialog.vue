<template>
  <el-dialog
    v-model="dialogVisible"
    title="用户登录"
    width="400px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="login-form">
      <!-- 登录方式切换 -->
      <el-tabs v-model="loginType" class="login-tabs">
        <el-tab-pane label="密码登录" name="password">
          <el-form
            ref="passwordFormRef"
            :model="passwordForm"
            :rules="passwordRules"
            label-width="0"
            @submit.prevent="handlePasswordLogin"
          >
            <el-form-item prop="phone">
              <el-input
                v-model="passwordForm.phone"
                placeholder="请输入手机号"
                prefix-icon="Phone"
                size="large"
                maxlength="11"
              />
            </el-form-item>
            
            <el-form-item prop="password">
              <el-input
                v-model="passwordForm.password"
                type="password"
                placeholder="请输入密码"
                prefix-icon="Lock"
                size="large"
                show-password
              />
            </el-form-item>
            
            <el-form-item>
              <el-button
                type="primary"
                size="large"
                style="width: 100%"
                :loading="passwordLoading"
                @click="handlePasswordLogin"
              >
                登录
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <el-tab-pane label="验证码登录" name="sms">
          <el-form
            ref="smsFormRef"
            :model="smsForm"
            :rules="smsRules"
            label-width="0"
            @submit.prevent="handleSmsLogin"
          >
            <el-form-item prop="phone">
              <el-input
                v-model="smsForm.phone"
                placeholder="请输入手机号"
                prefix-icon="Phone"
                size="large"
                maxlength="11"
              />
            </el-form-item>
            
            <el-form-item prop="code">
              <div class="code-input-group">
                <el-input
                  v-model="smsForm.code"
                  placeholder="请输入验证码"
                  prefix-icon="Message"
                  size="large"
                  maxlength="6"
                />
                <el-button
                  :disabled="!canSendSms || smsCountdown > 0"
                  :loading="smsLoading"
                  @click="handleSendSms"
                >
                  {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
                </el-button>
              </div>
            </el-form-item>
            
            <el-form-item>
              <el-button
                type="primary"
                size="large"
                style="width: 100%"
                :loading="smsLoginLoading"
                @click="handleSmsLogin"
              >
                登录
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      
      <!-- 其他操作 -->
      <div class="login-footer">
        <div class="forgot-password">
          <el-link type="primary" @click="handleForgotPassword">忘记密码？</el-link>
        </div>
        <div class="register-link">
          还没有账号？
          <el-link type="primary" @click="handleGoRegister">立即注册</el-link>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

export default {
  name: 'LoginDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'success', 'register'],
  setup(props, { emit }) {
    const userStore = useUserStore()
    
    // 对话框显示状态
    const dialogVisible = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value)
    })
    
    // 登录方式
    const loginType = ref('password')
    
    // 密码登录表单
    const passwordFormRef = ref()
    const passwordForm = reactive({
      phone: '',
      password: ''
    })
    const passwordLoading = ref(false)
    
    // 短信登录表单
    const smsFormRef = ref()
    const smsForm = reactive({
      phone: '',
      code: ''
    })
    const smsLoginLoading = ref(false)
    const smsLoading = ref(false)
    const smsCountdown = ref(0)
    
    // 表单验证规则
    const passwordRules = {
      phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
      ]
    }
    
    const smsRules = {
      phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
      ],
      code: [
        { required: true, message: '请输入验证码', trigger: 'blur' },
        { pattern: /^\d{6}$/, message: '请输入6位数字验证码', trigger: 'blur' }
      ]
    }
    
    // 计算属性
    const canSendSms = computed(() => {
      return /^1[3-9]\d{9}$/.test(smsForm.phone)
    })
    
    // 密码登录
    const handlePasswordLogin = async () => {
      if (!passwordFormRef.value) return
      
      const valid = await passwordFormRef.value.validate().catch(() => false)
      if (!valid) return
      
      passwordLoading.value = true
      
      try {
        await userStore.login({
          phone: passwordForm.phone,
          password: passwordForm.password,
          loginType: 'PASSWORD'
        })
        
        ElMessage.success('登录成功')
        emit('success')
        handleClose()
        
      } catch (error) {
        ElMessage.error(error.message || '登录失败，请重试')
      } finally {
        passwordLoading.value = false
      }
    }
    
    // 发送短信验证码
    const handleSendSms = async () => {
      if (!canSendSms.value) {
        ElMessage.error('请输入正确的手机号')
        return
      }
      
      smsLoading.value = true
      
      try {
        await userStore.sendSMS(smsForm.phone, 'LOGIN')
        ElMessage.success('验证码已发送')
        
        // 开始倒计时
        smsCountdown.value = 60
        const timer = setInterval(() => {
          smsCountdown.value--
          if (smsCountdown.value <= 0) {
            clearInterval(timer)
          }
        }, 1000)
        
      } catch (error) {
        ElMessage.error(error.message || '发送验证码失败')
      } finally {
        smsLoading.value = false
      }
    }
    
    // 短信验证码登录
    const handleSmsLogin = async () => {
      if (!smsFormRef.value) return
      
      const valid = await smsFormRef.value.validate().catch(() => false)
      if (!valid) return
      
      smsLoginLoading.value = true
      
      try {
        await userStore.smsLogin(smsForm.phone, smsForm.code)
        
        ElMessage.success('登录成功')
        emit('success')
        handleClose()
        
      } catch (error) {
        ElMessage.error(error.message || '登录失败，请重试')
      } finally {
        smsLoginLoading.value = false
      }
    }
    
    // 忘记密码
    const handleForgotPassword = () => {
      ElMessage.info('请联系客服找回密码')
    }
    
    // 去注册
    const handleGoRegister = () => {
      emit('register')
      handleClose()
    }
    
    // 关闭对话框
    const handleClose = () => {
      // 重置表单
      passwordForm.phone = ''
      passwordForm.password = ''
      smsForm.phone = ''
      smsForm.code = ''
      
      // 重置验证
      passwordFormRef.value?.resetFields()
      smsFormRef.value?.resetFields()
      
      // 重置状态
      passwordLoading.value = false
      smsLoginLoading.value = false
      smsLoading.value = false
      smsCountdown.value = 0
      
      dialogVisible.value = false
    }
    
    return {
      dialogVisible,
      loginType,
      passwordFormRef,
      passwordForm,
      passwordLoading,
      passwordRules,
      smsFormRef,
      smsForm,
      smsLoginLoading,
      smsLoading,
      smsCountdown,
      smsRules,
      canSendSms,
      handlePasswordLogin,
      handleSendSms,
      handleSmsLogin,
      handleForgotPassword,
      handleGoRegister,
      handleClose
    }
  }
}
</script>

<style scoped>
.login-form {
  padding: 20px 0;
}

.login-tabs {
  margin-bottom: 20px;
}

.code-input-group {
  display: flex;
  gap: 12px;
}

.code-input-group .el-input {
  flex: 1;
}

.login-footer {
  text-align: center;
  margin-top: 20px;
}

.forgot-password {
  margin-bottom: 12px;
}

.register-link {
  color: #666;
  font-size: 14px;
}
</style>
