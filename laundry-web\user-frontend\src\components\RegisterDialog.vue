<template>
  <el-dialog
    v-model="dialogVisible"
    title="用户注册"
    width="400px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="register-form">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="0"
        @submit.prevent="handleRegister"
      >
        <el-form-item prop="phone">
          <el-input
            v-model="form.phone"
            placeholder="请输入手机号"
            prefix-icon="Phone"
            size="large"
            maxlength="11"
          />
        </el-form-item>
        
        <el-form-item prop="code">
          <div class="code-input-group">
            <el-input
              v-model="form.code"
              placeholder="请输入验证码"
              prefix-icon="Message"
              size="large"
              maxlength="6"
            />
            <el-button
              :disabled="!canSendSms || smsCountdown > 0"
              :loading="smsLoading"
              @click="handleSendSms"
            >
              {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
            </el-button>
          </div>
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="form.password"
            type="password"
            placeholder="请设置密码（6-20位）"
            prefix-icon="Lock"
            size="large"
            show-password
          />
        </el-form-item>
        
        <el-form-item prop="confirmPassword">
          <el-input
            v-model="form.confirmPassword"
            type="password"
            placeholder="请确认密码"
            prefix-icon="Lock"
            size="large"
            show-password
          />
        </el-form-item>
        
        <el-form-item prop="nickname">
          <el-input
            v-model="form.nickname"
            placeholder="请输入昵称"
            prefix-icon="User"
            size="large"
            maxlength="20"
          />
        </el-form-item>
        
        <el-form-item prop="agreement">
          <el-checkbox v-model="form.agreement">
            我已阅读并同意
            <el-link type="primary" @click="showUserAgreement">《用户协议》</el-link>
            和
            <el-link type="primary" @click="showPrivacyPolicy">《隐私政策》</el-link>
          </el-checkbox>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            style="width: 100%"
            :loading="loading"
            @click="handleRegister"
          >
            注册
          </el-button>
        </el-form-item>
      </el-form>
      
      <!-- 其他操作 -->
      <div class="register-footer">
        <div class="login-link">
          已有账号？
          <el-link type="primary" @click="handleGoLogin">立即登录</el-link>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

export default {
  name: 'RegisterDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'success', 'login'],
  setup(props, { emit }) {
    const userStore = useUserStore()
    
    // 对话框显示状态
    const dialogVisible = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value)
    })
    
    // 表单引用
    const formRef = ref()
    
    // 表单数据
    const form = reactive({
      phone: '',
      code: '',
      password: '',
      confirmPassword: '',
      nickname: '',
      agreement: false
    })
    
    // 加载状态
    const loading = ref(false)
    const smsLoading = ref(false)
    const smsCountdown = ref(0)
    
    // 自定义验证器
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== form.password) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }
    
    // 表单验证规则
    const rules = {
      phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
      ],
      code: [
        { required: true, message: '请输入验证码', trigger: 'blur' },
        { pattern: /^\d{6}$/, message: '请输入6位数字验证码', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, message: '请确认密码', trigger: 'blur' },
        { validator: validateConfirmPassword, trigger: 'blur' }
      ],
      nickname: [
        { required: true, message: '请输入昵称', trigger: 'blur' },
        { min: 2, max: 20, message: '昵称长度为2-20位', trigger: 'blur' }
      ],
      agreement: [
        { 
          validator: (rule, value, callback) => {
            if (!value) {
              callback(new Error('请同意用户协议和隐私政策'))
            } else {
              callback()
            }
          }, 
          trigger: 'change' 
        }
      ]
    }
    
    // 计算属性
    const canSendSms = computed(() => {
      return /^1[3-9]\d{9}$/.test(form.phone)
    })
    
    // 发送短信验证码
    const handleSendSms = async () => {
      if (!canSendSms.value) {
        ElMessage.error('请输入正确的手机号')
        return
      }
      
      smsLoading.value = true
      
      try {
        await userStore.sendSMS(form.phone, 'REGISTER')
        ElMessage.success('验证码已发送')
        
        // 开始倒计时
        smsCountdown.value = 60
        const timer = setInterval(() => {
          smsCountdown.value--
          if (smsCountdown.value <= 0) {
            clearInterval(timer)
          }
        }, 1000)
        
      } catch (error) {
        ElMessage.error(error.message || '发送验证码失败')
      } finally {
        smsLoading.value = false
      }
    }
    
    // 注册
    const handleRegister = async () => {
      if (!formRef.value) return
      
      const valid = await formRef.value.validate().catch(() => false)
      if (!valid) return
      
      loading.value = true
      
      try {
        await userStore.register({
          phone: form.phone,
          code: form.code,
          password: form.password,
          nickname: form.nickname
        })
        
        ElMessage.success('注册成功')
        emit('success')
        handleClose()
        
      } catch (error) {
        ElMessage.error(error.message || '注册失败，请重试')
      } finally {
        loading.value = false
      }
    }
    
    // 显示用户协议
    const showUserAgreement = () => {
      ElMessage.info('用户协议内容')
    }
    
    // 显示隐私政策
    const showPrivacyPolicy = () => {
      ElMessage.info('隐私政策内容')
    }
    
    // 去登录
    const handleGoLogin = () => {
      emit('login')
      handleClose()
    }
    
    // 关闭对话框
    const handleClose = () => {
      // 重置表单
      form.phone = ''
      form.code = ''
      form.password = ''
      form.confirmPassword = ''
      form.nickname = ''
      form.agreement = false
      
      // 重置验证
      formRef.value?.resetFields()
      
      // 重置状态
      loading.value = false
      smsLoading.value = false
      smsCountdown.value = 0
      
      dialogVisible.value = false
    }
    
    return {
      dialogVisible,
      formRef,
      form,
      rules,
      loading,
      smsLoading,
      smsCountdown,
      canSendSms,
      handleSendSms,
      handleRegister,
      showUserAgreement,
      showPrivacyPolicy,
      handleGoLogin,
      handleClose
    }
  }
}
</script>

<style scoped>
.register-form {
  padding: 20px 0;
}

.code-input-group {
  display: flex;
  gap: 12px;
}

.code-input-group .el-input {
  flex: 1;
}

.register-footer {
  text-align: center;
  margin-top: 20px;
}

.login-link {
  color: #666;
  font-size: 14px;
}
</style>
