// 用户状态管理
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI } from '@/api'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('user_token') || '')
  const userInfo = ref(JSON.parse(localStorage.getItem('user_info') || 'null'))
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)

  // 登录
  const login = async (loginData) => {
    try {
      const result = await authAPI.login(loginData)
      
      // 保存登录信息
      token.value = result.token
      userInfo.value = result.userInfo
      
      // 保存到本地存储
      localStorage.setItem('user_token', result.token)
      localStorage.setItem('user_info', JSON.stringify(result.userInfo))
      
      return result
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }

  // 注册
  const register = async (registerData) => {
    try {
      const result = await authAPI.register(registerData)
      
      // 注册成功后自动登录
      if (result.token) {
        token.value = result.token
        userInfo.value = result.userInfo
        
        localStorage.setItem('user_token', result.token)
        localStorage.setItem('user_info', JSON.stringify(result.userInfo))
      }
      
      return result
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    }
  }

  // 获取用户信息
  const getUserInfo = async () => {
    try {
      const result = await authAPI.getUserInfo()
      userInfo.value = result
      localStorage.setItem('user_info', JSON.stringify(result))
      return result
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取失败，可能是token过期，清除登录状态
      logout()
      throw error
    }
  }

  // 更新用户信息
  const updateUserInfo = async (newUserInfo) => {
    try {
      const result = await authAPI.updateUserInfo(newUserInfo)
      userInfo.value = { ...userInfo.value, ...result }
      localStorage.setItem('user_info', JSON.stringify(userInfo.value))
      return result
    } catch (error) {
      console.error('更新用户信息失败:', error)
      throw error
    }
  }

  // 退出登录
  const logout = async () => {
    try {
      // 调用后端退出接口
      if (token.value) {
        await authAPI.logout()
      }
    } catch (error) {
      console.error('退出登录接口调用失败:', error)
    } finally {
      // 清除本地数据
      clearUserData()
    }
  }

  // 清除用户数据
  const clearUserData = () => {
    token.value = ''
    userInfo.value = null
    localStorage.removeItem('user_token')
    localStorage.removeItem('user_info')
  }

  // 检查登录状态
  const checkLoginStatus = async () => {
    if (token.value && userInfo.value) {
      try {
        // 验证token有效性
        await getUserInfo()
        return true
      } catch (error) {
        // token无效，清除登录状态
        clearUserData()
        return false
      }
    }
    return false
  }

  // 短信登录
  const smsLogin = async (phone, code) => {
    try {
      const result = await authAPI.smsLogin(phone, code)
      
      // 保存登录信息
      token.value = result.token
      userInfo.value = result.userInfo
      
      localStorage.setItem('user_token', result.token)
      localStorage.setItem('user_info', JSON.stringify(result.userInfo))
      
      return result
    } catch (error) {
      console.error('短信登录失败:', error)
      throw error
    }
  }

  // 发送验证码
  const sendSMS = async (phone, type = 'LOGIN') => {
    try {
      const result = await authAPI.sendSMS(phone, type)
      return result
    } catch (error) {
      console.error('发送验证码失败:', error)
      throw error
    }
  }

  return {
    // 状态
    token,
    userInfo,
    isLoggedIn,
    
    // 方法
    login,
    register,
    getUserInfo,
    updateUserInfo,
    logout,
    clearUserData,
    checkLoginStatus,
    smsLogin,
    sendSMS
  }
})
