<template>
  <div class="home-page">
    <!-- 导航栏 -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <!-- Logo -->
          <div class="logo">
            <img src="/logo.png" alt="洗护平台" />
            <span class="logo-text">洗护平台</span>
          </div>
          
          <!-- 导航菜单 -->
          <nav class="nav-menu">
            <a href="#home" class="nav-item active">首页</a>
            <a href="#services" class="nav-item">服务</a>
            <a href="#merchants" class="nav-item">商家</a>
            <a href="#about" class="nav-item">关于我们</a>
          </nav>
          
          <!-- 用户操作区 -->
          <div class="user-actions">
            <template v-if="!isLoggedIn">
              <el-button type="primary" @click="showLoginDialog">登录</el-button>
              <el-button @click="showRegisterDialog">注册</el-button>
            </template>
            <template v-else>
              <el-dropdown @command="handleUserCommand">
                <span class="user-info">
                  <el-avatar :src="userInfo.avatar" :size="32">
                    {{ userInfo.nickname?.charAt(0) }}
                  </el-avatar>
                  <span class="username">{{ userInfo.nickname }}</span>
                  <el-icon><arrow-down /></el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                    <el-dropdown-item command="orders">我的订单</el-dropdown-item>
                    <el-dropdown-item command="addresses">地址管理</el-dropdown-item>
                    <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="main-content">
      <!-- 轮播图区域 -->
      <section class="hero-section">
        <div class="container">
          <el-carousel height="400px" indicator-position="outside" autoplay>
            <el-carousel-item v-for="banner in banners" :key="banner.id">
              <img :src="banner.image" :alt="banner.title" class="banner-image" />
              <div class="banner-overlay">
                <h2 class="banner-title">{{ banner.title }}</h2>
                <p class="banner-subtitle">专业洗护，品质保证</p>
                <el-button type="primary" size="large" @click="handleServiceExplore">
                  立即体验
                </el-button>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
      </section>

      <!-- 快速服务入口 -->
      <section class="quick-services">
        <div class="container">
          <div class="quick-grid">
            <div class="quick-item" @click="handleQuickService('wash')">
              <div class="quick-icon">
                <el-icon size="32"><washing-machine /></el-icon>
              </div>
              <span>衣物清洗</span>
            </div>
            <div class="quick-item" @click="handleQuickService('dry')">
              <div class="quick-icon">
                <el-icon size="32"><sunny /></el-icon>
              </div>
              <span>干洗服务</span>
            </div>
            <div class="quick-item" @click="handleQuickService('shoes')">
              <div class="quick-icon">
                <el-icon size="32"><shoe /></el-icon>
              </div>
              <span>鞋类护理</span>
            </div>
            <div class="quick-item" @click="handleQuickService('home')">
              <div class="quick-icon">
                <el-icon size="32"><house /></el-icon>
              </div>
              <span>家纺清洗</span>
            </div>
          </div>
        </div>
      </section>

      <!-- 服务分类区域 -->
      <section id="services" class="services-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">精选服务</h2>
            <p class="section-subtitle">专业洗护，让生活更美好</p>
          </div>
          <div class="services-grid">
            <div
              v-for="category in categories"
              :key="category.id"
              class="service-card"
              @click="handleServiceClick(category)"
            >
              <div class="service-image">
                <img :src="category.icon" :alt="category.name" />
                <div class="service-badge" v-if="category.isHot">热门</div>
              </div>
              <div class="service-content">
                <h3 class="service-name">{{ category.name }}</h3>
                <p class="service-desc">{{ category.description }}</p>
                <div class="service-footer">
                  <span class="service-price">起价 ¥{{ category.startPrice || 10 }}</span>
                  <el-button type="primary" size="small">立即预约</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 推荐商家区域 -->
      <section id="merchants" class="merchants-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">优质商家</h2>
            <p class="section-subtitle">精选认证商家，服务有保障</p>
          </div>
          <div class="merchants-grid">
            <div
              v-for="merchant in recommendMerchants"
              :key="merchant.id"
              class="merchant-card"
              @click="handleMerchantClick(merchant)"
            >
              <div class="merchant-header">
                <div class="merchant-logo">
                  <img :src="merchant.logo" :alt="merchant.name" />
                </div>
                <div class="merchant-status">
                  <el-tag type="success" size="small">认证商家</el-tag>
                </div>
              </div>
              <div class="merchant-info">
                <h3 class="merchant-name">{{ merchant.name }}</h3>
                <div class="merchant-rating">
                  <el-rate v-model="merchant.rating" disabled show-score text-color="#ff9900" />
                  <span class="rating-text">({{ merchant.reviewCount || 128 }}条评价)</span>
                </div>
                <p class="merchant-address">
                  <el-icon><location /></el-icon>
                  {{ merchant.address }}
                </p>
                <div class="merchant-services">
                  <span class="service-tag" v-for="service in merchant.services?.slice(0, 3)" :key="service">
                    {{ service }}
                  </span>
                </div>
                <div class="merchant-footer">
                  <div class="merchant-stats">
                    <span class="stat-item">月销 {{ merchant.monthlyOrders || 156 }}+</span>
                    <span class="stat-item">起送 ¥{{ merchant.minOrder || 20 }}</span>
                  </div>
                  <el-button type="primary" size="small">进店看看</el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 查看更多商家 -->
          <div class="more-merchants">
            <el-button type="primary" size="large" @click="handleViewMoreMerchants">
              查看更多商家
            </el-button>
          </div>
        </div>
      </section>

      <!-- 关于我们区域 -->
      <section id="about" class="about-section">
        <div class="container">
          <h2 class="section-title">为什么选择我们</h2>
          <div class="features-grid">
            <div class="feature-item">
              <div class="feature-icon">
                <el-icon size="48"><clock /></el-icon>
              </div>
              <h3>快速便捷</h3>
              <p>24小时在线服务，快速响应您的需求</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <el-icon size="48"><shield-check /></el-icon>
              </div>
              <h3>品质保证</h3>
              <p>专业设备，严格质检，确保服务品质</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <el-icon size="48"><truck /></el-icon>
              </div>
              <h3>上门服务</h3>
              <p>免费上门取送，让您享受便捷体验</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <el-icon size="48"><money /></el-icon>
              </div>
              <h3>价格透明</h3>
              <p>明码标价，无隐藏费用，让您消费放心</p>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h4>联系我们</h4>
            <p>客服热线：400-123-4567</p>
            <p>邮箱：<EMAIL></p>
          </div>
          <div class="footer-section">
            <h4>服务时间</h4>
            <p>周一至周日：8:00-22:00</p>
            <p>节假日正常营业</p>
          </div>
          <div class="footer-section">
            <h4>关注我们</h4>
            <p>微信公众号：洗护平台</p>
            <p>官方微博：@洗护平台</p>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 洗护平台. All rights reserved.</p>
        </div>
      </div>
    </footer>

    <!-- 登录对话框 -->
    <LoginDialog 
      v-model="loginDialogVisible" 
      @success="handleLoginSuccess"
    />

    <!-- 注册对话框 -->
    <RegisterDialog 
      v-model="registerDialogVisible" 
      @success="handleRegisterSuccess"
    />
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { serviceAPI, merchantAPI, commonAPI } from '@/api'
import LoginDialog from '@/components/LoginDialog.vue'
import RegisterDialog from '@/components/RegisterDialog.vue'
import { ElMessage } from 'element-plus'
import { ArrowDown, Clock, ShieldCheck, Truck, Money } from '@element-plus/icons-vue'

export default {
  name: 'Home',
  components: {
    LoginDialog,
    RegisterDialog,
    ArrowDown,
    Clock,
    ShieldCheck,
    Truck,
    Money
  },
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    
    // 响应式数据
    const banners = ref([])
    const categories = ref([])
    const recommendMerchants = ref([])
    const loginDialogVisible = ref(false)
    const registerDialogVisible = ref(false)
    const loading = ref(true)

    // 计算属性
    const isLoggedIn = computed(() => userStore.isLoggedIn)
    const userInfo = computed(() => userStore.userInfo)

    // 页面初始化
    onMounted(async () => {
      await loadPageData()
    })

    // 加载页面数据
    const loadPageData = async () => {
      try {
        loading.value = true
        
        // 并行加载数据
        const [bannersData, categoriesData, merchantsData] = await Promise.all([
          commonAPI.getBanners(),
          serviceAPI.getServiceCategories(),
          merchantAPI.getRecommendMerchants()
        ])
        
        banners.value = bannersData
        categories.value = categoriesData
        recommendMerchants.value = merchantsData
        
      } catch (error) {
        console.error('加载页面数据失败:', error)
        ElMessage.error('页面加载失败，请刷新重试')
      } finally {
        loading.value = false
      }
    }

    // 显示登录对话框
    const showLoginDialog = () => {
      loginDialogVisible.value = true
    }

    // 显示注册对话框
    const showRegisterDialog = () => {
      registerDialogVisible.value = true
    }

    // 处理登录成功
    const handleLoginSuccess = () => {
      loginDialogVisible.value = false
      ElMessage.success('登录成功')
    }

    // 处理注册成功
    const handleRegisterSuccess = () => {
      registerDialogVisible.value = false
      ElMessage.success('注册成功')
    }

    // 处理用户下拉菜单命令
    const handleUserCommand = (command) => {
      switch (command) {
        case 'profile':
          router.push('/profile')
          break
        case 'orders':
          router.push('/orders')
          break
        case 'addresses':
          router.push('/addresses')
          break
        case 'logout':
          userStore.logout()
          ElMessage.success('已退出登录')
          break
      }
    }

    // 处理服务点击
    const handleServiceClick = (category) => {
      if (!isLoggedIn.value) {
        showLoginDialog()
        return
      }
      router.push(`/services?categoryId=${category.id}`)
    }

    // 处理商家点击
    const handleMerchantClick = (merchant) => {
      if (!isLoggedIn.value) {
        showLoginDialog()
        return
      }
      router.push(`/merchant/${merchant.id}`)
    }

    // 处理服务探索
    const handleServiceExplore = () => {
      if (!isLoggedIn.value) {
        showLoginDialog()
        return
      }
      router.push('/services')
    }

    // 处理快速服务
    const handleQuickService = (serviceType) => {
      if (!isLoggedIn.value) {
        showLoginDialog()
        return
      }
      router.push(`/services?type=${serviceType}`)
    }

    // 查看更多商家
    const handleViewMoreMerchants = () => {
      if (!isLoggedIn.value) {
        showLoginDialog()
        return
      }
      router.push('/merchants')
    }

    return {
      banners,
      categories,
      recommendMerchants,
      loginDialogVisible,
      registerDialogVisible,
      loading,
      isLoggedIn,
      userInfo,
      showLoginDialog,
      showRegisterDialog,
      handleLoginSuccess,
      handleRegisterSuccess,
      handleUserCommand,
      handleServiceClick,
      handleMerchantClick,
      handleServiceExplore,
      handleQuickService,
      handleViewMoreMerchants
    }
  }
}
</script>

<style scoped>
.home-page {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 头部样式 */
.header {
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo img {
  width: 40px;
  height: 40px;
}

.logo-text {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
}

.nav-menu {
  display: flex;
  gap: 40px;
}

.nav-item {
  color: #333;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  transition: color 0.3s;
}

.nav-item:hover,
.nav-item.active {
  color: #1890ff;
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 20px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f5f5;
}

.username {
  font-size: 14px;
  color: #333;
}

/* 主要内容样式 */
.main-content {
  padding-bottom: 60px;
}

/* 轮播图区域 */
.hero-section {
  background: #ffffff;
  padding: 20px 0;
}

.banner-image {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 12px;
}

.banner-overlay {
  position: absolute;
  top: 50%;
  left: 80px;
  transform: translateY(-50%);
  color: #ffffff;
  z-index: 2;
}

.banner-title {
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 16px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.banner-subtitle {
  font-size: 20px;
  margin-bottom: 32px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* 快速服务入口 */
.quick-services {
  background: #ffffff;
  padding: 40px 0;
  border-bottom: 1px solid #f0f0f0;
}

.quick-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
  max-width: 800px;
  margin: 0 auto;
}

.quick-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
  border-radius: 12px;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-item:hover {
  background: #e3f2fd;
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.quick-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  margin-bottom: 16px;
}

.quick-item span {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

/* 服务分类区域 */
.services-section {
  padding: 60px 0;
  background: #f8f9fa;
}

.section-header {
  text-align: center;
  margin-bottom: 48px;
}

.section-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

.section-subtitle {
  font-size: 16px;
  color: #666;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
}

.service-card {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #f0f0f0;
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #1890ff;
}

.service-image {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.service-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: #ff4d4f;
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.service-content {
  padding: 20px;
}

.service-name {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.service-desc {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.service-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-price {
  font-size: 16px;
  font-weight: bold;
  color: #ff4d4f;
}

/* 商家区域 */
.merchants-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.merchants-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.merchant-card {
  background: #ffffff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
}

.merchant-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.merchant-image {
  height: 200px;
  overflow: hidden;
}

.merchant-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.merchant-info {
  padding: 24px;
}

.merchant-name {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

.merchant-rating {
  margin-bottom: 12px;
}

.merchant-address {
  color: #666;
  margin-bottom: 16px;
  font-size: 14px;
}

.merchant-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 关于我们区域 */
.about-section {
  padding: 80px 0;
  background: #ffffff;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.feature-item {
  text-align: center;
  padding: 40px 20px;
}

.feature-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 24px;
  background: linear-gradient(135deg, #52c41a, #73d13d);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
}

.feature-item h3 {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}

.feature-item p {
  color: #666;
  line-height: 1.6;
}

/* 页脚样式 */
.footer {
  background: #333;
  color: #ffffff;
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h4 {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #ffffff;
}

.footer-section p {
  color: #cccccc;
  line-height: 1.6;
  margin-bottom: 8px;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #555;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    height: auto;
    padding: 20px 0;
    gap: 20px;
  }

  .nav-menu {
    gap: 20px;
  }

  .services-grid,
  .merchants-grid,
  .features-grid {
    grid-template-columns: 1fr;
  }

  .section-title {
    font-size: 28px;
  }

  .service-card,
  .feature-item {
    padding: 30px 20px;
  }
}
</style>
