package com.laundry.miniprogram.common;

/**
 * 响应码枚举
 */
public enum ResultCode {

    // 成功
    SUCCESS(200, "操作成功"),

    // 客户端错误
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    VALIDATION_ERROR(422, "参数验证失败"),

    // 服务器错误
    ERROR(500, "系统内部错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),

    // 业务错误码 (1000-1999)
    // 用户相关 (1000-1099)
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_DISABLED(1002, "用户已被禁用"),
    USER_ALREADY_EXISTS(1003, "用户已存在"),
    PHONE_ALREADY_EXISTS(1004, "手机号已存在"),
    INVALID_PHONE(1005, "手机号格式错误"),
    INVALID_CODE(1006, "验证码错误"),
    CODE_EXPIRED(1007, "验证码已过期"),

    // 认证相关 (1100-1199)
    LOGIN_FAILED(1101, "登录失败"),
    TOKEN_INVALID(1102, "令牌无效"),
    TOKEN_EXPIRED(1103, "令牌已过期"),
    WECHAT_AUTH_FAILED(1104, "微信授权失败"),
    INSUFFICIENT_PERMISSIONS(1105, "权限不足"),

    // 商家相关 (1200-1299)
    MERCHANT_NOT_FOUND(1201, "商家不存在"),
    MERCHANT_NOT_APPROVED(1202, "商家未通过审核"),
    MERCHANT_SUSPENDED(1203, "商家已被暂停"),
    DEPOSIT_NOT_PAID(1204, "保证金未缴纳"),
    INSUFFICIENT_BALANCE(1205, "余额不足"),

    // 服务相关 (1300-1399)
    SERVICE_NOT_FOUND(1301, "服务不存在"),
    SERVICE_NOT_AVAILABLE(1302, "服务不可用"),
    SERVICE_ALREADY_EXISTS(1303, "服务已存在"),

    // 订单相关 (1400-1499)
    ORDER_NOT_FOUND(1401, "订单不存在"),
    ORDER_STATUS_ERROR(1402, "订单状态错误"),
    ORDER_CANNOT_CANCEL(1403, "订单无法取消"),
    ORDER_ALREADY_PAID(1404, "订单已支付"),
    ORDER_EXPIRED(1405, "订单已过期"),

    // 支付相关 (1500-1599)
    PAYMENT_FAILED(1501, "支付失败"),
    PAYMENT_NOT_FOUND(1502, "支付记录不存在"),
    PAYMENT_AMOUNT_ERROR(1503, "支付金额错误"),
    REFUND_FAILED(1504, "退款失败"),
    PAYMENT_METHOD_ERROR(1505, "支付方式错误"),

    // 文件相关 (1600-1699)
    FILE_UPLOAD_FAILED(1601, "文件上传失败"),
    FILE_TYPE_NOT_ALLOWED(1602, "文件类型不允许"),
    FILE_SIZE_EXCEEDED(1603, "文件大小超限"),
    FILE_NOT_FOUND(1604, "文件不存在"),

    // 地址相关 (1700-1799)
    ADDRESS_NOT_FOUND(1701, "地址不存在"),
    DEFAULT_ADDRESS_REQUIRED(1702, "请设置默认地址"),

    // 评价相关 (1800-1899)
    REVIEW_NOT_FOUND(1801, "评价不存在"),
    REVIEW_ALREADY_EXISTS(1802, "已评价过"),
    REVIEW_NOT_ALLOWED(1803, "不允许评价"),

    // 消息相关 (1900-1999)
    MESSAGE_NOT_FOUND(1901, "消息不存在"),
    MESSAGE_SEND_FAILED(1902, "消息发送失败");

    /**
     * 响应码
     */
    private final Integer code;

    /**
     * 响应消息
     */
    private final String message;

    /**
     * 构造函数
     */
    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 获取响应码
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取响应消息
     */
    public String getMessage() {
        return message;
    }
}
