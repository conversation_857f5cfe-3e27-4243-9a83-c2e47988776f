package com.laundry.miniprogram.controller;

import com.laundry.miniprogram.common.ApiResponse;
import org.springframework.web.bind.annotation.*;
import java.util.*;

/**
 * 消息相关接口
 */
@RestController
@RequestMapping("/api/messages")
@CrossOrigin(origins = "*")
public class MessageController {

    /**
     * 获取会话列表
     */
    @GetMapping("/conversations")
    public ApiResponse<Map<String, Object>> getConversations(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<Map<String, Object>> conversations = new ArrayList<>();
        
        String[] merchantNames = {"洁净洗衣店", "快洁洗护", "高端护理中心", "便民洗衣", "专业洗护"};
        String[] lastMessages = {"您的订单已完成", "衣物正在清洗中", "请问有什么需要帮助的吗", "订单已确认", "感谢您的评价"};
        
        for (int i = 0; i < Math.min(merchantNames.length, size); i++) {
            Map<String, Object> conversation = new HashMap<>();
            conversation.put("id", i + 1);
            conversation.put("targetId", i + 1);
            conversation.put("targetType", "merchant");
            conversation.put("targetName", merchantNames[i]);
            conversation.put("targetAvatar", "https://img.yzcdn.cn/vant/cat.jpeg");
            conversation.put("lastMessage", lastMessages[i]);
            conversation.put("lastMessageTime", "2024-01-15 " + String.format("%02d", 10 + i) + ":30:00");
            conversation.put("unreadCount", i == 0 ? 2 : 0);
            conversations.add(conversation);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("content", conversations);
        result.put("totalElements", 10);
        result.put("totalPages", 1);
        result.put("currentPage", page);
        result.put("size", size);
        
        return ApiResponse.success(result);
    }

    /**
     * 获取会话消息
     */
    @GetMapping("/conversations/{targetId}")
    public ApiResponse<Map<String, Object>> getConversationMessages(
            @PathVariable Long targetId,
            @RequestParam String targetType,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<Map<String, Object>> messages = new ArrayList<>();
        
        String[] messageContents = {
            "您好，请问有什么可以帮助您的吗？",
            "我想咨询一下干洗的价格",
            "我们的干洗价格是25元/件，高档衣物45元/件",
            "好的，我想预约明天上门取件",
            "没问题，请提供您的地址和联系方式",
            "地址是北京市朝阳区三里屯街道1号，电话13800138000",
            "已为您安排，明天上午10点会有师傅上门取件",
            "谢谢！"
        };
        
        for (int i = 0; i < Math.min(messageContents.length, size); i++) {
            Map<String, Object> message = new HashMap<>();
            message.put("id", i + 1);
            message.put("conversationId", targetId);
            message.put("senderId", i % 2 == 0 ? targetId : 1);
            message.put("senderType", i % 2 == 0 ? "merchant" : "user");
            message.put("senderName", i % 2 == 0 ? "洁净洗衣店" : "张三");
            message.put("senderAvatar", "https://img.yzcdn.cn/vant/cat.jpeg");
            message.put("messageType", "text");
            message.put("content", messageContents[i]);
            message.put("sendTime", "2024-01-15 10:" + String.format("%02d", 30 + i * 2) + ":00");
            message.put("isRead", true);
            messages.add(message);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("content", messages);
        result.put("totalElements", 20);
        result.put("totalPages", 1);
        result.put("currentPage", page);
        result.put("size", size);
        
        return ApiResponse.success(result);
    }

    /**
     * 发送消息
     */
    @PostMapping("")
    public ApiResponse<Map<String, Object>> sendMessage(@RequestBody Map<String, Object> messageData) {
        Long conversationId = Long.valueOf(messageData.get("conversationId").toString());
        String messageType = (String) messageData.get("messageType");
        String content = (String) messageData.get("content");
        
        Map<String, Object> message = new HashMap<>();
        message.put("id", System.currentTimeMillis());
        message.put("conversationId", conversationId);
        message.put("senderId", 1);
        message.put("senderType", "user");
        message.put("senderName", "张三");
        message.put("senderAvatar", "https://img.yzcdn.cn/vant/cat.jpeg");
        message.put("messageType", messageType);
        message.put("content", content);
        message.put("sendTime", new Date());
        message.put("isRead", false);
        
        return ApiResponse.success(message);
    }

    /**
     * 标记消息已读
     */
    @PostMapping("/{messageId}/read")
    public ApiResponse<String> markMessageRead(@PathVariable Long messageId) {
        return ApiResponse.success("消息已标记为已读");
    }

    /**
     * 标记会话已读
     */
    @PostMapping("/conversations/{conversationId}/read")
    public ApiResponse<String> markConversationRead(@PathVariable Long conversationId) {
        return ApiResponse.success("会话已标记为已读");
    }

    /**
     * 删除消息
     */
    @DeleteMapping("/{messageId}")
    public ApiResponse<String> deleteMessage(@PathVariable Long messageId) {
        return ApiResponse.success("消息删除成功");
    }

    /**
     * 删除会话
     */
    @DeleteMapping("/conversations/{conversationId}")
    public ApiResponse<String> deleteConversation(@PathVariable Long conversationId) {
        return ApiResponse.success("会话删除成功");
    }

    /**
     * 获取系统通知
     */
    @GetMapping("/notifications")
    public ApiResponse<Map<String, Object>> getNotifications(
            @RequestParam(required = false) String type,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<Map<String, Object>> notifications = new ArrayList<>();
        
        String[] types = {"order", "system", "promotion"};
        String[] typeNames = {"订单通知", "系统通知", "优惠活动"};
        String[] titles = {
            "订单状态更新",
            "系统维护通知",
            "新用户专享优惠",
            "订单完成提醒",
            "账户安全提醒"
        };
        String[] contents = {
            "您的订单LD20240115001已完成，请及时确认收货",
            "系统将于今晚23:00-01:00进行维护，期间可能影响使用",
            "新用户注册即送50元优惠券，快来体验吧！",
            "您的洗衣订单已完成，衣物已送达指定地址",
            "检测到您的账户在异地登录，请注意账户安全"
        };
        
        for (int i = 0; i < Math.min(titles.length, size); i++) {
            Map<String, Object> notification = new HashMap<>();
            notification.put("id", i + 1);
            notification.put("type", types[i % types.length]);
            notification.put("typeName", typeNames[i % typeNames.length]);
            notification.put("title", titles[i]);
            notification.put("content", contents[i]);
            notification.put("isRead", i > 1);
            notification.put("createTime", "2024-01-" + String.format("%02d", 15 - i) + " 10:30:00");
            notifications.add(notification);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("content", notifications);
        result.put("totalElements", 20);
        result.put("totalPages", 1);
        result.put("currentPage", page);
        result.put("size", size);
        
        return ApiResponse.success(result);
    }

    /**
     * 标记通知已读
     */
    @PostMapping("/notifications/{notificationId}/read")
    public ApiResponse<String> markNotificationRead(@PathVariable Long notificationId) {
        return ApiResponse.success("通知已标记为已读");
    }

    /**
     * 获取未读消息数量
     */
    @GetMapping("/unread-count")
    public ApiResponse<Map<String, Object>> getUnreadCount() {
        Map<String, Object> count = new HashMap<>();
        count.put("messageCount", 3);
        count.put("notificationCount", 2);
        count.put("totalCount", 5);
        
        return ApiResponse.success(count);
    }

    /**
     * 搜索消息
     */
    @GetMapping("/search")
    public ApiResponse<Map<String, Object>> searchMessages(
            @RequestParam String keyword,
            @RequestParam(required = false) Long targetId,
            @RequestParam(required = false) String targetType,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<Map<String, Object>> messages = new ArrayList<>();
        
        // 模拟搜索结果
        Map<String, Object> message = new HashMap<>();
        message.put("id", 1);
        message.put("conversationId", 1);
        message.put("senderId", 1);
        message.put("senderType", "merchant");
        message.put("senderName", "洁净洗衣店");
        message.put("content", "您好，请问有什么可以帮助您的吗？包含关键词：" + keyword);
        message.put("sendTime", "2024-01-15 10:30:00");
        messages.add(message);
        
        Map<String, Object> result = new HashMap<>();
        result.put("content", messages);
        result.put("totalElements", 1);
        result.put("totalPages", 1);
        result.put("currentPage", page);
        result.put("size", size);
        
        return ApiResponse.success(result);
    }

    /**
     * 举报消息
     */
    @PostMapping("/{messageId}/report")
    public ApiResponse<String> reportMessage(@PathVariable Long messageId, @RequestBody Map<String, String> request) {
        String reason = request.get("reason");
        String description = request.get("description");
        
        return ApiResponse.success("举报已提交，我们会尽快处理");
    }
}
