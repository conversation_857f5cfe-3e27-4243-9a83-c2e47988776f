package com.laundry.miniprogram.controller;

import com.laundry.miniprogram.common.ApiResponse;
import org.springframework.web.bind.annotation.*;
import java.util.*;

/**
 * 订单相关接口
 */
@RestController
@RequestMapping("/api/orders")
@CrossOrigin(origins = "*")
public class OrderController {

    /**
     * 创建订单
     */
    @PostMapping("")
    public ApiResponse<Map<String, Object>> createOrder(@RequestBody Map<String, Object> orderData) {
        Map<String, Object> order = new HashMap<>();
        order.put("id", System.currentTimeMillis());
        order.put("orderNumber", "LD" + System.currentTimeMillis());
        order.put("status", "pending");
        order.put("paymentStatus", "unpaid");
        order.put("totalAmount", orderData.get("totalAmount"));
        order.put("createTime", new Date());
        
        return ApiResponse.success(order);
    }

    /**
     * 获取订单列表
     */
    @GetMapping("")
    public ApiResponse<Map<String, Object>> getOrders(
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        List<Map<String, Object>> orders = new ArrayList<>();
        
        String[] statuses = {"pending", "confirmed", "processing", "completed", "cancelled"};
        String[] statusNames = {"待确认", "已确认", "处理中", "已完成", "已取消"};
        
        for (int i = 0; i < size; i++) {
            Map<String, Object> order = new HashMap<>();
            order.put("id", (page - 1) * size + i + 1);
            order.put("orderNumber", "LD202401" + String.format("%04d", i + 1));
            order.put("status", statuses[i % statuses.length]);
            order.put("statusName", statusNames[i % statusNames.length]);
            order.put("serviceName", "精品干洗服务");
            order.put("merchantName", "洁净洗衣店");
            order.put("totalAmount", 50.0 + i * 10);
            order.put("createTime", "2024-01-" + String.format("%02d", (i % 30) + 1) + " 10:30:00");
            
            // 订单项
            List<Map<String, Object>> items = new ArrayList<>();
            Map<String, Object> item = new HashMap<>();
            item.put("serviceName", "精品干洗");
            item.put("quantity", 2);
            item.put("price", 25.0);
            item.put("totalPrice", 50.0);
            items.add(item);
            order.put("items", items);
            
            orders.add(order);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("content", orders);
        result.put("totalElements", 50);
        result.put("totalPages", 5);
        result.put("currentPage", page);
        result.put("size", size);
        
        return ApiResponse.success(result);
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/{id}")
    public ApiResponse<Map<String, Object>> getOrderDetail(@PathVariable Long id) {
        Map<String, Object> order = new HashMap<>();
        order.put("id", id);
        order.put("orderNumber", "LD202401" + String.format("%04d", id.intValue()));
        order.put("status", "processing");
        order.put("statusName", "处理中");
        order.put("paymentStatus", "paid");
        order.put("paymentStatusName", "已支付");
        order.put("totalAmount", 75.0);
        order.put("finalAmount", 75.0);
        order.put("createTime", "2024-01-15 10:30:00");
        order.put("payTime", "2024-01-15 10:35:00");
        
        // 客户信息
        Map<String, Object> customer = new HashMap<>();
        customer.put("name", "张三");
        customer.put("phone", "13800138000");
        customer.put("pickupAddress", "北京市朝阳区三里屯街道1号");
        customer.put("deliveryAddress", "北京市朝阳区三里屯街道1号");
        order.put("customer", customer);
        
        // 商家信息
        Map<String, Object> merchant = new HashMap<>();
        merchant.put("id", 1);
        merchant.put("name", "洁净洗衣店");
        merchant.put("phone", "************");
        merchant.put("address", "北京市朝阳区工体北路2号");
        order.put("merchant", merchant);
        
        // 订单项
        List<Map<String, Object>> items = new ArrayList<>();
        Map<String, Object> item1 = new HashMap<>();
        item1.put("serviceName", "精品干洗");
        item1.put("quantity", 2);
        item1.put("price", 25.0);
        item1.put("totalPrice", 50.0);
        items.add(item1);
        
        Map<String, Object> item2 = new HashMap<>();
        item2.put("serviceName", "高档护理");
        item2.put("quantity", 1);
        item2.put("price", 25.0);
        item2.put("totalPrice", 25.0);
        items.add(item2);
        
        order.put("items", items);
        
        // 订单进度
        List<Map<String, Object>> progress = new ArrayList<>();
        Map<String, Object> step1 = new HashMap<>();
        step1.put("status", "pending");
        step1.put("statusName", "订单创建");
        step1.put("description", "订单已创建，等待商家确认");
        step1.put("time", "2024-01-15 10:30:00");
        progress.add(step1);
        
        Map<String, Object> step2 = new HashMap<>();
        step2.put("status", "confirmed");
        step2.put("statusName", "订单确认");
        step2.put("description", "商家已确认订单");
        step2.put("time", "2024-01-15 11:00:00");
        progress.add(step2);
        
        Map<String, Object> step3 = new HashMap<>();
        step3.put("status", "processing");
        step3.put("statusName", "处理中");
        step3.put("description", "衣物正在清洗中");
        step3.put("time", "2024-01-15 14:00:00");
        progress.add(step3);
        
        order.put("progress", progress);
        
        return ApiResponse.success(order);
    }

    /**
     * 取消订单
     */
    @PostMapping("/{id}/cancel")
    public ApiResponse<String> cancelOrder(@PathVariable Long id, @RequestBody Map<String, String> request) {
        String reason = request.get("reason");
        return ApiResponse.success("订单取消成功");
    }

    /**
     * 确认收货
     */
    @PostMapping("/{id}/confirm")
    public ApiResponse<String> confirmOrder(@PathVariable Long id) {
        return ApiResponse.success("确认收货成功");
    }

    /**
     * 申请退款
     */
    @PostMapping("/{id}/refund")
    public ApiResponse<String> requestRefund(@PathVariable Long id, @RequestBody Map<String, String> request) {
        String reason = request.get("reason");
        String description = request.get("description");
        return ApiResponse.success("退款申请已提交");
    }

    /**
     * 订单评价
     */
    @PostMapping("/{id}/review")
    public ApiResponse<String> reviewOrder(@PathVariable Long id, @RequestBody Map<String, Object> reviewData) {
        return ApiResponse.success("评价提交成功");
    }

    /**
     * 再次下单
     */
    @PostMapping("/{id}/reorder")
    public ApiResponse<Map<String, Object>> reorder(@PathVariable Long id) {
        Map<String, Object> order = new HashMap<>();
        order.put("id", System.currentTimeMillis());
        order.put("orderNumber", "LD" + System.currentTimeMillis());
        order.put("status", "pending");
        order.put("totalAmount", 75.0);
        
        return ApiResponse.success(order);
    }

    /**
     * 获取订单统计
     */
    @GetMapping("/statistics")
    public ApiResponse<Map<String, Object>> getOrderStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalOrders", 25);
        statistics.put("pendingOrders", 3);
        statistics.put("processingOrders", 5);
        statistics.put("completedOrders", 15);
        statistics.put("cancelledOrders", 2);
        statistics.put("totalAmount", 2580.0);
        statistics.put("averageAmount", 103.2);
        
        return ApiResponse.success(statistics);
    }

    /**
     * 预估订单价格
     */
    @PostMapping("/estimate")
    public ApiResponse<Map<String, Object>> estimatePrice(@RequestBody Map<String, Object> orderData) {
        Map<String, Object> estimate = new HashMap<>();
        estimate.put("subtotal", 100.0);
        estimate.put("deliveryFee", 10.0);
        estimate.put("serviceFee", 5.0);
        estimate.put("discount", 15.0);
        estimate.put("total", 100.0);
        
        return ApiResponse.success(estimate);
    }

    /**
     * 获取可用优惠券
     */
    @GetMapping("/available-coupons")
    public ApiResponse<List<Map<String, Object>>> getAvailableCoupons(
            @RequestParam Long serviceId,
            @RequestParam Double amount) {
        
        List<Map<String, Object>> coupons = new ArrayList<>();
        
        Map<String, Object> coupon1 = new HashMap<>();
        coupon1.put("id", 1);
        coupon1.put("name", "新用户优惠券");
        coupon1.put("discount", 10.0);
        coupon1.put("minAmount", 50.0);
        coupon1.put("expireTime", "2024-12-31");
        coupons.add(coupon1);
        
        Map<String, Object> coupon2 = new HashMap<>();
        coupon2.put("id", 2);
        coupon2.put("name", "满100减20");
        coupon2.put("discount", 20.0);
        coupon2.put("minAmount", 100.0);
        coupon2.put("expireTime", "2024-06-30");
        coupons.add(coupon2);
        
        return ApiResponse.success(coupons);
    }
}
