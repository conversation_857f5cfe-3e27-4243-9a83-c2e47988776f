package com.laundry.miniprogram.controller;

import com.laundry.miniprogram.common.ApiResponse;
import org.springframework.web.bind.annotation.*;
import java.util.*;

/**
 * 支付相关接口
 */
@RestController
@RequestMapping("/api/payment")
@CrossOrigin(origins = "*")
public class PaymentController {

    /**
     * 创建支付订单
     */
    @PostMapping("/create")
    public ApiResponse<Map<String, Object>> createPayment(@RequestBody Map<String, Object> paymentData) {
        Long orderId = Long.valueOf(paymentData.get("orderId").toString());
        String paymentMethod = (String) paymentData.get("paymentMethod");
        Double amount = (Double) paymentData.get("amount");
        
        Map<String, Object> payment = new HashMap<>();
        payment.put("paymentId", "PAY" + System.currentTimeMillis());
        payment.put("orderId", orderId);
        payment.put("amount", amount);
        payment.put("paymentMethod", paymentMethod);
        payment.put("status", "pending");
        payment.put("createTime", new Date());
        
        // 模拟微信支付参数
        if ("wechat".equals(paymentMethod)) {
            Map<String, Object> wxPayParams = new HashMap<>();
            wxPayParams.put("appId", "wx1234567890");
            wxPayParams.put("timeStamp", String.valueOf(System.currentTimeMillis() / 1000));
            wxPayParams.put("nonceStr", "random_string_" + System.currentTimeMillis());
            wxPayParams.put("package", "prepay_id=wx" + System.currentTimeMillis());
            wxPayParams.put("signType", "MD5");
            wxPayParams.put("paySign", "mock_pay_sign");
            payment.put("wxPayParams", wxPayParams);
        }
        
        return ApiResponse.success(payment);
    }

    /**
     * 查询支付状态
     */
    @GetMapping("/status/{paymentId}")
    public ApiResponse<Map<String, Object>> getPaymentStatus(@PathVariable String paymentId) {
        Map<String, Object> payment = new HashMap<>();
        payment.put("paymentId", paymentId);
        payment.put("status", "success");
        payment.put("statusName", "支付成功");
        payment.put("payTime", new Date());
        
        return ApiResponse.success(payment);
    }

    /**
     * 支付回调（微信支付）
     */
    @PostMapping("/callback/wechat")
    public String wechatPayCallback(@RequestBody String xmlData) {
        // 处理微信支付回调
        System.out.println("微信支付回调: " + xmlData);
        
        // 返回成功响应
        return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
    }

    /**
     * 支付回调（支付宝）
     */
    @PostMapping("/callback/alipay")
    public String alipayCallback(@RequestParam Map<String, String> params) {
        // 处理支付宝回调
        System.out.println("支付宝回调: " + params);
        
        return "success";
    }

    /**
     * 申请退款
     */
    @PostMapping("/refund")
    public ApiResponse<Map<String, Object>> requestRefund(@RequestBody Map<String, Object> refundData) {
        String paymentId = (String) refundData.get("paymentId");
        Double refundAmount = (Double) refundData.get("refundAmount");
        String reason = (String) refundData.get("reason");
        
        Map<String, Object> refund = new HashMap<>();
        refund.put("refundId", "REF" + System.currentTimeMillis());
        refund.put("paymentId", paymentId);
        refund.put("refundAmount", refundAmount);
        refund.put("reason", reason);
        refund.put("status", "processing");
        refund.put("createTime", new Date());
        
        return ApiResponse.success(refund);
    }

    /**
     * 查询退款状态
     */
    @GetMapping("/refund/status/{refundId}")
    public ApiResponse<Map<String, Object>> getRefundStatus(@PathVariable String refundId) {
        Map<String, Object> refund = new HashMap<>();
        refund.put("refundId", refundId);
        refund.put("status", "success");
        refund.put("statusName", "退款成功");
        refund.put("refundTime", new Date());
        
        return ApiResponse.success(refund);
    }

    /**
     * 获取支付方式列表
     */
    @GetMapping("/methods")
    public ApiResponse<List<Map<String, Object>>> getPaymentMethods() {
        List<Map<String, Object>> methods = new ArrayList<>();
        
        Map<String, Object> wechat = new HashMap<>();
        wechat.put("id", "wechat");
        wechat.put("name", "微信支付");
        wechat.put("icon", "/images/payment/wechat.png");
        wechat.put("enabled", true);
        methods.add(wechat);
        
        Map<String, Object> alipay = new HashMap<>();
        alipay.put("id", "alipay");
        alipay.put("name", "支付宝");
        alipay.put("icon", "/images/payment/alipay.png");
        alipay.put("enabled", true);
        methods.add(alipay);
        
        Map<String, Object> balance = new HashMap<>();
        balance.put("id", "balance");
        balance.put("name", "余额支付");
        balance.put("icon", "/images/payment/balance.png");
        balance.put("enabled", true);
        balance.put("balance", 1288.50);
        methods.add(balance);
        
        return ApiResponse.success(methods);
    }

    /**
     * 余额支付
     */
    @PostMapping("/balance")
    public ApiResponse<Map<String, Object>> balancePayment(@RequestBody Map<String, Object> paymentData) {
        Long orderId = Long.valueOf(paymentData.get("orderId").toString());
        Double amount = (Double) paymentData.get("amount");
        String password = (String) paymentData.get("password");
        
        // 验证支付密码（模拟）
        if (!"123456".equals(password)) {
            return ApiResponse.error("支付密码错误");
        }
        
        Map<String, Object> payment = new HashMap<>();
        payment.put("paymentId", "BAL" + System.currentTimeMillis());
        payment.put("orderId", orderId);
        payment.put("amount", amount);
        payment.put("status", "success");
        payment.put("payTime", new Date());
        
        return ApiResponse.success(payment);
    }

    /**
     * 获取支付记录
     */
    @GetMapping("/records")
    public ApiResponse<List<Map<String, Object>>> getPaymentRecords(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<Map<String, Object>> records = new ArrayList<>();
        
        for (int i = 1; i <= size; i++) {
            Map<String, Object> record = new HashMap<>();
            record.put("id", i);
            record.put("paymentId", "PAY202401" + String.format("%04d", i));
            record.put("orderId", "LD202401" + String.format("%04d", i));
            record.put("amount", 50.0 + i * 10);
            record.put("paymentMethod", i % 3 == 0 ? "balance" : (i % 2 == 0 ? "wechat" : "alipay"));
            record.put("paymentMethodName", i % 3 == 0 ? "余额支付" : (i % 2 == 0 ? "微信支付" : "支付宝"));
            record.put("status", "success");
            record.put("statusName", "支付成功");
            record.put("payTime", "2024-01-" + String.format("%02d", i) + " 10:30:00");
            records.add(record);
        }
        
        return ApiResponse.success(records);
    }

    /**
     * 设置支付密码
     */
    @PostMapping("/password/set")
    public ApiResponse<String> setPaymentPassword(@RequestBody Map<String, String> request) {
        String password = request.get("password");
        String confirmPassword = request.get("confirmPassword");
        
        if (!password.equals(confirmPassword)) {
            return ApiResponse.error("两次输入的密码不一致");
        }
        
        return ApiResponse.success("支付密码设置成功");
    }

    /**
     * 修改支付密码
     */
    @PostMapping("/password/change")
    public ApiResponse<String> changePaymentPassword(@RequestBody Map<String, String> request) {
        String oldPassword = request.get("oldPassword");
        String newPassword = request.get("newPassword");
        
        // 验证旧密码（模拟）
        if (!"123456".equals(oldPassword)) {
            return ApiResponse.error("原密码错误");
        }
        
        return ApiResponse.success("支付密码修改成功");
    }

    /**
     * 验证支付密码
     */
    @PostMapping("/password/verify")
    public ApiResponse<Boolean> verifyPaymentPassword(@RequestBody Map<String, String> request) {
        String password = request.get("password");
        boolean isValid = "123456".equals(password);
        
        return ApiResponse.success(isValid);
    }
}
