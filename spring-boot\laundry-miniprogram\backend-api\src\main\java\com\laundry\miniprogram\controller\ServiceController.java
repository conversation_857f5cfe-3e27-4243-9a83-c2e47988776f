package com.laundry.miniprogram.controller;

import com.laundry.miniprogram.common.ApiResponse;
import org.springframework.web.bind.annotation.*;
import java.util.*;

/**
 * 服务相关接口
 */
@RestController
@RequestMapping("/api/services")
@CrossOrigin(origins = "*")
public class ServiceController {

    /**
     * 获取服务分类
     */
    @GetMapping("/categories")
    public ApiResponse<List<Map<String, Object>>> getCategories() {
        List<Map<String, Object>> categories = new ArrayList<>();
        
        String[] categoryNames = {"洗衣服", "洗鞋", "洗萌宠", "洗包包", "洗被子", "洗车", "上门清洗", "疏通厕所"};
        String[] categoryIcons = {"/images/category/clothes.png", "/images/category/shoes.png", 
                                "/images/category/pet.png", "/images/category/bag.png",
                                "/images/category/bedding.png", "/images/category/car.png",
                                "/images/category/home.png", "/images/category/toilet.png"};
        String[] categoryColors = {"#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", 
                                 "#FFEAA7", "#DDA0DD", "#98D8C8", "#F7DC6F"};
        
        for (int i = 0; i < categoryNames.length; i++) {
            Map<String, Object> category = new HashMap<>();
            category.put("id", i + 1);
            category.put("name", categoryNames[i]);
            category.put("icon", categoryIcons[i]);
            category.put("color", categoryColors[i]);
            category.put("desc", categoryNames[i] + "专业服务");
            categories.add(category);
        }
        
        return ApiResponse.success(categories);
    }

    /**
     * 获取服务列表
     */
    @GetMapping("")
    public ApiResponse<Map<String, Object>> getServices(
            @RequestParam(required = false) Integer categoryId,
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "default") String sort,
            @RequestParam(required = false) Double minPrice,
            @RequestParam(required = false) Double maxPrice,
            @RequestParam(required = false) Integer minRating,
            @RequestParam(required = false) Integer maxDistance,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int limit) {
        
        List<Map<String, Object>> services = new ArrayList<>();
        
        // 模拟服务数据
        String[] serviceNames = {"精品干洗", "标准水洗", "高端护理", "快速清洗", "深度清洁", 
                               "专业除菌", "奢侈品护理", "家庭套餐", "商务洗护", "学生优惠"};
        String[] merchantNames = {"洁净洗衣店", "快洁洗护", "高端护理中心", "便民洗衣", "专业洗护",
                                "品质洗衣", "奢护专家", "家庭洗护", "商务洗衣", "学生洗衣"};
        
        for (int i = 0; i < Math.min(serviceNames.length, limit); i++) {
            Map<String, Object> service = new HashMap<>();
            service.put("id", (page - 1) * limit + i + 1);
            service.put("name", serviceNames[i]);
            service.put("description", serviceNames[i] + "，专业品质保证");
            service.put("price", 20.0 + i * 5);
            service.put("unit", "件");
            service.put("rating", 4.0 + (i % 10) * 0.1);
            service.put("orderCount", 100 + i * 20);
            service.put("merchantId", i + 1);
            service.put("merchantName", merchantNames[i]);
            service.put("distance", 0.5 + i * 0.3);
            service.put("categoryId", categoryId != null ? categoryId : (i % 8) + 1);
            
            List<String> images = new ArrayList<>();
            images.add("https://img.yzcdn.cn/vant/cat.jpeg");
            images.add("https://img.yzcdn.cn/vant/tree.jpg");
            service.put("images", images);
            
            services.add(service);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("content", services);
        result.put("totalElements", 100);
        result.put("totalPages", 5);
        result.put("currentPage", page);
        result.put("size", limit);
        
        return ApiResponse.success(result);
    }

    /**
     * 获取服务详情
     */
    @GetMapping("/{id}")
    public ApiResponse<Map<String, Object>> getServiceDetail(@PathVariable Long id) {
        Map<String, Object> service = new HashMap<>();
        service.put("id", id);
        service.put("name", "精品干洗服务");
        service.put("description", "专业干洗设备，进口洗涤剂，呵护您的每一件衣物");
        service.put("price", 25.0);
        service.put("unit", "件");
        service.put("rating", 4.8);
        service.put("orderCount", 1580);
        service.put("categoryId", 1);
        service.put("categoryName", "洗衣服");
        
        // 商家信息
        Map<String, Object> merchant = new HashMap<>();
        merchant.put("id", 1);
        merchant.put("name", "洁净洗衣店");
        merchant.put("avatar", "https://img.yzcdn.cn/vant/cat.jpeg");
        merchant.put("phone", "************");
        merchant.put("address", "北京市朝阳区三里屯街道1号");
        merchant.put("distance", 0.8);
        merchant.put("rating", 4.9);
        merchant.put("serviceCount", 15);
        service.put("merchant", merchant);
        
        // 服务图片
        List<String> images = new ArrayList<>();
        images.add("https://img.yzcdn.cn/vant/cat.jpeg");
        images.add("https://img.yzcdn.cn/vant/tree.jpg");
        images.add("https://img.yzcdn.cn/vant/leaf.jpg");
        service.put("images", images);
        
        // 服务详情
        service.put("detail", "我们采用进口干洗设备和环保洗涤剂，专业处理各类面料，确保衣物清洁的同时保护纤维结构。");
        
        // 服务规格
        List<Map<String, Object>> specs = new ArrayList<>();
        Map<String, Object> spec1 = new HashMap<>();
        spec1.put("name", "普通衣物");
        spec1.put("price", 25.0);
        spec1.put("unit", "件");
        specs.add(spec1);
        
        Map<String, Object> spec2 = new HashMap<>();
        spec2.put("name", "高档衣物");
        spec2.put("price", 45.0);
        spec2.put("unit", "件");
        specs.add(spec2);
        
        service.put("specs", specs);
        
        return ApiResponse.success(service);
    }

    /**
     * 获取推荐服务
     */
    @GetMapping("/recommend")
    public ApiResponse<List<Map<String, Object>>> getRecommendServices(
            @RequestParam(defaultValue = "10") int limit) {
        
        List<Map<String, Object>> services = new ArrayList<>();
        
        String[] serviceNames = {"热门干洗", "快速水洗", "奢侈品护理", "家庭套餐", "学生优惠"};
        String[] merchantNames = {"洁净洗衣店", "快洁洗护", "高端护理中心", "家庭洗护", "学生洗衣"};
        
        for (int i = 0; i < Math.min(serviceNames.length, limit); i++) {
            Map<String, Object> service = new HashMap<>();
            service.put("id", i + 1);
            service.put("name", serviceNames[i]);
            service.put("price", 20.0 + i * 8);
            service.put("rating", 4.5 + i * 0.1);
            service.put("merchantName", merchantNames[i]);
            
            List<String> images = new ArrayList<>();
            images.add("https://img.yzcdn.cn/vant/cat.jpeg");
            service.put("images", images);
            
            services.add(service);
        }
        
        return ApiResponse.success(services);
    }

    /**
     * 获取热门服务
     */
    @GetMapping("/hot")
    public ApiResponse<List<Map<String, Object>>> getHotServices(
            @RequestParam(defaultValue = "10") int limit) {
        
        return getRecommendServices(limit);
    }

    /**
     * 搜索建议
     */
    @GetMapping("/search/suggestions")
    public ApiResponse<List<String>> getSearchSuggestions(@RequestParam String keyword) {
        List<String> suggestions = new ArrayList<>();
        
        if (keyword.contains("洗")) {
            suggestions.add("洗衣服");
            suggestions.add("洗鞋");
            suggestions.add("洗被子");
        } else if (keyword.contains("干")) {
            suggestions.add("干洗");
            suggestions.add("干洗店");
        } else {
            suggestions.add("精品干洗");
            suggestions.add("快速水洗");
            suggestions.add("专业护理");
        }
        
        return ApiResponse.success(suggestions);
    }
}
