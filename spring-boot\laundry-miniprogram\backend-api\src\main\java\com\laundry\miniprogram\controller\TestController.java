package com.laundry.miniprogram.controller;

import com.laundry.miniprogram.common.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 */
@RestController
@RequestMapping("/test")
public class TestController {

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", LocalDateTime.now());
        data.put("service", "洗护小程序API");
        data.put("version", "1.0.0");
        
        return Result.success("服务运行正常", data);
    }

    /**
     * 获取服务信息
     */
    @GetMapping("/info")
    public Result<Map<String, Object>> info() {
        Map<String, Object> data = new HashMap<>();
        data.put("name", "洗护小程序API服务");
        data.put("description", "提供用户端、商家端、管理端的API接口");
        data.put("version", "1.0.0");
        data.put("author", "开发团队");

        return Result.success(data);
    }

    /**
     * 简单的Hello接口
     */
    @GetMapping("/hello")
    public Result<String> hello() {
        return Result.success("Hello, 洗护小程序API正在运行！");
    }
}
