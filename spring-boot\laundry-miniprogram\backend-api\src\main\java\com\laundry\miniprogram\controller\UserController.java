package com.laundry.miniprogram.controller;

import com.laundry.miniprogram.common.ApiResponse;
import org.springframework.web.bind.annotation.*;
import java.util.*;

/**
 * 用户相关接口
 */
@RestController
@RequestMapping("/api/user")
@CrossOrigin(origins = "*")
public class UserController {

    /**
     * 微信登录
     */
    @PostMapping("/wx-login")
    public ApiResponse<Map<String, Object>> wxLogin(@RequestBody Map<String, String> request) {
        String code = request.get("code");
        
        // 模拟微信登录逻辑
        Map<String, Object> result = new HashMap<>();
        result.put("token", "mock_token_" + System.currentTimeMillis());
        result.put("userInfo", createMockUser());
        
        return ApiResponse.success(result);
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/info")
    public ApiResponse<Map<String, Object>> getUserInfo() {
        return ApiResponse.success(createMockUser());
    }

    /**
     * 更新用户信息
     */
    @PostMapping("/update")
    public ApiResponse<String> updateUserInfo(@RequestBody Map<String, Object> userInfo) {
        return ApiResponse.success("更新成功");
    }

    /**
     * 获取用户地址列表
     */
    @GetMapping("/addresses")
    public ApiResponse<List<Map<String, Object>>> getAddresses() {
        List<Map<String, Object>> addresses = new ArrayList<>();
        
        Map<String, Object> address1 = new HashMap<>();
        address1.put("id", 1);
        address1.put("name", "张三");
        address1.put("phone", "13800138000");
        address1.put("province", "北京市");
        address1.put("city", "北京市");
        address1.put("district", "朝阳区");
        address1.put("detail", "三里屯街道1号");
        address1.put("isDefault", true);
        addresses.add(address1);
        
        Map<String, Object> address2 = new HashMap<>();
        address2.put("id", 2);
        address2.put("name", "李四");
        address2.put("phone", "13900139000");
        address2.put("province", "上海市");
        address2.put("city", "上海市");
        address2.put("district", "浦东新区");
        address2.put("detail", "陆家嘴金融中心2号");
        address2.put("isDefault", false);
        addresses.add(address2);
        
        return ApiResponse.success(addresses);
    }

    /**
     * 添加地址
     */
    @PostMapping("/addresses")
    public ApiResponse<String> addAddress(@RequestBody Map<String, Object> address) {
        return ApiResponse.success("地址添加成功");
    }

    /**
     * 更新地址
     */
    @PutMapping("/addresses/{id}")
    public ApiResponse<String> updateAddress(@PathVariable Long id, @RequestBody Map<String, Object> address) {
        return ApiResponse.success("地址更新成功");
    }

    /**
     * 删除地址
     */
    @DeleteMapping("/addresses/{id}")
    public ApiResponse<String> deleteAddress(@PathVariable Long id) {
        return ApiResponse.success("地址删除成功");
    }

    /**
     * 设置默认地址
     */
    @PostMapping("/addresses/{id}/default")
    public ApiResponse<String> setDefaultAddress(@PathVariable Long id) {
        return ApiResponse.success("默认地址设置成功");
    }

    /**
     * 获取用户钱包信息
     */
    @GetMapping("/wallet")
    public ApiResponse<Map<String, Object>> getWallet() {
        Map<String, Object> wallet = new HashMap<>();
        wallet.put("balance", 1288.50);
        wallet.put("points", 2580);
        wallet.put("coupons", 5);
        
        return ApiResponse.success(wallet);
    }

    /**
     * 钱包充值
     */
    @PostMapping("/wallet/recharge")
    public ApiResponse<Map<String, Object>> recharge(@RequestBody Map<String, Object> request) {
        Double amount = (Double) request.get("amount");
        String paymentMethod = (String) request.get("paymentMethod");
        
        Map<String, Object> result = new HashMap<>();
        result.put("orderId", "recharge_" + System.currentTimeMillis());
        result.put("amount", amount);
        result.put("paymentMethod", paymentMethod);
        
        return ApiResponse.success(result);
    }

    /**
     * 获取钱包交易记录
     */
    @GetMapping("/wallet/transactions")
    public ApiResponse<List<Map<String, Object>>> getTransactions(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<Map<String, Object>> transactions = new ArrayList<>();
        
        for (int i = 1; i <= size; i++) {
            Map<String, Object> transaction = new HashMap<>();
            transaction.put("id", i);
            transaction.put("type", i % 2 == 0 ? "recharge" : "consume");
            transaction.put("amount", 50.0 + i * 10);
            transaction.put("description", i % 2 == 0 ? "钱包充值" : "洗衣服务消费");
            transaction.put("createTime", "2024-01-" + String.format("%02d", i) + " 10:30:00");
            transactions.add(transaction);
        }
        
        return ApiResponse.success(transactions);
    }

    /**
     * 创建模拟用户数据
     */
    private Map<String, Object> createMockUser() {
        Map<String, Object> user = new HashMap<>();
        user.put("id", 1);
        user.put("openid", "mock_openid_123");
        user.put("nickname", "微信用户");
        user.put("avatar", "https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLL4JibbVkYlJOlXwqZQGpHJGqJqBdwdGhQCMqKHdcHHjruXXkSeqJoOjF5lbEhIhGlBOPQrRDib8Aw/132");
        user.put("phone", "13800138000");
        user.put("gender", 1);
        user.put("city", "北京");
        user.put("province", "北京");
        user.put("country", "中国");
        user.put("registerTime", "2024-01-01 10:00:00");
        user.put("lastLoginTime", "2024-01-15 15:30:00");
        user.put("status", "active");
        
        return user;
    }
}
