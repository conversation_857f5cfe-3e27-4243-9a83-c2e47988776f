server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: laundry-miniprogram-api

  # 数据库配置
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password:

  # H2数据库控制台
  h2:
    console:
      enabled: true
      path: /h2-console

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB

  # JSON配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: NON_NULL

# 自定义配置
laundry:
  # JWT配置
  jwt:
    secret: laundry-miniprogram-secret-key-2024
    expiration: 604800 # 7天，单位秒
    header: Authorization
    prefix: Bearer

  # 微信小程序配置
  wechat:
    miniapp:
      # 用户端小程序
      user:
        app-id: your-user-appid
        secret: your-user-secret
      # 商家端小程序
      merchant:
        app-id: your-merchant-appid
        secret: your-merchant-secret
      # 管理端小程序
      admin:
        app-id: your-admin-appid
        secret: your-admin-secret

  # 支付配置
  pay:
    wechat:
      mch-id: your-mch-id
      api-key: your-api-key
      cert-path: classpath:cert/apiclient_cert.p12
      notify-url: https://your-domain.com/api/pay/notify

  # 文件存储配置
  file:
    upload:
      path: /uploads/
      domain: https://your-domain.com
      allowed-types: jpg,jpeg,png,gif,pdf,doc,docx

  # 业务配置
  business:
    # 平台佣金比例
    commission-rate: 0.05
    # 商家保证金
    deposit-amount: 1000.00
    # 最小提现金额
    min-withdraw-amount: 100.00
    # 订单自动确认时间（小时）
    auto-confirm-hours: 72

# 日志配置
logging:
  level:
    '[com.laundry]': debug
    '[org.springframework.security]': debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/laundry-miniprogram.log
