// 管理端小程序入口文件
const env = require('./config/env');

App({
  globalData: {
    userInfo: null,
    systemInfo: null,
    adminInfo: null,
    appId: 'wxa09ca7461e763be5', // 管理端使用相同AppID
    version: '1.0.0',
    environment: env.currentEnv || 'development'
  },

  onLaunch(options) {
    console.log('🔧 管理端小程序启动', options);
    this.initApp();
  },

  onShow(options) {
    console.log('📱 管理端显示', options);
  },

  onHide() {
    console.log('📱 管理端隐藏');
  },

  onError(error) {
    console.error('❌ 管理端错误:', error);
  },

  // 初始化应用
  async initApp() {
    try {
      // 获取系统信息
      const systemInfo = await this.getSystemInfo();
      this.globalData.systemInfo = systemInfo;

      // 检查登录状态
      this.checkLoginStatus();

      console.log('✅ 管理端初始化完成');
    } catch (error) {
      console.error('❌ 管理端初始化失败:', error);
    }
  },

  // 获取系统信息
  getSystemInfo() {
    return new Promise((resolve, reject) => {
      wx.getSystemInfo({
        success: resolve,
        fail: reject
      });
    });
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('admin_token');
    const adminInfo = wx.getStorageSync('admin_info');

    if (token && adminInfo) {
      this.globalData.adminInfo = adminInfo;
      console.log('✅ 管理员已登录');
    } else {
      console.log('ℹ️ 管理员未登录');
    }
  },

  // 获取管理员信息
  getAdminInfo() {
    return this.globalData.adminInfo;
  },

  // 设置管理员信息
  setAdminInfo(adminInfo) {
    this.globalData.adminInfo = adminInfo;
    wx.setStorageSync('admin_info', adminInfo);
  },

  // 清除管理员信息
  clearAdminInfo() {
    this.globalData.adminInfo = null;
    wx.removeStorageSync('admin_token');
    wx.removeStorageSync('admin_info');
  }
});
