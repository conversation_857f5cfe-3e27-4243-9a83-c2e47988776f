/**app.wxss**/
/* 管理端全局样式 */

/* 重置样式 */
* {
  box-sizing: border-box;
}

page {
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

/* 管理端主色调 */
:root {
  --primary-color: #6C5CE7;
  --secondary-color: #5A4FCF;
}

/* 通用容器 */
.container {
  padding: 0 20rpx;
}

.page-container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 管理端特色样式 */
.admin-dashboard {
  padding: 20rpx;
}

.admin-header {
  background: linear-gradient(135deg, #6C5CE7 0%, #5A4FCF 100%);
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  color: white;
}

.admin-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.admin-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
}

.admin-details {
  flex: 1;
}

.admin-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.admin-role {
  font-size: 24rpx;
  opacity: 0.8;
}

.system-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-item {
  text-align: center;
}

.status-value {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 4rpx;
}

.status-label {
  font-size: 22rpx;
  opacity: 0.8;
}

.overview-stats {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
}

.stat-card {
  text-align: center;
  padding: 30rpx 20rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background: var(--primary-color);
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #6C5CE7;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
}

.stat-trend {
  font-size: 22rpx;
  color: #28a745;
  margin-top: 8rpx;
}

.quick-management {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 6rpx;
  height: 32rpx;
  background: #6C5CE7;
  border-radius: 3rpx;
  margin-right: 16rpx;
}

.management-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.management-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.management-item:active {
  transform: scale(0.95);
  border-color: #6C5CE7;
  background: rgba(108, 92, 231, 0.1);
}

.management-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 12rpx;
}

.management-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.recent-activities {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.activity-list {
  margin-top: 20rpx;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-radius: 8rpx;
  background: #f8f9fa;
  margin-bottom: 16rpx;
}

.activity-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  background: #6C5CE7;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.activity-desc {
  font-size: 24rpx;
  color: #666;
}

.activity-time {
  font-size: 22rpx;
  color: #999;
}

.system-alerts {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.alert-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-radius: 8rpx;
  background: #fff3cd;
  border-left: 6rpx solid #ffc107;
  margin-bottom: 16rpx;
}

.alert-item.error {
  background: #f8d7da;
  border-left-color: #dc3545;
}

.alert-item.success {
  background: #d4edda;
  border-left-color: #28a745;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.alert-message {
  font-size: 24rpx;
  color: #666;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 20rpx;
  }
  
  .management-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
