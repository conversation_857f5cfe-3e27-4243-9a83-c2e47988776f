// 管理端环境配置
const env = {
  // 开发环境
  development: {
    baseUrl: 'http://localhost:8081/api/admin',
    wsUrl: 'ws://localhost:8081/ws',
    appId: 'wxa09ca7461e763be5',
    debug: true,
    logLevel: 'debug'
  },
  
  // 测试环境
  testing: {
    baseUrl: 'https://test-api.your-domain.com/api/admin',
    wsUrl: 'wss://test-api.your-domain.com/ws',
    appId: 'wxa09ca7461e763be5',
    debug: true,
    logLevel: 'info'
  },
  
  // 生产环境
  production: {
    baseUrl: 'https://api.your-domain.com/api/admin',
    wsUrl: 'wss://api.your-domain.com/ws',
    appId: 'wxa09ca7461e763be5',
    debug: false,
    logLevel: 'error'
  }
};

// 当前环境 - 发布时需要修改为 'production'
const currentEnv = 'development';

// 导出当前环境配置
module.exports = {
  ...env[currentEnv],
  currentEnv,
  version: '1.0.0',
  buildTime: new Date().toISOString()
};
