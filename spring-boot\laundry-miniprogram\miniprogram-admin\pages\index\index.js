// 管理端首页
const app = getApp();

Page({
  data: {
    adminInfo: {},
    systemStatus: {},
    stats: {},
    recentActivities: [],
    alerts: []
  },

  onLoad() {
    this.checkLoginStatus();
  },

  onShow() {
    if (this.data.adminInfo.id) {
      this.loadDashboardData();
    }
  },

  onPullDownRefresh() {
    this.loadDashboardData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 检查登录状态
  checkLoginStatus() {
    const adminInfo = app.getAdminInfo();
    
    if (!adminInfo) {
      // 未登录，跳转到登录页
      wx.redirectTo({
        url: '/pages/login/login'
      });
      return;
    }

    this.setData({ adminInfo });
    this.loadDashboardData();
  },

  // 加载仪表板数据
  async loadDashboardData() {
    try {
      await Promise.all([
        this.loadSystemStatus(),
        this.loadStats(),
        this.loadRecentActivities(),
        this.loadSystemAlerts()
      ]);
    } catch (error) {
      console.error('加载仪表板数据失败:', error);
    }
  },

  // 加载系统状态
  async loadSystemStatus() {
    // 模拟系统状态数据
    const systemStatus = {
      onlineUsers: 1250,
      todayOrders: 89,
      systemLoad: '正常'
    };
    
    this.setData({ systemStatus });
  },

  // 加载统计数据
  async loadStats() {
    // 模拟统计数据
    const stats = {
      totalUsers: 15680,
      totalMerchants: 256,
      totalOrders: 8920,
      totalRevenue: '¥125.8万'
    };
    
    this.setData({ stats });
  },

  // 加载最近活动
  async loadRecentActivities() {
    // 模拟活动数据
    const recentActivities = [
      {
        id: 1,
        icon: 'user-o',
        title: '新用户注册',
        description: '张先生注册成为新用户',
        time: '5分钟前'
      },
      {
        id: 2,
        icon: 'shop-o',
        title: '商家申请',
        description: '专业洗护店申请入驻',
        time: '10分钟前'
      },
      {
        id: 3,
        icon: 'orders-o',
        title: '订单完成',
        description: '订单 #LD202412010001 已完成',
        time: '15分钟前'
      }
    ];
    
    this.setData({ recentActivities });
  },

  // 加载系统警告
  async loadSystemAlerts() {
    // 模拟警告数据
    const alerts = [
      {
        id: 1,
        type: 'warning',
        title: '服务器负载警告',
        message: 'API服务器负载较高，建议关注'
      }
    ];
    
    this.setData({ alerts });
  },

  // 跳转到用户管理
  goToUsers() {
    wx.switchTab({
      url: '/pages/users/users'
    });
  },

  // 跳转到商家管理
  goToMerchants() {
    wx.switchTab({
      url: '/pages/merchants/merchants'
    });
  },

  // 跳转到订单管理
  goToOrders() {
    wx.switchTab({
      url: '/pages/orders/orders'
    });
  },

  // 跳转到财务管理
  goToFinance() {
    wx.navigateTo({
      url: '/pages/finance/finance'
    });
  },

  // 跳转到服务管理
  goToServices() {
    wx.navigateTo({
      url: '/pages/services/services'
    });
  },

  // 跳转到系统设置
  goToSystem() {
    wx.navigateTo({
      url: '/pages/system/system'
    });
  }
});
