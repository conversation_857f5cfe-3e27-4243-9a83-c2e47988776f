<view class="admin-dashboard">
  <!-- 管理员信息 -->
  <view class="admin-header">
    <view class="admin-info">
      <image src="{{ adminInfo.avatar || '/images/default/admin.png' }}" class="admin-avatar" />
      <view class="admin-details">
        <text class="admin-name">{{ adminInfo.name || '管理员' }}</text>
        <text class="admin-role">{{ adminInfo.role || '系统管理员' }}</text>
      </view>
    </view>
    
    <view class="system-status">
      <view class="status-item">
        <text class="status-value">{{ systemStatus.onlineUsers || 0 }}</text>
        <text class="status-label">在线用户</text>
      </view>
      <view class="status-item">
        <text class="status-value">{{ systemStatus.todayOrders || 0 }}</text>
        <text class="status-label">今日订单</text>
      </view>
      <view class="status-item">
        <text class="status-value">{{ systemStatus.systemLoad || '正常' }}</text>
        <text class="status-label">系统负载</text>
      </view>
    </view>
  </view>

  <!-- 数据概览 -->
  <view class="overview-stats">
    <view class="section-title">数据概览</view>
    <view class="stats-grid">
      <view class="stat-card">
        <text class="stat-number">{{ stats.totalUsers || 0 }}</text>
        <text class="stat-label">总用户数</text>
        <text class="stat-trend">+12%</text>
      </view>
      <view class="stat-card">
        <text class="stat-number">{{ stats.totalMerchants || 0 }}</text>
        <text class="stat-label">商家数量</text>
        <text class="stat-trend">+8%</text>
      </view>
      <view class="stat-card">
        <text class="stat-number">{{ stats.totalOrders || 0 }}</text>
        <text class="stat-label">总订单数</text>
        <text class="stat-trend">+15%</text>
      </view>
      <view class="stat-card">
        <text class="stat-number">{{ stats.totalRevenue || 0 }}</text>
        <text class="stat-label">总收入</text>
        <text class="stat-trend">+20%</text>
      </view>
    </view>
  </view>

  <!-- 快捷管理 -->
  <view class="quick-management">
    <view class="section-title">快捷管理</view>
    <view class="management-grid">
      <view class="management-item" bind:tap="goToUsers">
        <van-icon name="friends-o" size="24" color="#6C5CE7" />
        <text class="management-text">用户管理</text>
      </view>
      <view class="management-item" bind:tap="goToMerchants">
        <van-icon name="shop-o" size="24" color="#6C5CE7" />
        <text class="management-text">商家管理</text>
      </view>
      <view class="management-item" bind:tap="goToOrders">
        <van-icon name="orders-o" size="24" color="#6C5CE7" />
        <text class="management-text">订单管理</text>
      </view>
      <view class="management-item" bind:tap="goToFinance">
        <van-icon name="balance-o" size="24" color="#6C5CE7" />
        <text class="management-text">财务管理</text>
      </view>
      <view class="management-item" bind:tap="goToServices">
        <van-icon name="goods-collect-o" size="24" color="#6C5CE7" />
        <text class="management-text">服务管理</text>
      </view>
      <view class="management-item" bind:tap="goToSystem">
        <van-icon name="setting-o" size="24" color="#6C5CE7" />
        <text class="management-text">系统设置</text>
      </view>
    </view>
  </view>

  <!-- 最近活动 -->
  <view class="recent-activities">
    <view class="section-title">最近活动</view>
    <view class="activity-list">
      <view 
        class="activity-item" 
        wx:for="{{ recentActivities }}" 
        wx:key="id"
      >
        <view class="activity-icon">
          <van-icon name="{{ item.icon }}" size="16" color="white" />
        </view>
        <view class="activity-content">
          <text class="activity-title">{{ item.title }}</text>
          <text class="activity-desc">{{ item.description }}</text>
        </view>
        <text class="activity-time">{{ item.time }}</text>
      </view>
    </view>
  </view>

  <!-- 系统警告 -->
  <view class="system-alerts" wx:if="{{ alerts.length > 0 }}">
    <view class="section-title">系统警告</view>
    <view 
      class="alert-item {{ item.type }}" 
      wx:for="{{ alerts }}" 
      wx:key="id"
    >
      <view class="alert-content">
        <text class="alert-title">{{ item.title }}</text>
        <text class="alert-message">{{ item.message }}</text>
      </view>
    </view>
  </view>
</view>

<van-toast id="van-toast" />
