// 商家端小程序入口文件
const env = require('./config/env');

App({
  globalData: {
    userInfo: null,
    systemInfo: null,
    merchantInfo: null,
    appId: 'wxa09ca7461e763be5', // 商家端使用相同AppID
    version: '1.0.0',
    environment: env.currentEnv || 'development'
  },

  onLaunch(options) {
    console.log('🏪 商家端小程序启动', options);
    this.initApp();
  },

  onShow(options) {
    console.log('📱 商家端显示', options);
  },

  onHide() {
    console.log('📱 商家端隐藏');
  },

  onError(error) {
    console.error('❌ 商家端错误:', error);
  },

  // 初始化应用
  async initApp() {
    try {
      // 获取系统信息
      const systemInfo = await this.getSystemInfo();
      this.globalData.systemInfo = systemInfo;

      // 检查登录状态
      this.checkLoginStatus();

      console.log('✅ 商家端初始化完成');
    } catch (error) {
      console.error('❌ 商家端初始化失败:', error);
    }
  },

  // 获取系统信息
  getSystemInfo() {
    return new Promise((resolve, reject) => {
      wx.getSystemInfo({
        success: resolve,
        fail: reject
      });
    });
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('merchant_token');
    const merchantInfo = wx.getStorageSync('merchant_info');

    if (token && merchantInfo) {
      this.globalData.merchantInfo = merchantInfo;
      console.log('✅ 商家已登录');
    } else {
      console.log('ℹ️ 商家未登录');
    }
  },

  // 获取商家信息
  getMerchantInfo() {
    return this.globalData.merchantInfo;
  },

  // 设置商家信息
  setMerchantInfo(merchantInfo) {
    this.globalData.merchantInfo = merchantInfo;
    wx.setStorageSync('merchant_info', merchantInfo);
  },

  // 清除商家信息
  clearMerchantInfo() {
    this.globalData.merchantInfo = null;
    wx.removeStorageSync('merchant_token');
    wx.removeStorageSync('merchant_info');
  }
});
