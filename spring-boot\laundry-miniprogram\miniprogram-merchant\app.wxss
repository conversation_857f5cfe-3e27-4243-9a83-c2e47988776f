/**app.wxss**/
/* 商家端全局样式 */

/* 重置样式 */
* {
  box-sizing: border-box;
}

page {
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

/* 商家端主色调 */
:root {
  --primary-color: #FF6B35;
  --secondary-color: #E55A2B;
}

/* 通用容器 */
.container {
  padding: 0 20rpx;
}

.page-container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 商家端特色样式 */
.merchant-home {
  padding: 20rpx;
}

.merchant-info-card {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.merchant-header {
  display: flex;
  align-items: center;
}

.merchant-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
}

.merchant-details {
  flex: 1;
}

.merchant-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.merchant-status {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.rating-text {
  font-size: 24rpx;
  color: #666;
}

.stats-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx;
  border-radius: 8rpx;
  background: #f8f9fa;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #FF6B35;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.more-link {
  font-size: 26rpx;
  color: #FF6B35;
}

.quick-actions {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 8rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.action-item:active {
  transform: scale(0.95);
  background: #e9ecef;
}

.action-item text {
  font-size: 24rpx;
  color: #333;
  margin-top: 8rpx;
}

.pending-orders,
.recent-messages {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.order-list,
.message-list {
  margin-top: 20rpx;
}

.order-item,
.message-item {
  padding: 20rpx;
  border-radius: 8rpx;
  background: #f8f9fa;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}

.order-item:active,
.message-item:active {
  transform: scale(0.98);
  background: #e9ecef;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.order-number {
  font-size: 26rpx;
  color: #666;
}

.order-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.service-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.order-amount {
  font-size: 28rpx;
  color: #FF6B35;
  font-weight: bold;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.message-content {
  margin-bottom: 8rpx;
}

.message-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.message-preview {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.message-time {
  font-size: 24rpx;
  color: #999;
}

.empty-state {
  padding: 100rpx 20rpx;
  text-align: center;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
  }
  
  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
