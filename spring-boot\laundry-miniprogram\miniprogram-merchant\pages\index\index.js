// 商家端首页
const app = getApp();

Page({
  data: {
    merchantInfo: {},
    todayStats: {},
    serviceStats: {},
    reviewStats: {},
    pendingOrders: [],
    recentMessages: [],
    hasData: false,
    loading: true
  },

  onLoad() {
    this.checkLoginStatus();
  },

  onShow() {
    if (this.data.merchantInfo.id) {
      this.loadPageData();
    }
  },

  onPullDownRefresh() {
    this.loadPageData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 检查登录状态
  checkLoginStatus() {
    const merchantInfo = app.getMerchantInfo();
    
    if (!merchantInfo) {
      // 未登录，跳转到登录页
      wx.redirectTo({
        url: '/pages/login/login'
      });
      return;
    }

    this.setData({ merchantInfo });
    this.loadPageData();
  },

  // 加载页面数据
  async loadPageData() {
    this.setData({ loading: true });

    try {
      await Promise.all([
        this.loadTodayStats(),
        this.loadServiceStats(),
        this.loadReviewStats(),
        this.loadPendingOrders(),
        this.loadRecentMessages()
      ]);

      this.setData({ 
        hasData: true,
        loading: false 
      });
    } catch (error) {
      console.error('加载页面数据失败:', error);
      this.setData({ loading: false });
    }
  },

  // 加载今日统计
  async loadTodayStats() {
    try {
      // 模拟数据
      const todayStats = {
        orderCount: 12,
        revenue: 1580.50
      };
      
      this.setData({ todayStats });
    } catch (error) {
      console.error('加载今日统计失败:', error);
    }
  },

  // 加载服务统计
  async loadServiceStats() {
    try {
      // 模拟数据
      const serviceStats = {
        totalServices: 25,
        activeServices: 20
      };
      
      this.setData({ serviceStats });
    } catch (error) {
      console.error('加载服务统计失败:', error);
    }
  },

  // 加载评价统计
  async loadReviewStats() {
    try {
      // 模拟数据
      const reviewStats = {
        totalReviews: 156,
        averageRating: 4.8
      };
      
      this.setData({ reviewStats });
    } catch (error) {
      console.error('加载评价统计失败:', error);
    }
  },

  // 加载待处理订单
  async loadPendingOrders() {
    try {
      // 模拟数据
      const pendingOrders = [
        {
          id: 1,
          orderNumber: 'LD202412010001',
          serviceName: '衣物精洗',
          amount: 35.00,
          statusText: '待确认',
          createdAt: '2024-12-01 10:30'
        },
        {
          id: 2,
          orderNumber: 'LD202412010002',
          serviceName: '鞋类护理',
          amount: 25.00,
          statusText: '待取件',
          createdAt: '2024-12-01 11:15'
        }
      ];
      
      this.setData({ pendingOrders });
    } catch (error) {
      console.error('加载待处理订单失败:', error);
    }
  },

  // 加载最新消息
  async loadRecentMessages() {
    try {
      // 模拟数据
      const recentMessages = [
        {
          id: 1,
          title: '系统通知',
          content: '您有新的订单需要处理',
          createdAt: '10:30'
        },
        {
          id: 2,
          title: '用户评价',
          content: '用户对您的服务给出了5星好评',
          createdAt: '09:45'
        }
      ];
      
      this.setData({ recentMessages });
    } catch (error) {
      console.error('加载最新消息失败:', error);
    }
  },

  // 添加服务
  addService() {
    wx.navigateTo({
      url: '/pages/service-edit/service-edit'
    });
  },

  // 跳转到订单管理
  goToOrders() {
    wx.switchTab({
      url: '/pages/orders/orders'
    });
  },

  // 跳转到财务管理
  goToFinance() {
    wx.switchTab({
      url: '/pages/finance/finance'
    });
  },

  // 跳转到服务管理
  goToServices() {
    wx.switchTab({
      url: '/pages/services/services'
    });
  },

  // 跳转到评价管理
  goToReviews() {
    wx.navigateTo({
      url: '/pages/reviews/reviews'
    });
  },

  // 跳转到消息中心
  goToMessages() {
    wx.navigateTo({
      url: '/pages/messages/messages'
    });
  },

  // 跳转到设置
  goToSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  },

  // 跳转到订单详情
  goToOrderDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?id=${id}`
    });
  },

  // 跳转到消息详情
  goToMessageDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/message-detail/message-detail?id=${id}`
    });
  }
});
