<view class="merchant-home">
  <!-- 商家信息卡片 -->
  <view class="merchant-info-card">
    <view class="merchant-header">
      <image src="{{ merchantInfo.avatar || '/images/default/merchant.png' }}" class="merchant-avatar" />
      <view class="merchant-details">
        <text class="merchant-name">{{ merchantInfo.shopName || '未设置店铺名称' }}</text>
        <view class="merchant-status">
          <van-tag type="{{ merchantInfo.status === 'ACTIVE' ? 'success' : 'warning' }}">
            {{ merchantInfo.status === 'ACTIVE' ? '营业中' : '休息中' }}
          </van-tag>
          <van-rate value="{{ merchantInfo.rating || 5 }}" size="12" readonly />
          <text class="rating-text">{{ merchantInfo.rating || '5.0' }}</text>
        </view>
      </view>
      <van-icon name="setting-o" size="20" bind:click="goToSettings" />
    </view>
  </view>

  <!-- 数据统计 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stat-item" bind:tap="goToOrders">
        <text class="stat-number">{{ todayStats.orderCount || 0 }}</text>
        <text class="stat-label">今日订单</text>
      </view>
      <view class="stat-item" bind:tap="goToFinance">
        <text class="stat-number">¥{{ todayStats.revenue || 0 }}</text>
        <text class="stat-label">今日收入</text>
      </view>
      <view class="stat-item" bind:tap="goToServices">
        <text class="stat-number">{{ serviceStats.totalServices || 0 }}</text>
        <text class="stat-label">服务数量</text>
      </view>
      <view class="stat-item" bind:tap="goToReviews">
        <text class="stat-number">{{ reviewStats.totalReviews || 0 }}</text>
        <text class="stat-label">评价数量</text>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <view class="section-title">快捷操作</view>
    <view class="actions-grid">
      <view class="action-item" bind:tap="addService">
        <van-icon name="plus" size="24" color="#FF6B35" />
        <text>添加服务</text>
      </view>
      <view class="action-item" bind:tap="goToOrders">
        <van-icon name="orders-o" size="24" color="#FF6B35" />
        <text>订单管理</text>
      </view>
      <view class="action-item" bind:tap="goToFinance">
        <van-icon name="balance-o" size="24" color="#FF6B35" />
        <text>财务管理</text>
      </view>
      <view class="action-item" bind:tap="goToMessages">
        <van-icon name="chat-o" size="24" color="#FF6B35" />
        <text>消息中心</text>
      </view>
    </view>
  </view>

  <!-- 待处理订单 -->
  <view class="pending-orders" wx:if="{{ pendingOrders.length > 0 }}">
    <view class="section-title">
      <text>待处理订单</text>
      <text class="more-link" bind:tap="goToOrders">查看全部</text>
    </view>
    <view class="order-list">
      <view 
        class="order-item" 
        wx:for="{{ pendingOrders }}" 
        wx:key="id"
        bind:tap="goToOrderDetail"
        data-id="{{ item.id }}"
      >
        <view class="order-header">
          <text class="order-number">订单号：{{ item.orderNumber }}</text>
          <van-tag type="warning">{{ item.statusText }}</van-tag>
        </view>
        <view class="order-content">
          <text class="service-name">{{ item.serviceName }}</text>
          <text class="order-amount">¥{{ item.amount }}</text>
        </view>
        <view class="order-time">{{ item.createdAt }}</view>
      </view>
    </view>
  </view>

  <!-- 最新消息 -->
  <view class="recent-messages" wx:if="{{ recentMessages.length > 0 }}">
    <view class="section-title">
      <text>最新消息</text>
      <text class="more-link" bind:tap="goToMessages">查看全部</text>
    </view>
    <view class="message-list">
      <view 
        class="message-item" 
        wx:for="{{ recentMessages }}" 
        wx:key="id"
        bind:tap="goToMessageDetail"
        data-id="{{ item.id }}"
      >
        <view class="message-content">
          <text class="message-title">{{ item.title }}</text>
          <text class="message-preview">{{ item.content }}</text>
        </view>
        <view class="message-time">{{ item.createdAt }}</view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{ !hasData }}">
    <van-empty description="暂无数据" />
  </view>
</view>

<van-toast id="van-toast" />
