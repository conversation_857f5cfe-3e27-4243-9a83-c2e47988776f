// 商家端订单管理页面
const app = getApp();

Page({
  data: {
    activeTab: 'all',
    orders: [],
    loading: true
  },

  onLoad() {
    this.loadOrders();
  },

  onShow() {
    this.loadOrders();
  },

  onPullDownRefresh() {
    this.loadOrders().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 切换标签
  onTabChange(event) {
    const { name } = event.detail;
    this.setData({ activeTab: name });
    this.loadOrders();
  },

  // 加载订单列表
  async loadOrders() {
    this.setData({ loading: true });

    try {
      // 模拟订单数据
      const orders = this.getMockOrders();
      const filteredOrders = this.filterOrdersByStatus(orders);
      
      this.setData({ 
        orders: filteredOrders,
        loading: false 
      });
    } catch (error) {
      console.error('加载订单失败:', error);
      this.setData({ loading: false });
    }
  },

  // 获取模拟订单数据
  getMockOrders() {
    return [
      {
        id: 1,
        orderNumber: 'LD202412010001',
        customerName: '张先生',
        customerPhone: '138****8888',
        serviceName: '衣物精洗',
        quantity: 3,
        amount: 105.00,
        status: 'PENDING',
        statusText: '待确认',
        statusType: 'warning',
        createdAt: '2024-12-01 10:30',
        actions: [
          { action: 'confirm', text: '确认订单', type: 'primary' },
          { action: 'reject', text: '拒绝', type: 'default' }
        ]
      },
      {
        id: 2,
        orderNumber: 'LD202412010002',
        customerName: '李女士',
        customerPhone: '139****9999',
        serviceName: '鞋类护理',
        quantity: 2,
        amount: 50.00,
        status: 'PROCESSING',
        statusText: '进行中',
        statusType: 'primary',
        createdAt: '2024-12-01 09:15',
        actions: [
          { action: 'complete', text: '完成服务', type: 'primary' }
        ]
      },
      {
        id: 3,
        orderNumber: 'LD202411300003',
        customerName: '王先生',
        customerPhone: '137****7777',
        serviceName: '汽车内饰清洁',
        quantity: 1,
        amount: 200.00,
        status: 'COMPLETED',
        statusText: '已完成',
        statusType: 'success',
        createdAt: '2024-11-30 16:45',
        actions: []
      }
    ];
  },

  // 根据状态筛选订单
  filterOrdersByStatus(orders) {
    const { activeTab } = this.data;
    
    if (activeTab === 'all') {
      return orders;
    }
    
    const statusMap = {
      'pending': 'PENDING',
      'processing': 'PROCESSING',
      'completed': 'COMPLETED'
    };
    
    return orders.filter(order => order.status === statusMap[activeTab]);
  },

  // 处理订单操作
  async handleOrderAction(e) {
    const { orderId, action } = e.currentTarget.dataset;
    
    switch (action) {
      case 'confirm':
        await this.confirmOrder(orderId);
        break;
      case 'reject':
        await this.rejectOrder(orderId);
        break;
      case 'complete':
        await this.completeOrder(orderId);
        break;
    }
  },

  // 确认订单
  async confirmOrder(orderId) {
    try {
      // 调用确认订单API
      console.log('确认订单:', orderId);
      
      wx.showToast({
        title: '订单已确认',
        icon: 'success'
      });
      
      this.loadOrders();
    } catch (error) {
      console.error('确认订单失败:', error);
      wx.showToast({
        title: '确认失败',
        icon: 'error'
      });
    }
  },

  // 拒绝订单
  async rejectOrder(orderId) {
    try {
      const result = await new Promise((resolve) => {
        wx.showModal({
          title: '确认拒绝',
          content: '确定要拒绝这个订单吗？',
          success: resolve
        });
      });

      if (result.confirm) {
        // 调用拒绝订单API
        console.log('拒绝订单:', orderId);
        
        wx.showToast({
          title: '订单已拒绝',
          icon: 'success'
        });
        
        this.loadOrders();
      }
    } catch (error) {
      console.error('拒绝订单失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      });
    }
  },

  // 完成订单
  async completeOrder(orderId) {
    try {
      // 调用完成订单API
      console.log('完成订单:', orderId);
      
      wx.showToast({
        title: '订单已完成',
        icon: 'success'
      });
      
      this.loadOrders();
    } catch (error) {
      console.error('完成订单失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      });
    }
  },

  // 跳转到订单详情
  goToOrderDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?id=${id}`
    });
  }
});
