<view class="merchant-orders">
  <!-- 订单状态筛选 -->
  <view class="order-tabs">
    <van-tabs active="{{ activeTab }}" bind:change="onTabChange" color="#FF6B35">
      <van-tab title="全部" name="all"></van-tab>
      <van-tab title="待确认" name="pending"></van-tab>
      <van-tab title="进行中" name="processing"></van-tab>
      <van-tab title="已完成" name="completed"></van-tab>
    </van-tabs>
  </view>

  <!-- 订单列表 -->
  <view class="orders-list" wx:if="{{ orders.length > 0 }}">
    <view 
      class="order-item" 
      wx:for="{{ orders }}" 
      wx:key="id"
      bind:tap="goToOrderDetail"
      data-id="{{ item.id }}"
    >
      <view class="order-header">
        <text class="order-number">{{ item.orderNumber }}</text>
        <van-tag type="{{ item.statusType }}">{{ item.statusText }}</van-tag>
      </view>
      
      <view class="order-content">
        <view class="customer-info">
          <text class="customer-name">客户：{{ item.customerName }}</text>
          <text class="customer-phone">{{ item.customerPhone }}</text>
        </view>
        <view class="service-info">
          <text class="service-name">{{ item.serviceName }}</text>
          <text class="service-count">数量：{{ item.quantity }}</text>
        </view>
        <view class="order-amount">
          <text class="amount-text">¥{{ item.amount }}</text>
        </view>
      </view>
      
      <view class="order-actions" wx:if="{{ item.actions.length > 0 }}">
        <van-button 
          wx:for="{{ item.actions }}" 
          wx:key="type"
          wx:for-item="action"
          size="small" 
          type="{{ action.type }}"
          bind:click="handleOrderAction"
          data-order-id="{{ item.id }}"
          data-action="{{ action.action }}"
        >
          {{ action.text }}
        </van-button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <van-empty description="暂无订单" />
  </view>
</view>

<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
