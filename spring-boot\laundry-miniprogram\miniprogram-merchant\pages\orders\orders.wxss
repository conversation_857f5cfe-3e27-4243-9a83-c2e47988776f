/* 商家端订单管理页面样式 */
.merchant-orders {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.order-tabs {
  background: white;
  position: sticky;
  top: 0;
  z-index: 100;
}

.orders-list {
  padding: 20rpx;
}

.order-item {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-number {
  font-size: 26rpx;
  color: #666;
}

.order-content {
  margin-bottom: 20rpx;
}

.customer-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.customer-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.customer-phone {
  font-size: 24rpx;
  color: #666;
}

.service-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.service-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.service-count {
  font-size: 24rpx;
  color: #666;
}

.order-amount {
  text-align: right;
}

.amount-text {
  font-size: 32rpx;
  color: #FF6B35;
  font-weight: bold;
}

.order-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.order-actions .van-button {
  min-width: 120rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-state .van-button {
  margin-top: 40rpx;
  width: 200rpx;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .orders-list {
    padding: 15rpx;
  }
  
  .order-item {
    padding: 20rpx;
  }
  
  .order-actions {
    flex-direction: column;
    gap: 12rpx;
  }
  
  .order-actions .van-button {
    width: 100%;
  }
}
