// 商家端个人中心页面
const app = getApp();

Page({
  data: {
    merchantInfo: {},
    stats: {}
  },

  onLoad() {
    this.loadMerchantInfo();
    this.loadStats();
  },

  onShow() {
    this.loadMerchantInfo();
  },

  // 加载商家信息
  loadMerchantInfo() {
    const merchantInfo = app.getMerchantInfo() || {
      shopName: '专业洗护店',
      avatar: '/images/default/merchant.png',
      status: 'ACTIVE',
      rating: 4.8
    };
    
    this.setData({ merchantInfo });
  },

  // 加载统计数据
  loadStats() {
    // 模拟统计数据
    const stats = {
      totalOrders: 156,
      totalRevenue: '12,580',
      totalServices: 25
    };
    
    this.setData({ stats });
  },

  // 编辑个人资料
  editProfile() {
    wx.navigateTo({
      url: '/pages/profile-edit/profile-edit'
    });
  },

  // 跳转到店铺设置
  goToShopSettings() {
    wx.navigateTo({
      url: '/pages/shop-settings/shop-settings'
    });
  },

  // 跳转到服务管理
  goToServices() {
    wx.switchTab({
      url: '/pages/services/services'
    });
  },

  // 跳转到财务管理
  goToFinance() {
    wx.navigateTo({
      url: '/pages/finance/finance'
    });
  },

  // 跳转到数据统计
  goToStatistics() {
    wx.navigateTo({
      url: '/pages/statistics/statistics'
    });
  },

  // 跳转到消息中心
  goToMessages() {
    wx.navigateTo({
      url: '/pages/messages/messages'
    });
  },

  // 跳转到客户评价
  goToReviews() {
    wx.navigateTo({
      url: '/pages/reviews/reviews'
    });
  },

  // 跳转到帮助中心
  goToHelp() {
    wx.navigateTo({
      url: '/pages/help/help'
    });
  },

  // 跳转到意见反馈
  goToFeedback() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    });
  },

  // 跳转到系统设置
  goToSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  },

  // 跳转到关于我们
  goToAbout() {
    wx.navigateTo({
      url: '/pages/about/about'
    });
  },

  // 退出登录
  async logout() {
    try {
      const result = await new Promise((resolve) => {
        wx.showModal({
          title: '确认退出',
          content: '确定要退出登录吗？',
          success: resolve
        });
      });

      if (result.confirm) {
        // 清除登录信息
        app.clearMerchantInfo();
        
        wx.showToast({
          title: '已退出登录',
          icon: 'success'
        });

        // 跳转到登录页
        setTimeout(() => {
          wx.redirectTo({
            url: '/pages/login/login'
          });
        }, 1500);
      }
    } catch (error) {
      console.error('退出登录失败:', error);
      wx.showToast({
        title: '退出失败',
        icon: 'error'
      });
    }
  }
});
