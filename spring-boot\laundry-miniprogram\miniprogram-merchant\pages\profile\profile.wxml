<view class="merchant-profile">
  <!-- 商家信息卡片 -->
  <view class="merchant-card">
    <view class="merchant-header">
      <image src="{{ merchantInfo.avatar || '/images/default/merchant.png' }}" class="merchant-avatar" />
      <view class="merchant-info">
        <text class="merchant-name">{{ merchantInfo.shopName || '未设置店铺名称' }}</text>
        <view class="merchant-status">
          <van-tag type="{{ merchantInfo.status === 'ACTIVE' ? 'success' : 'warning' }}">
            {{ merchantInfo.status === 'ACTIVE' ? '营业中' : '休息中' }}
          </van-tag>
          <van-rate value="{{ merchantInfo.rating || 5 }}" size="12" readonly />
        </view>
      </view>
      <van-icon name="edit" size="20" bind:click="editProfile" />
    </view>
    
    <view class="merchant-stats">
      <view class="stat-item">
        <text class="stat-number">{{ stats.totalOrders || 0 }}</text>
        <text class="stat-label">总订单</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ stats.totalRevenue || 0 }}</text>
        <text class="stat-label">总收入</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ stats.totalServices || 0 }}</text>
        <text class="stat-label">服务数</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <van-cell-group>
      <van-cell 
        title="店铺设置" 
        icon="shop-o" 
        is-link 
        bind:click="goToShopSettings"
      />
      <van-cell 
        title="服务管理" 
        icon="goods-collect-o" 
        is-link 
        bind:click="goToServices"
      />
      <van-cell 
        title="财务管理" 
        icon="balance-o" 
        is-link 
        bind:click="goToFinance"
      />
      <van-cell 
        title="数据统计" 
        icon="chart-trending-o" 
        is-link 
        bind:click="goToStatistics"
      />
    </van-cell-group>
  </view>

  <view class="menu-section">
    <van-cell-group>
      <van-cell 
        title="消息中心" 
        icon="chat-o" 
        is-link 
        bind:click="goToMessages"
      />
      <van-cell 
        title="客户评价" 
        icon="star-o" 
        is-link 
        bind:click="goToReviews"
      />
      <van-cell 
        title="帮助中心" 
        icon="question-o" 
        is-link 
        bind:click="goToHelp"
      />
      <van-cell 
        title="意见反馈" 
        icon="edit" 
        is-link 
        bind:click="goToFeedback"
      />
    </van-cell-group>
  </view>

  <view class="menu-section">
    <van-cell-group>
      <van-cell 
        title="系统设置" 
        icon="setting-o" 
        is-link 
        bind:click="goToSettings"
      />
      <van-cell 
        title="关于我们" 
        icon="info-o" 
        is-link 
        bind:click="goToAbout"
      />
    </van-cell-group>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section">
    <van-button type="danger" size="large" bind:click="logout">
      退出登录
    </van-button>
  </view>
</view>

<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
