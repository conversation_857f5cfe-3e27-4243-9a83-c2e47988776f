// 商家端服务管理页面
const app = getApp();

Page({
  data: {
    services: [],
    loading: true
  },

  onLoad() {
    this.loadServices();
  },

  onShow() {
    this.loadServices();
  },

  onPullDownRefresh() {
    this.loadServices().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载服务列表
  async loadServices() {
    this.setData({ loading: true });

    try {
      // 模拟服务数据
      const services = this.getMockServices();
      
      this.setData({ 
        services,
        loading: false 
      });
    } catch (error) {
      console.error('加载服务失败:', error);
      this.setData({ loading: false });
    }
  },

  // 获取模拟服务数据
  getMockServices() {
    return [
      {
        id: 1,
        name: '衣物精洗',
        description: '专业衣物清洗，去污除菌',
        price: 35.00,
        unit: '件',
        image: '/images/services/clothes.jpg',
        status: 'ACTIVE'
      },
      {
        id: 2,
        name: '鞋类护理',
        description: '专业鞋类清洁护理',
        price: 25.00,
        unit: '双',
        image: '/images/services/shoes.jpg',
        status: 'ACTIVE'
      },
      {
        id: 3,
        name: '汽车内饰清洁',
        description: '汽车内饰深度清洁',
        price: 200.00,
        unit: '次',
        image: '/images/services/car.jpg',
        status: 'INACTIVE'
      }
    ];
  },

  // 添加服务
  addService() {
    wx.navigateTo({
      url: '/pages/service-edit/service-edit'
    });
  },

  // 编辑服务
  editService(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/service-edit/service-edit?id=${id}`
    });
  },

  // 切换服务状态
  async toggleServiceStatus(e) {
    const { id, status } = e.currentTarget.dataset;
    const newStatus = status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE';
    const actionText = newStatus === 'ACTIVE' ? '上架' : '下架';

    try {
      const result = await new Promise((resolve) => {
        wx.showModal({
          title: '确认操作',
          content: `确定要${actionText}这个服务吗？`,
          success: resolve
        });
      });

      if (result.confirm) {
        // 调用切换状态API
        console.log('切换服务状态:', id, newStatus);
        
        // 更新本地数据
        const services = this.data.services.map(service => {
          if (service.id === id) {
            return { ...service, status: newStatus };
          }
          return service;
        });
        
        this.setData({ services });
        
        wx.showToast({
          title: `${actionText}成功`,
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('切换服务状态失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      });
    }
  },

  // 删除服务
  async deleteService(e) {
    const { id } = e.currentTarget.dataset;

    try {
      const result = await new Promise((resolve) => {
        wx.showModal({
          title: '确认删除',
          content: '确定要删除这个服务吗？删除后无法恢复。',
          confirmColor: '#ff4757',
          success: resolve
        });
      });

      if (result.confirm) {
        // 调用删除服务API
        console.log('删除服务:', id);
        
        // 更新本地数据
        const services = this.data.services.filter(service => service.id !== id);
        this.setData({ services });
        
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('删除服务失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      });
    }
  }
});
