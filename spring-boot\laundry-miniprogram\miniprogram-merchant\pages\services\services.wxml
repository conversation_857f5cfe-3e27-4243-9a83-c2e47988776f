<view class="merchant-services">
  <!-- 顶部操作栏 -->
  <view class="top-actions">
    <van-button type="primary" size="large" bind:click="addService">
      <van-icon name="plus" size="16" />
      添加服务
    </van-button>
  </view>

  <!-- 服务列表 -->
  <view class="services-list" wx:if="{{ services.length > 0 }}">
    <view 
      class="service-item" 
      wx:for="{{ services }}" 
      wx:key="id"
    >
      <view class="service-content">
        <image src="{{ item.image }}" class="service-image" />
        <view class="service-info">
          <text class="service-name">{{ item.name }}</text>
          <text class="service-desc">{{ item.description }}</text>
          <view class="service-price">
            <text class="price-text">¥{{ item.price }}</text>
            <text class="price-unit">/{{ item.unit }}</text>
          </view>
        </view>
        <view class="service-status">
          <van-tag type="{{ item.status === 'ACTIVE' ? 'success' : 'default' }}">
            {{ item.status === 'ACTIVE' ? '上架中' : '已下架' }}
          </van-tag>
        </view>
      </view>
      
      <view class="service-actions">
        <van-button 
          size="small" 
          type="default"
          bind:click="editService"
          data-id="{{ item.id }}"
        >
          编辑
        </van-button>
        <van-button 
          size="small" 
          type="{{ item.status === 'ACTIVE' ? 'warning' : 'primary' }}"
          bind:click="toggleServiceStatus"
          data-id="{{ item.id }}"
          data-status="{{ item.status }}"
        >
          {{ item.status === 'ACTIVE' ? '下架' : '上架' }}
        </van-button>
        <van-button 
          size="small" 
          type="danger"
          bind:click="deleteService"
          data-id="{{ item.id }}"
        >
          删除
        </van-button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <van-empty description="暂无服务" />
    <van-button type="primary" size="large" bind:click="addService">
      添加第一个服务
    </van-button>
  </view>
</view>

<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
