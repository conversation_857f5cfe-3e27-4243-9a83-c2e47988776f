package com.example.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class ChatWebSocketHandler implements WebSocketHandler {

    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 存储WebSocket会话
    private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    
    // 存储用户ID与会话的映射
    private final Map<String, String> userSessions = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = session.getId();
        String userId = getUserIdFromSession(session);
        
        sessions.put(sessionId, session);
        if (userId != null) {
            userSessions.put(userId, sessionId);
        }
        
        System.out.println("WebSocket连接建立: " + sessionId + ", 用户ID: " + userId);
        
        // 发送连接成功消息
        sendMessage(session, Map.of(
            "type", "connection",
            "status", "connected",
            "message", "连接成功"
        ));
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        String payload = message.getPayload().toString();
        System.out.println("收到消息: " + payload);
        
        try {
            Map<String, Object> messageData = objectMapper.readValue(payload, Map.class);
            String messageType = (String) messageData.get("type");
            
            switch (messageType) {
                case "text":
                case "image":
                    handleChatMessage(session, messageData);
                    break;
                case "ping":
                    handlePing(session);
                    break;
                default:
                    System.out.println("未知消息类型: " + messageType);
            }
            
        } catch (Exception e) {
            System.err.println("处理消息失败: " + e.getMessage());
            sendMessage(session, Map.of(
                "type", "error",
                "message", "消息处理失败"
            ));
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        System.err.println("WebSocket传输错误: " + exception.getMessage());
        exception.printStackTrace();
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String sessionId = session.getId();
        String userId = getUserIdFromSession(session);
        
        sessions.remove(sessionId);
        if (userId != null) {
            userSessions.remove(userId);
        }
        
        System.out.println("WebSocket连接关闭: " + sessionId + ", 状态: " + closeStatus);
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 处理聊天消息
     */
    private void handleChatMessage(WebSocketSession session, Map<String, Object> messageData) {
        try {
            String senderId = (String) messageData.get("senderId");
            String senderType = (String) messageData.get("senderType");
            String merchantId = (String) messageData.get("merchantId");
            String content = (String) messageData.get("content");
            String type = (String) messageData.get("type");
            
            // 构建消息对象
            Map<String, Object> chatMessage = Map.of(
                "id", System.currentTimeMillis(),
                "type", type,
                "content", content,
                "senderId", senderId,
                "senderType", senderType,
                "merchantId", merchantId,
                "timestamp", java.time.LocalDateTime.now().toString(),
                "status", "delivered"
            );
            
            // 转发消息给相关用户
            forwardMessage(senderId, senderType, merchantId, chatMessage);
            
            // 确认消息已发送
            sendMessage(session, Map.of(
                "type", "message_ack",
                "messageId", chatMessage.get("id"),
                "status", "sent"
            ));
            
        } catch (Exception e) {
            System.err.println("处理聊天消息失败: " + e.getMessage());
        }
    }

    /**
     * 处理心跳消息
     */
    private void handlePing(WebSocketSession session) {
        try {
            sendMessage(session, Map.of(
                "type", "pong",
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            System.err.println("处理心跳失败: " + e.getMessage());
        }
    }

    /**
     * 转发消息
     */
    private void forwardMessage(String senderId, String senderType, String merchantId, Map<String, Object> message) {
        try {
            // 根据发送者类型决定转发目标
            if ("USER".equals(senderType)) {
                // 用户发送的消息，转发给商家
                String merchantSessionId = userSessions.get("merchant_" + merchantId);
                if (merchantSessionId != null) {
                    WebSocketSession merchantSession = sessions.get(merchantSessionId);
                    if (merchantSession != null && merchantSession.isOpen()) {
                        sendMessage(merchantSession, message);
                    }
                }
            } else if ("MERCHANT".equals(senderType)) {
                // 商家发送的消息，转发给用户
                String userId = (String) message.get("targetUserId");
                if (userId != null) {
                    String userSessionId = userSessions.get("user_" + userId);
                    if (userSessionId != null) {
                        WebSocketSession userSession = sessions.get(userSessionId);
                        if (userSession != null && userSession.isOpen()) {
                            sendMessage(userSession, message);
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("转发消息失败: " + e.getMessage());
        }
    }

    /**
     * 发送消息
     */
    private void sendMessage(WebSocketSession session, Object message) {
        try {
            if (session.isOpen()) {
                String jsonMessage = objectMapper.writeValueAsString(message);
                session.sendMessage(new TextMessage(jsonMessage));
            }
        } catch (IOException e) {
            System.err.println("发送消息失败: " + e.getMessage());
        }
    }

    /**
     * 从会话中获取用户ID
     */
    private String getUserIdFromSession(WebSocketSession session) {
        try {
            // 从查询参数中获取token和用户信息
            String query = session.getUri().getQuery();
            if (query != null) {
                String[] params = query.split("&");
                for (String param : params) {
                    String[] keyValue = param.split("=");
                    if (keyValue.length == 2) {
                        String key = keyValue[0];
                        String value = keyValue[1];
                        
                        if ("userId".equals(key)) {
                            return value;
                        } else if ("merchantId".equals(key)) {
                            return "merchant_" + value;
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("获取用户ID失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 广播消息给所有在线用户
     */
    public void broadcastMessage(Object message) {
        sessions.values().forEach(session -> {
            if (session.isOpen()) {
                sendMessage(session, message);
            }
        });
    }

    /**
     * 发送消息给指定用户
     */
    public void sendMessageToUser(String userId, Object message) {
        String sessionId = userSessions.get(userId);
        if (sessionId != null) {
            WebSocketSession session = sessions.get(sessionId);
            if (session != null && session.isOpen()) {
                sendMessage(session, message);
            }
        }
    }

    /**
     * 获取在线用户数量
     */
    public int getOnlineUserCount() {
        return (int) sessions.values().stream()
                .filter(WebSocketSession::isOpen)
                .count();
    }
}
