package com.example.controller;

import com.example.model.LaundryOrder;
import com.example.model.User;
import com.example.service.LaundryOrderService;
import com.example.service.UserService;
import com.example.repository.UserRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.time.LocalDateTime;

@RestController
@RequestMapping("/api/admin")
@Tag(name = "管理端API", description = "管理端专用的API接口")
@CrossOrigin(origins = "*", allowedHeaders = "*", methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.OPTIONS})
public class AdminApiController {

    @Autowired
    private UserService userService;

    @Autowired
    private LaundryOrderService laundryOrderService;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;

    // ==================== 管理员认证 ====================
    @PostMapping("/login")
    @Operation(summary = "管理员登录")
    public ResponseEntity<Map<String, Object>> login(@RequestBody Map<String, String> loginRequest) {
        try {
            String usernameOrPhone = loginRequest.get("username");
            String password = loginRequest.get("password");

            if (usernameOrPhone == null || password == null) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "用户名和密码不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 查找管理员账号（支持用户名或手机号登录）
            com.example.model.User user = userRepository.findByUsername(usernameOrPhone)
                    .orElse(null);
            
            // 如果按用户名没找到，尝试按手机号查找
            if (user == null) {
                List<com.example.model.User> allUsers = userRepository.findAll();
                for (com.example.model.User u : allUsers) {
                    if (usernameOrPhone.equals(u.getPhone())) {
                        user = u;
                        break;
                    }
                }
            }

            if (user == null) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "管理员账号不存在");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 验证密码
            if (!passwordEncoder.matches(password, user.getPassword())) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "密码错误");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 检查用户状态
            if (!"ACTIVE".equals(user.getStatus())) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "管理员账号已被禁用");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 检查是否是管理员角色
            if (user.getRole() != User.UserRole.ADMIN) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "该账号不是管理员账号");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 更新登录信息
            user.setUpdatedAt(LocalDateTime.now());
            userRepository.save(user);

            // 生成响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "登录成功");
            response.put("token", "admin_token_" + System.currentTimeMillis());
            
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", user.getId());
            userInfo.put("username", user.getUsername());
            userInfo.put("phone", user.getPhone());
            userInfo.put("realName", user.getRealName());
            userInfo.put("email", user.getEmail());
            userInfo.put("role", "ADMIN");
            userInfo.put("avatar", user.getAvatar());
            userInfo.put("status", user.getStatus());
            userInfo.put("permissions", List.of("*", "user:view", "user:edit", "merchant:view", "order:view", "system:config"));
            
            response.put("user", userInfo);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "登录失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    // ==================== 用户管理 ====================
    @GetMapping("/users")
    @Operation(summary = "获取用户列表")
    public ResponseEntity<Page<User>> getUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String role) {
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<User> users = userRepository.findAll(pageable);
        return ResponseEntity.ok(users);
    }

    @GetMapping("/users/{id}")
    @Operation(summary = "获取用户详情")
    public ResponseEntity<User> getUserDetail(@PathVariable Long id) {
        return userRepository.findById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/users/{id}/status")
    @Operation(summary = "更新用户状态")
    public ResponseEntity<Map<String, Object>> updateUserStatus(
            @PathVariable Long id,
            @RequestBody Map<String, String> statusData) {
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "用户状态更新成功");
        
        return ResponseEntity.ok(response);
    }

    // ==================== 订单管理 ====================
    @GetMapping("/orders")
    @Operation(summary = "获取所有订单")
    public ResponseEntity<Page<LaundryOrder>> getAllOrders(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String status) {
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<LaundryOrder> orders = laundryOrderService.getAllOrders(pageable);
        return ResponseEntity.ok(orders);
    }

    // ==================== 商家管理 ====================
    @GetMapping("/merchants")
    @Operation(summary = "获取商家列表")
    public ResponseEntity<List<Map<String, Object>>> getMerchants() {
        List<Map<String, Object>> merchants = List.of(
            Map.of(
                "id", 1L,
                "shopName", "示例洗衣店",
                "contactPerson", "张老板",
                "phone", "13900139000",
                "status", "ACTIVE",
                "certificationStatus", "APPROVED",
                "totalOrders", 1250,
                "rating", 4.8
            ),
            Map.of(
                "id", 2L,
                "shopName", "品质洗护中心",
                "contactPerson", "李经理",
                "phone", "13800138000",
                "status", "ACTIVE",
                "certificationStatus", "PENDING",
                "totalOrders", 850,
                "rating", 4.6
            )
        );
        
        return ResponseEntity.ok(merchants);
    }

    @PutMapping("/merchants/{id}/certification")
    @Operation(summary = "审核商家认证")
    public ResponseEntity<Map<String, Object>> approveMerchant(
            @PathVariable Long id,
            @RequestBody Map<String, String> approvalData) {
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "商家认证审核完成");
        
        return ResponseEntity.ok(response);
    }

    // ==================== 系统统计 ====================
    @GetMapping("/statistics")
    @Operation(summary = "获取系统统计数据")
    public ResponseEntity<Map<String, Object>> getSystemStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalUsers", 5280);
        statistics.put("totalMerchants", 156);
        statistics.put("totalOrders", 12580);
        statistics.put("todayOrders", 125);
        statistics.put("totalRevenue", 1250000.0);
        statistics.put("monthlyRevenue", 125000.0);
        statistics.put("activeUsers", 3250);
        statistics.put("activeMerchants", 128);
        
        return ResponseEntity.ok(statistics);
    }

    // ==================== 系统公告管理 ====================
    @GetMapping("/announcements")
    @Operation(summary = "获取公告列表")
    public ResponseEntity<List<Map<String, Object>>> getAnnouncements() {
        List<Map<String, Object>> announcements = List.of(
            Map.of(
                "id", 1L,
                "title", "系统维护通知",
                "content", "系统将于今晚22:00-24:00进行维护",
                "type", "SYSTEM",
                "status", "PUBLISHED",
                "createdAt", "2024-12-28 10:00:00"
            ),
            Map.of(
                "id", 2L,
                "title", "新功能上线",
                "content", "新增在线支付功能",
                "type", "FEATURE",
                "status", "PUBLISHED",
                "createdAt", "2024-12-27 15:30:00"
            )
        );
        
        return ResponseEntity.ok(announcements);
    }

    @PostMapping("/announcements")
    @Operation(summary = "发布公告")
    public ResponseEntity<Map<String, Object>> createAnnouncement(@RequestBody Map<String, Object> announcementData) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "公告发布成功");
        response.put("id", 3L);
        
        return ResponseEntity.ok(response);
    }
}
