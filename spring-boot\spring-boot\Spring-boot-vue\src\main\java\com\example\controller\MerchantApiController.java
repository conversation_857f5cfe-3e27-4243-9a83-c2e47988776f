package com.example.controller;

import com.example.model.LaundryOrder;
import com.example.model.User;
import com.example.service.LaundryOrderService;
import com.example.service.UserService;
import com.example.repository.UserRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.time.LocalDateTime;

@RestController
@RequestMapping("/api/merchant")
@Tag(name = "商家端API", description = "商家端专用的API接口")
@CrossOrigin(origins = "*", allowedHeaders = "*", methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.OPTIONS})
public class MerchantApiController {

    @Autowired
    private UserService userService;

    @Autowired
    private LaundryOrderService laundryOrderService;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;

    // ==================== 商家认证 ====================
    @PostMapping("/login")
    @Operation(summary = "商家登录")
    public ResponseEntity<Map<String, Object>> login(@RequestBody Map<String, String> loginRequest) {
        try {
            String usernameOrPhone = loginRequest.get("username");
            String password = loginRequest.get("password");

            if (usernameOrPhone == null || password == null) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "用户名和密码不能为空");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 查找商家账号（支持用户名或手机号登录）
            com.example.model.User user = userRepository.findByUsername(usernameOrPhone)
                    .orElse(null);
            
            // 如果按用户名没找到，尝试按手机号查找
            if (user == null) {
                List<com.example.model.User> allUsers = userRepository.findAll();
                for (com.example.model.User u : allUsers) {
                    if (usernameOrPhone.equals(u.getPhone())) {
                        user = u;
                        break;
                    }
                }
            }

            if (user == null) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "商家账号不存在");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 验证密码
            if (!passwordEncoder.matches(password, user.getPassword())) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "密码错误");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 检查用户状态
            if (!"ACTIVE".equals(user.getStatus())) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "商家账号已被禁用");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 检查是否是商家角色
            if (user.getRole() != User.UserRole.MERCHANT && user.getRole() != User.UserRole.ADMIN) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "该账号不是商家账号");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 更新登录信息
            user.setUpdatedAt(LocalDateTime.now());
            userRepository.save(user);

            // 生成响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "登录成功");
            response.put("token", "merchant_token_" + System.currentTimeMillis());
            
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", user.getId());
            userInfo.put("username", user.getUsername());
            userInfo.put("phone", user.getPhone());
            userInfo.put("realName", user.getRealName());
            userInfo.put("email", user.getEmail());
            userInfo.put("role", "MERCHANT");
            userInfo.put("avatar", user.getAvatar());
            userInfo.put("status", user.getStatus());
            
            response.put("user", userInfo);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "登录失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    // ==================== 商家店铺管理 ====================
    @GetMapping("/profile")
    @Operation(summary = "获取商家资料")
    public ResponseEntity<Map<String, Object>> getMerchantProfile() {
        Map<String, Object> profile = new HashMap<>();
        profile.put("id", 1L);
        profile.put("shopName", "示例洗衣店");
        profile.put("contactPerson", "张老板");
        profile.put("phone", "***********");
        profile.put("email", "<EMAIL>");
        profile.put("address", "北京市朝阳区三里屯街道123号");
        profile.put("businessHours", "08:00-22:00");
        profile.put("description", "专业洗护服务，品质保证");
        profile.put("rating", 4.8);
        profile.put("totalOrders", 1250);
        profile.put("monthlyRevenue", 25000.0);
        profile.put("status", "ACTIVE");
        profile.put("certificationStatus", "APPROVED");
        
        return ResponseEntity.ok(profile);
    }

    @PutMapping("/profile")
    @Operation(summary = "更新商家资料")
    public ResponseEntity<Map<String, Object>> updateMerchantProfile(@RequestBody Map<String, Object> profileData) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "商家资料更新成功");
        
        return ResponseEntity.ok(response);
    }

    // ==================== 商家订单管理 ====================
    @GetMapping("/orders")
    @Operation(summary = "获取商家订单列表")
    public ResponseEntity<Page<LaundryOrder>> getMerchantOrders(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String status) {
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<LaundryOrder> orders = laundryOrderService.getAllOrders(pageable);
        return ResponseEntity.ok(orders);
    }

    @GetMapping("/orders/{id}")
    @Operation(summary = "获取商家订单详情")
    public ResponseEntity<LaundryOrder> getMerchantOrderDetail(@PathVariable Long id) {
        return laundryOrderService.getOrderById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/orders/{id}/status")
    @Operation(summary = "更新订单状态")
    public ResponseEntity<Map<String, Object>> updateOrderStatus(
            @PathVariable Long id,
            @RequestBody Map<String, String> statusData) {
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "订单状态更新成功");
        
        return ResponseEntity.ok(response);
    }

    // ==================== 商家服务管理 ====================
    @GetMapping("/services")
    @Operation(summary = "获取商家服务列表")
    public ResponseEntity<List<Map<String, Object>>> getMerchantServices() {
        List<Map<String, Object>> services = List.of(
            Map.of(
                "id", 1L,
                "name", "精品干洗",
                "description", "专业干洗服务",
                "price", 25.0,
                "duration", 24,
                "status", "ACTIVE"
            ),
            Map.of(
                "id", 2L,
                "name", "水洗服务",
                "description", "普通水洗服务",
                "price", 15.0,
                "duration", 12,
                "status", "ACTIVE"
            )
        );
        
        return ResponseEntity.ok(services);
    }

    @PostMapping("/services")
    @Operation(summary = "添加商家服务")
    public ResponseEntity<Map<String, Object>> addMerchantService(@RequestBody Map<String, Object> serviceData) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "服务添加成功");
        response.put("id", 3L);
        
        return ResponseEntity.ok(response);
    }

    // ==================== 商家财务管理 ====================
    @GetMapping("/finance/summary")
    @Operation(summary = "获取财务概览")
    public ResponseEntity<Map<String, Object>> getFinanceSummary() {
        Map<String, Object> summary = new HashMap<>();
        summary.put("todayRevenue", 1250.0);
        summary.put("monthlyRevenue", 25000.0);
        summary.put("totalRevenue", 150000.0);
        summary.put("pendingAmount", 2500.0);
        summary.put("withdrawableAmount", 22500.0);
        
        return ResponseEntity.ok(summary);
    }

    @GetMapping("/finance/transactions")
    @Operation(summary = "获取交易记录")
    public ResponseEntity<Page<Map<String, Object>>> getTransactions(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        return ResponseEntity.ok(Page.empty(pageable));
    }

    // ==================== 商家统计数据 ====================
    @GetMapping("/statistics")
    @Operation(summary = "获取商家统计数据")
    public ResponseEntity<Map<String, Object>> getMerchantStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalOrders", 1250);
        statistics.put("completedOrders", 1180);
        statistics.put("cancelledOrders", 70);
        statistics.put("averageRating", 4.8);
        statistics.put("totalCustomers", 850);
        statistics.put("repeatCustomers", 520);
        
        return ResponseEntity.ok(statistics);
    }
}
