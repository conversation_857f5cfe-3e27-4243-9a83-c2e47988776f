# 用户端配置
server.port=8081

# JWT配置
jwt.secret=mySecretKey123456789mySecretKey123456789mySecretKey123456789
jwt.expiration=86400000
jwt.refresh-expiration=604800000

# MySQL数据库配置
spring.datasource.url=**************************************************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# HikariCP连接池配置
spring.datasource.hikari.connectionTimeout=30000
spring.datasource.hikari.maximumPoolSize=10
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-test-query=SELECT 1
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# JPA配置
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.defer-datasource-initialization=false
spring.sql.init.mode=never
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# 禁用H2控制台
spring.h2.console.enabled=false

# 用户端日志配置
logging.level.com.example=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Jackson配置
spring.jackson.serialization.fail-on-empty-beans=false
spring.jackson.serialization.write-dates-as-timestamps=false
spring.jackson.deserialization.fail-on-unknown-properties=false
spring.jackson.default-property-inclusion=non-null

# Hibernate配置
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true
spring.jpa.open-in-view=true

# API文档配置
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html

# 监控配置
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=always

# CORS配置
spring.web.cors.allowed-origins=http://localhost:5173
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
spring.web.cors.allow-credentials=true

# 用户端特定配置
app.name=用户端服务
app.description=洗护平台用户端API服务
app.version=1.0.0
