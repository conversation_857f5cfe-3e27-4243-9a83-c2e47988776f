# 生产环境配置
server.port=8080

# JWT配置
jwt.secret=${JWT_SECRET:mySecretKey123456789mySecretKey123456789mySecretKey123456789}
jwt.expiration=86400
app.jwtSecret=${JWT_SECRET:mySecretKey123456789mySecretKey123456789mySecretKey123456789}
app.jwtExpirationInMs=86400000

# MySQL数据库配置
spring.datasource.url=${DB_URL:****************************************************************************************************************************************}
spring.datasource.username=${DB_USERNAME:root}
spring.datasource.password=${DB_PASSWORD:123456}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# HikariCP连接池配置
spring.datasource.hikari.connectionTimeout=30000
spring.datasource.hikari.maximumPoolSize=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-test-query=SELECT 1
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# JPA配置
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.defer-datasource-initialization=false
spring.sql.init.mode=never
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.use_sql_comments=false

# 禁用H2控制台
spring.h2.console.enabled=false

# 生产环境日志配置
logging.file.name=logs/laundry-app.log
logging.logback.rollingpolicy.max-file-size=100MB
logging.logback.rollingpolicy.max-history=30
logging.level.org.springframework=WARN
logging.level.org.springframework.security=WARN
logging.level.com.example.laundry=INFO
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN

# 性能优化
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true

# 安全配置
server.error.include-stacktrace=never
server.error.include-message=never

# 监控配置
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized
management.metrics.export.prometheus.enabled=true 