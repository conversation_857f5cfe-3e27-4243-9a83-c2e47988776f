# 应用配置
spring.application.name=laundry-management-system
spring.profiles.active=dev

# JWT配置 - 默认配置，防止启动失败
jwt.secret=mySecretKey123456789012345678901234567890123456789012345678901234567890abcdefghijklmnopqrstuvwxyz
jwt.expiration=86400000
jwt.refresh-expiration=604800000

# Jackson配置 - 解决序列化问题
spring.jackson.serialization.fail-on-empty-beans=false
spring.jackson.serialization.write-dates-as-timestamps=false
spring.jackson.deserialization.fail-on-unknown-properties=false
spring.jackson.default-property-inclusion=non-null

# Hibernate配置 - 解决懒加载序列化问题
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true
spring.jpa.open-in-view=true

# 日志配置
logging.level.com.example=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# API文档配置
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
