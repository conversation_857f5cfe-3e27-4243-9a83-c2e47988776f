package com.example.config;

import com.example.model.*;
import com.example.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Component
public class DataInitializer implements CommandLineRunner {

    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private WashServiceRepository washServiceRepository;
    
    @Autowired
    private EquipmentRepository equipmentRepository;
    
    @Autowired
    private InventoryRepository inventoryRepository;
    
    @Autowired
    private CouponRepository couponRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        initializeUsers();
        initializeWashServices();
        initializeEquipment();
        initializeInventory();
        initializeCoupons();
        initializeSystemConfigs();
    }

    private void initializeUsers() {
        if (userRepository.count() == 0) {
            // 创建管理员用户
            User admin = new User();
            admin.setUsername("admin");
            admin.setPassword(passwordEncoder.encode("admin123"));
            admin.setPhone("13800138000");
            admin.setEmail("<EMAIL>");
            admin.setRealName("系统管理员");
            admin.setRole(User.UserRole.ADMIN);
            admin.setStatus("ACTIVE");
            userRepository.save(admin);

            // 创建工人用户
            User worker = new User();
            worker.setUsername("worker");
            worker.setPassword(passwordEncoder.encode("worker123"));
            worker.setPhone("13800138001");
            worker.setEmail("<EMAIL>");
            worker.setRealName("洗护工人");
            worker.setRole(User.UserRole.WORKER);
            worker.setStatus("ACTIVE");
            userRepository.save(worker);

            // 创建客户用户
            User customer = new User();
            customer.setUsername("customer");
            customer.setPassword(passwordEncoder.encode("customer123"));
            customer.setPhone("13800138002");
            customer.setEmail("<EMAIL>");
            customer.setRealName("普通客户");
            customer.setRole(User.UserRole.CUSTOMER);
            customer.setStatus("ACTIVE");
            customer.setMembershipLevel(User.MembershipLevel.REGULAR);
            userRepository.save(customer);

            System.out.println("初始化用户数据完成");
        }
    }

    private void initializeWashServices() {
        if (washServiceRepository.count() == 0) {
            // 干洗服务
            WashService dryClean = new WashService();
            dryClean.setName("干洗服务");
            dryClean.setDescription("专业干洗，适用于高档衣物");
            dryClean.setCategory(WashService.ServiceCategory.DRY_CLEANING);
            dryClean.setPrice(new BigDecimal("30.00"));
            dryClean.setProcessingHours(48);
            dryClean.setSortOrder(1);
            washServiceRepository.save(dryClean);

            // 水洗服务
            WashService wetClean = new WashService();
            wetClean.setName("水洗服务");
            wetClean.setDescription("标准水洗，适用于日常衣物");
            wetClean.setCategory(WashService.ServiceCategory.WET_CLEANING);
            wetClean.setPrice(new BigDecimal("15.00"));
            wetClean.setProcessingHours(24);
            wetClean.setSortOrder(2);
            washServiceRepository.save(wetClean);

            // 熨烫服务
            WashService ironing = new WashService();
            ironing.setName("熨烫服务");
            ironing.setDescription("专业熨烫，让衣物平整如新");
            ironing.setCategory(WashService.ServiceCategory.IRONING);
            ironing.setPrice(new BigDecimal("10.00"));
            ironing.setProcessingHours(2);
            ironing.setSortOrder(3);
            washServiceRepository.save(ironing);

            System.out.println("初始化洗护服务数据完成");
        }
    }

    private void initializeEquipment() {
        if (equipmentRepository.count() == 0) {
            // 洗衣机
            Equipment washingMachine = new Equipment();
            washingMachine.setName("工业洗衣机-001");
            washingMachine.setSerialNumber("WM001");
            washingMachine.setType(Equipment.EquipmentType.WASHING_MACHINE);
            washingMachine.setBrand("海尔");
            washingMachine.setModel("XQG100-B12866");
            washingMachine.setStatus(Equipment.EquipmentStatus.AVAILABLE);
            washingMachine.setLocation("洗衣区A");
            washingMachine.setPurchaseDate(LocalDateTime.now().minusYears(1));
            equipmentRepository.save(washingMachine);

            // 烘干机
            Equipment dryer = new Equipment();
            dryer.setName("工业烘干机-001");
            dryer.setSerialNumber("DR001");
            dryer.setType(Equipment.EquipmentType.DRYER);
            dryer.setBrand("美的");
            dryer.setModel("MG100V31D");
            dryer.setStatus(Equipment.EquipmentStatus.AVAILABLE);
            dryer.setLocation("烘干区A");
            dryer.setPurchaseDate(LocalDateTime.now().minusYears(1));
            equipmentRepository.save(dryer);

            System.out.println("初始化设备数据完成");
        }
    }

    private void initializeInventory() {
        if (inventoryRepository.count() == 0) {
            // 洗涤剂
            Inventory detergent = new Inventory();
            detergent.setItemName("多功能洗涤剂");
            detergent.setItemCode("DET001");
            detergent.setCategory(Inventory.ItemCategory.DETERGENT);
            detergent.setDescription("适用于各种面料的洗涤剂");
            detergent.setUnit("瓶");
            detergent.setCurrentStock(50);
            detergent.setMinStock(10);
            detergent.setMaxStock(100);
            detergent.setUnitCost(new BigDecimal("25.00"));
            detergent.setSupplier("清洁用品有限公司");
            detergent.setStorageLocation("仓库A-01");
            inventoryRepository.save(detergent);

            // 柔顺剂
            Inventory softener = new Inventory();
            softener.setItemName("衣物柔顺剂");
            softener.setItemCode("SOF001");
            softener.setCategory(Inventory.ItemCategory.SOFTENER);
            softener.setDescription("让衣物更加柔软舒适");
            softener.setUnit("瓶");
            softener.setCurrentStock(30);
            softener.setMinStock(5);
            softener.setMaxStock(50);
            softener.setUnitCost(new BigDecimal("18.00"));
            softener.setSupplier("清洁用品有限公司");
            softener.setStorageLocation("仓库A-02");
            inventoryRepository.save(softener);

            System.out.println("初始化库存数据完成");
        }
    }

    private void initializeCoupons() {
        if (couponRepository.count() == 0) {
            // 新用户优惠券
            Coupon newUserCoupon = new Coupon();
            newUserCoupon.setName("新用户专享优惠券");
            newUserCoupon.setCode("NEW2024");
            newUserCoupon.setType(Coupon.CouponType.FIXED_AMOUNT);
            newUserCoupon.setDiscountValue(new BigDecimal("10.00"));
            newUserCoupon.setMinOrderAmount(new BigDecimal("50.00"));
            newUserCoupon.setTotalQuantity(1000);
            newUserCoupon.setUsedQuantity(0);
            newUserCoupon.setUserLimit(1);
            newUserCoupon.setStartTime(LocalDateTime.now());
            newUserCoupon.setEndTime(LocalDateTime.now().plusMonths(3));
            newUserCoupon.setDescription("新用户注册即可获得10元优惠券");
            couponRepository.save(newUserCoupon);

            // 满减优惠券
            Coupon discountCoupon = new Coupon();
            discountCoupon.setName("满100减20优惠券");
            discountCoupon.setCode("SAVE20");
            discountCoupon.setType(Coupon.CouponType.FIXED_AMOUNT);
            discountCoupon.setDiscountValue(new BigDecimal("20.00"));
            discountCoupon.setMinOrderAmount(new BigDecimal("100.00"));
            discountCoupon.setTotalQuantity(500);
            discountCoupon.setUsedQuantity(0);
            discountCoupon.setUserLimit(3);
            discountCoupon.setStartTime(LocalDateTime.now());
            discountCoupon.setEndTime(LocalDateTime.now().plusMonths(1));
            discountCoupon.setDescription("满100元立减20元");
            couponRepository.save(discountCoupon);

            System.out.println("初始化优惠券数据完成");
        }
    }

    private void initializeSystemConfigs() {
        // 这里可以初始化系统配置，但由于SystemConfigService可能还没有注入
        // 我们暂时跳过，或者在SystemConfigService中实现
        System.out.println("系统配置初始化跳过，将在SystemConfigService中处理");
    }
}
