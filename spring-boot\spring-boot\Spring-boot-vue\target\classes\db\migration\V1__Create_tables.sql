-- 聊天消息表
CREATE TABLE chat_messages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    sender_id VARCHAR(100) NOT NULL COMMENT '发送者用户ID',
    receiver_id VARCHAR(100) NOT NULL COMMENT '接收者用户ID',
    content TEXT COMMENT '消息内容',
    message_type ENUM('TEXT', 'IMAGE', 'FILE', 'SYSTEM', 'ORDER_INQUIRY', 'SERVICE_INQUIRY') DEFAULT 'TEXT' COMMENT '消息类型',
    status ENUM('SENDING', 'SENT', 'DELIVERED', 'FAILED') DEFAULT 'SENT' COMMENT '消息状态',
    is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
    read_at DATETIME COMMENT '读取时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    extra_data TEXT COMMENT '额外数据（JSON格式）',
    INDEX idx_sender_receiver (sender_id, receiver_id),
    INDEX idx_created_at (created_at),
    INDEX idx_is_read (is_read)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天消息表';

-- 公告表
CREATE TABLE announcements (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL COMMENT '公告标题',
    content TEXT COMMENT '公告内容',
    type ENUM('GENERAL', 'SYSTEM', 'MAINTENANCE', 'PROMOTION', 'POLICY', 'EMERGENCY') DEFAULT 'GENERAL' COMMENT '公告类型',
    target_type ENUM('ALL', 'CUSTOMERS', 'MERCHANTS', 'ADMINS', 'SPECIFIC') DEFAULT 'ALL' COMMENT '目标用户类型',
    priority ENUM('LOW', 'NORMAL', 'HIGH', 'URGENT') DEFAULT 'NORMAL' COMMENT '优先级',
    status ENUM('DRAFT', 'SCHEDULED', 'PUBLISHED', 'EXPIRED', 'CANCELLED') DEFAULT 'DRAFT' COMMENT '发布状态',
    is_pinned BOOLEAN DEFAULT FALSE COMMENT '是否置顶',
    publisher_id VARCHAR(100) COMMENT '发布者ID',
    publisher_name VARCHAR(100) COMMENT '发布者姓名',
    scheduled_at DATETIME COMMENT '计划发布时间',
    published_at DATETIME COMMENT '实际发布时间',
    expires_at DATETIME COMMENT '过期时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    read_count INT DEFAULT 0 COMMENT '阅读次数',
    attachment_url VARCHAR(500) COMMENT '附件URL',
    INDEX idx_status (status),
    INDEX idx_target_type (target_type),
    INDEX idx_created_at (created_at),
    INDEX idx_published_at (published_at),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公告表';

-- 商家服务表
CREATE TABLE merchant_services (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    merchant_id VARCHAR(100) NOT NULL COMMENT '商家ID',
    merchant_name VARCHAR(100) COMMENT '商家名称',
    title VARCHAR(200) NOT NULL COMMENT '服务标题',
    description TEXT COMMENT '服务描述',
    category ENUM('LAUNDRY', 'DRY_CLEANING', 'IRONING', 'SHOE_CLEANING', 'BAG_CLEANING', 'CARPET_CLEANING', 'CURTAIN_CLEANING', 'BEDDING_CLEANING', 'OTHER') COMMENT '服务分类',
    service_type ENUM('PICKUP_DELIVERY', 'SELF_SERVICE', 'STORE_SERVICE', 'EXPRESS_SERVICE', 'PREMIUM_SERVICE') COMMENT '服务类型',
    price DECIMAL(10,2) COMMENT '价格',
    original_price DECIMAL(10,2) COMMENT '原价（用于显示折扣）',
    duration_minutes INT COMMENT '服务时长（分钟）',
    status ENUM('DRAFT', 'PENDING', 'APPROVED', 'PUBLISHED', 'SUSPENDED', 'REJECTED', 'DELETED') DEFAULT 'DRAFT' COMMENT '服务状态',
    is_recommended BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
    is_popular BOOLEAN DEFAULT FALSE COMMENT '是否热门',
    sort_order INT DEFAULT 0 COMMENT '排序权重',
    image_urls TEXT COMMENT '服务图片URL列表（JSON格式）',
    tags TEXT COMMENT '服务标签（JSON格式）',
    service_area VARCHAR(200) COMMENT '服务区域',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_address VARCHAR(300) COMMENT '联系地址',
    business_hours VARCHAR(100) COMMENT '营业时间',
    rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '评分',
    review_count INT DEFAULT 0 COMMENT '评价数量',
    order_count INT DEFAULT 0 COMMENT '订单数量',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    favorite_count INT DEFAULT 0 COMMENT '收藏次数',
    published_at DATETIME COMMENT '发布时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    extra_info TEXT COMMENT '额外信息（JSON格式）',
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_status (status),
    INDEX idx_category (category),
    INDEX idx_service_type (service_type),
    INDEX idx_is_recommended (is_recommended),
    INDEX idx_is_popular (is_popular),
    INDEX idx_rating (rating),
    INDEX idx_created_at (created_at),
    INDEX idx_published_at (published_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家服务表';

-- 用户收藏表
CREATE TABLE user_favorites (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(100) NOT NULL COMMENT '用户ID',
    service_id BIGINT NOT NULL COMMENT '服务ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
    UNIQUE KEY uk_user_service (user_id, service_id),
    INDEX idx_user_id (user_id),
    INDEX idx_service_id (service_id),
    FOREIGN KEY (service_id) REFERENCES merchant_services(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏表';

-- 服务评价表
CREATE TABLE service_reviews (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    service_id BIGINT NOT NULL COMMENT '服务ID',
    user_id VARCHAR(100) NOT NULL COMMENT '用户ID',
    order_id BIGINT COMMENT '订单ID',
    rating DECIMAL(2,1) NOT NULL COMMENT '评分（1-5）',
    content TEXT COMMENT '评价内容',
    images TEXT COMMENT '评价图片（JSON格式）',
    reply_content TEXT COMMENT '商家回复',
    reply_at DATETIME COMMENT '回复时间',
    is_anonymous BOOLEAN DEFAULT FALSE COMMENT '是否匿名',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_service_id (service_id),
    INDEX idx_user_id (user_id),
    INDEX idx_order_id (order_id),
    INDEX idx_rating (rating),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (service_id) REFERENCES merchant_services(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务评价表';

-- 订单表
CREATE TABLE orders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    order_no VARCHAR(32) UNIQUE NOT NULL COMMENT '订单号',
    user_id VARCHAR(100) NOT NULL COMMENT '用户ID',
    merchant_id VARCHAR(100) NOT NULL COMMENT '商家ID',
    service_id BIGINT NOT NULL COMMENT '服务ID',
    service_title VARCHAR(200) COMMENT '服务标题',
    service_price DECIMAL(10,2) COMMENT '服务价格',
    quantity INT DEFAULT 1 COMMENT '数量',
    total_amount DECIMAL(10,2) COMMENT '总金额',
    status ENUM('PENDING', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'REFUNDED') DEFAULT 'PENDING' COMMENT '订单状态',
    pickup_address TEXT COMMENT '取件地址',
    pickup_time DATETIME COMMENT '取件时间',
    delivery_address TEXT COMMENT '送达地址',
    delivery_time DATETIME COMMENT '送达时间',
    notes TEXT COMMENT '备注',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_order_no (order_no),
    INDEX idx_user_id (user_id),
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_service_id (service_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (service_id) REFERENCES merchant_services(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 系统配置表
CREATE TABLE system_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('STRING', 'NUMBER', 'BOOLEAN', 'JSON') DEFAULT 'STRING' COMMENT '配置类型',
    description VARCHAR(500) COMMENT '配置描述',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统配置',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 插入一些初始数据
INSERT INTO system_configs (config_key, config_value, config_type, description, is_system) VALUES
('websocket.heartbeat.interval', '30000', 'NUMBER', 'WebSocket心跳间隔（毫秒）', TRUE),
('announcement.auto.expire', 'true', 'BOOLEAN', '是否自动过期公告', TRUE),
('service.auto.recommend', 'false', 'BOOLEAN', '是否自动推荐服务', TRUE),
('chat.message.max.length', '500', 'NUMBER', '聊天消息最大长度', TRUE),
('upload.image.max.size', '2097152', 'NUMBER', '图片上传最大大小（字节）', TRUE);
