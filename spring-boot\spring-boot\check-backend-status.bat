@echo off
echo ========================================
echo 检查后端服务状态
echo ========================================

echo.
echo 检查端口占用情况:
echo ----------------------------------------
echo 检查8080端口 (管理端后端):
netstat -ano | findstr :8080
echo.

echo 检查8081端口 (用户端后端):
netstat -ano | findstr :8081
echo.

echo 检查8082端口 (商家端后端):
netstat -ano | findstr :8082
echo.

echo ========================================
echo 测试后端API连接
echo ========================================

echo 测试管理端后端 (8080):
curl -s -o nul -w "HTTP状态码: %%{http_code}\n" http://localhost:8080/api/health || echo "连接失败"
echo.

echo 测试用户端后端 (8081):
curl -s -o nul -w "HTTP状态码: %%{http_code}\n" http://localhost:8081/api/health || echo "连接失败"
echo.

echo 测试商家端后端 (8082):
curl -s -o nul -w "HTTP状态码: %%{http_code}\n" http://localhost:8082/api/health || echo "连接失败"
echo.

echo ========================================
echo 检查Java进程
echo ========================================
tasklist | findstr java.exe

echo.
echo 检查完成！
pause
