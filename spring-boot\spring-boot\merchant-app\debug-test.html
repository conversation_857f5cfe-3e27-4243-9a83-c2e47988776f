<!DOCTYPE html>
<html>
<head>
    <title>Debug API Test</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button { margin: 5px; padding: 8px 16px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>Debug API Test</h1>
    
    <button onclick="testDirectBackend()">Test Direct Backend (8080)</button>
    <button onclick="testDirectBackend8082()">Test Direct Backend (8082)</button>
    <button onclick="testProxyAuth()">Test Proxy Auth</button>
    <button onclick="testActuator()">Test Actuator Health</button>
    
    <div id="results"></div>

    <script>
        function addResult(title, content, isSuccess = false) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.innerHTML = `<h3>${title}</h3><pre>${content}</pre>`;
            resultsDiv.appendChild(resultDiv);
        }

        async function testDirectBackend() {
            try {
                const response = await fetch('http://localhost:8080/api/auth/info', {
                    method: 'GET',
                    mode: 'cors'
                });
                
                const text = await response.text();
                const headers = Object.fromEntries(response.headers);
                
                addResult(
                    `Direct Backend 8080 - Status: ${response.status}`,
                    `Response: ${text}\n\nHeaders: ${JSON.stringify(headers, null, 2)}`,
                    response.status < 500
                );
                
            } catch (error) {
                addResult('Direct Backend 8080 Error', error.message);
            }
        }

        async function testDirectBackend8082() {
            try {
                const response = await fetch('http://localhost:8082/api/auth/info', {
                    method: 'GET',
                    mode: 'cors'
                });
                
                const text = await response.text();
                const headers = Object.fromEntries(response.headers);
                
                addResult(
                    `Direct Backend 8082 - Status: ${response.status}`,
                    `Response: ${text}\n\nHeaders: ${JSON.stringify(headers, null, 2)}`,
                    response.status < 500
                );
                
            } catch (error) {
                addResult('Direct Backend 8082 Error', error.message);
            }
        }

        async function testProxyAuth() {
            try {
                const response = await fetch('/api/auth/info', {
                    method: 'GET'
                });
                
                const text = await response.text();
                const headers = Object.fromEntries(response.headers);
                
                addResult(
                    `Proxy Auth - Status: ${response.status}`,
                    `Response: ${text}\n\nHeaders: ${JSON.stringify(headers, null, 2)}`,
                    response.status < 500
                );
                
            } catch (error) {
                addResult('Proxy Auth Error', error.message);
            }
        }

        async function testActuator() {
            try {
                // Test actuator health endpoint
                const response = await fetch('/actuator/health', {
                    method: 'GET'
                });
                
                const text = await response.text();
                const headers = Object.fromEntries(response.headers);
                
                addResult(
                    `Actuator Health - Status: ${response.status}`,
                    `Response: ${text}\n\nHeaders: ${JSON.stringify(headers, null, 2)}`,
                    response.status < 500
                );
                
            } catch (error) {
                addResult('Actuator Health Error', error.message);
            }
        }

        // Auto test on load
        window.onload = function() {
            setTimeout(testDirectBackend, 500);
            setTimeout(testDirectBackend8082, 1000);
            setTimeout(testProxyAuth, 1500);
            setTimeout(testActuator, 2000);
        };
    </script>
</body>
</html>
