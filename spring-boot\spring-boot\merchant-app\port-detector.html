<!DOCTYPE html>
<html>
<head>
    <title>Backend Port Detector</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button { margin: 5px; padding: 8px 16px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
    </style>
</head>
<body>
    <h1>Backend Port Detector</h1>
    <p>Click buttons to test which port the backend is running on:</p>
    
    <button onclick="testPort(8080)">Test Port 8080</button>
    <button onclick="testPort(8081)">Test Port 8081</button>
    <button onclick="testPort(8082)">Test Port 8082</button>
    <button onclick="testPort(8083)">Test Port 8083</button>
    <button onclick="testPort(9090)">Test Port 9090</button>
    
    <div id="results"></div>

    <script>
        async function testPort(port) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'result';
            resultDiv.innerHTML = `Testing port ${port}...`;
            resultsDiv.appendChild(resultDiv);
            
            try {
                // Test direct connection to the port
                const response = await fetch(`http://localhost:${port}/api/auth/info`, {
                    method: 'GET',
                    mode: 'cors'
                });
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>Port ${port}: FOUND!</strong><br>
                    Status: ${response.status}<br>
                    Backend is running on port ${port}
                `;
                
                console.log(`Port ${port} test successful:`, response);
                
            } catch (error) {
                // Try alternative endpoint
                try {
                    const response2 = await fetch(`http://localhost:${port}/actuator/health`, {
                        method: 'GET',
                        mode: 'cors'
                    });
                    
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>Port ${port}: FOUND (via actuator)!</strong><br>
                        Status: ${response2.status}<br>
                        Backend is running on port ${port}
                    `;
                    
                } catch (error2) {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <strong>Port ${port}: Not accessible</strong><br>
                        Error: ${error.message}
                    `;
                }
            }
        }
        
        // Auto-test common ports
        window.onload = function() {
            setTimeout(() => testPort(8080), 500);
            setTimeout(() => testPort(8082), 1000);
            setTimeout(() => testPort(8081), 1500);
        };
    </script>
</body>
</html>
