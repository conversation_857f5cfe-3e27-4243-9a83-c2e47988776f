<!DOCTYPE html>
<html>
<head>
    <title>Simple API Test</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button { margin: 10px; padding: 10px 20px; }
        #result { margin-top: 20px; border: 1px solid #ccc; padding: 10px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Backend API Test</h1>
    
    <button onclick="testBackend()">Test Backend Connection</button>
    <button onclick="testLogin()">Test Login API</button>
    
    <div id="result"></div>

    <script>
        async function testBackend() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing backend connection...';
            
            try {
                // Test a simple GET request to auth info (should return 401 but proves connection)
                const response = await fetch('/api/auth/info', {
                    method: 'GET'
                });
                
                const responseText = await response.text();
                
                resultDiv.innerHTML = `
                    <h3>Backend Connection Test - Status: ${response.status}</h3>
                    <h3>Response:</h3>
                    <pre>${responseText || 'Empty response'}</pre>
                `;
                
                console.log('Backend test response:', response);
                console.log('Backend test text:', responseText);
                
            } catch (error) {
                resultDiv.innerHTML = `<h3>Backend Connection Error: ${error.message}</h3>`;
                console.error('Backend test error:', error);
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing login API...';
            
            try {
                const response = await fetch('/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: '13900139000',
                        password: '123456'
                    })
                });
                
                const responseText = await response.text();
                
                resultDiv.innerHTML = `
                    <h3>Login API Test - Status: ${response.status}</h3>
                    <h3>Response:</h3>
                    <pre>${responseText || 'Empty response'}</pre>
                `;
                
                console.log('Login test response:', response);
                console.log('Login test text:', responseText);
                
            } catch (error) {
                resultDiv.innerHTML = `<h3>Login API Error: ${error.message}</h3>`;
                console.error('Login test error:', error);
            }
        }
    </script>
</body>
</html>
