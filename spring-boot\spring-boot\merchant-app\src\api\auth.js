import request from '@/utils/request'

// 登录
export function login(data) {
  return request({
    url: '/api/auth/login',
    method: 'post',
    data: {
      username: data.username,
      password: data.password
    }
  })
}

// 注册
export function register(data) {
  return request({
    url: '/api/auth/register',
    method: 'post',
    data
  })
}

// 发送验证码
export function sendVerifyCode(data) {
  return request({
    url: '/api/auth/verify-code',
    method: 'post',
    data
  })
}

// 重置密码
export function resetPassword(data) {
  return request({
    url: '/api/auth/reset-password',
    method: 'post',
    data
  })
}

// 修改密码
export function changePassword(data) {
  return request({
    url: '/api/auth/password',
    method: 'put',
    data
  })
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/api/auth/info',
    method: 'get'
  })
}

// 刷新token
export function refreshToken() {
  return request({
    url: '/api/auth/refresh-token',
    method: 'post'
  })
}

// 退出登录
export function logout() {
  return request({
    url: '/api/auth/logout',
    method: 'post'
  })
}

// 第三方登录
export function socialLogin(type) {
  return request({
    url: `/api/auth/social-login/${type}`,
    method: 'post'
  })
}

// 获取第三方登录URL
export function getSocialLoginUrl(type) {
  return request({
    url: `/api/auth/social-login/${type}/url`,
    method: 'get'
  })
}

// 第三方登录回调
export function socialLoginCallback(type, code) {
  return request({
    url: `/api/auth/social-login/${type}/callback`,
    method: 'get',
    params: { code }
  })
}