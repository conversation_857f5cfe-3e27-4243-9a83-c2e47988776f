import request from '@/utils/request'

// 商家端投诉相关API
export const complaintApi = {
  // 获取商家投诉列表
  getMerchantComplaints(params) {
    return request({
      url: '/merchant/complaints',
      method: 'get',
      params
    })
  },

  // 获取投诉详情
  getComplaintDetail(id) {
    return request({
      url: `/merchant/complaints/${id}`,
      method: 'get'
    })
  },

  // 处理投诉
  handleComplaint(id, data) {
    return request({
      url: `/merchant/complaints/${id}/handle`,
      method: 'post',
      data
    })
  },

  // 获取商家投诉统计
  getMerchantComplaintStats() {
    return request({
      url: '/merchant/complaints/stats',
      method: 'get'
    })
  },

  // 回复投诉
  replyComplaint(id, data) {
    return request({
      url: `/merchant/complaints/${id}/reply`,
      method: 'post',
      data
    })
  },

  // 上传处理凭证
  uploadEvidence(file) {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', 'complaint_evidence')
    
    return request({
      url: '/common/upload',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取投诉处理记录
  getComplaintRecords(id) {
    return request({
      url: `/merchant/complaints/${id}/records`,
      method: 'get'
    })
  },

  // 导出投诉数据
  exportComplaints(params) {
    return request({
      url: '/merchant/complaints/export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  }
}

export default complaintApi
