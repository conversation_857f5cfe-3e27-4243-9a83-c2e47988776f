import request from '@/utils/request'

// 获取财务概览数据
export function getFinanceData() {
  return request({
    url: '/merchant/finance/overview',
    method: 'get'
  })
}

// 获取收益明细列表
export function getEarningsList(params) {
  return request({
    url: '/merchant/finance/earnings',
    method: 'get',
    params
  })
}

// 获取提现记录列表
export function getWithdrawList(params) {
  return request({
    url: '/merchant/finance/withdraw/list',
    method: 'get',
    params
  })
}

// 获取银行卡列表
export function getBankCards() {
  return request({
    url: '/merchant/finance/bank-cards',
    method: 'get'
  })
}

// 添加银行卡
export function addBankCard(data) {
  return request({
    url: '/merchant/finance/bank-cards',
    method: 'post',
    data
  })
}

// 更新银行卡
export function updateBankCard(id, data) {
  return request({
    url: `/merchant/finance/bank-cards/${id}`,
    method: 'put',
    data
  })
}

// 删除银行卡
export function deleteBankCard(id) {
  return request({
    url: `/merchant/finance/bank-cards/${id}`,
    method: 'delete'
  })
}

// 设置默认银行卡
export function setDefaultBankCard(id) {
  return request({
    url: `/merchant/finance/bank-cards/${id}/default`,
    method: 'put'
  })
}

// 提交提现申请
export function submitWithdrawRequest(data) {
  return request({
    url: '/merchant/finance/withdraw/apply',
    method: 'post',
    data
  })
}

// 获取提现详情
export function getWithdrawDetail(id) {
  return request({
    url: `/merchant/finance/withdraw/${id}`,
    method: 'get'
  })
}

// 取消提现申请
export function cancelWithdrawRequest(id) {
  return request({
    url: `/merchant/finance/withdraw/${id}/cancel`,
    method: 'put'
  })
}

// 获取财务统计数据
export function getFinanceStatistics(params) {
  return request({
    url: '/merchant/finance/statistics',
    method: 'get',
    params
  })
}

// 导出收益明细
export function exportEarnings(params) {
  return request({
    url: '/merchant/finance/earnings/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出提现记录
export function exportWithdrawRecords(params) {
  return request({
    url: '/merchant/finance/withdraw/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取提现配置
export function getWithdrawConfig() {
  return request({
    url: '/merchant/finance/withdraw/config',
    method: 'get'
  })
}

// 验证银行卡信息
export function validateBankCard(data) {
  return request({
    url: '/merchant/finance/bank-cards/validate',
    method: 'post',
    data
  })
}

// 获取支持的银行列表
export function getSupportedBanks() {
  return request({
    url: '/merchant/finance/banks',
    method: 'get'
  })
}

// 获取账单明细
export function getBillDetails(params) {
  return request({
    url: '/merchant/finance/bills',
    method: 'get',
    params
  })
}

// 获取分成比例
export function getCommissionRate() {
  return request({
    url: '/merchant/finance/commission-rate',
    method: 'get'
  })
}

// 获取结算记录
export function getSettlementRecords(params) {
  return request({
    url: '/merchant/finance/settlement',
    method: 'get',
    params
  })
}
