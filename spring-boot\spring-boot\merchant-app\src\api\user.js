import request from '@/utils/request'

// 登录
export const loginApi = (data) => {
  // 将前端的phone字段映射为后端期望的username字段
  const loginData = {
    username: data.phone || data.username,
    password: data.password
  }

  return request({
    url: '/auth/login',
    method: 'post',
    data: loginData
  })
}

// 注册
export const registerApi = (data) => {
  return request({
    url: '/auth/register',
    method: 'post',
    data
  })
}

// 获取用户信息
export const getUserInfoApi = () => {
  return request({
    url: '/auth/info',
    method: 'get'
  })
}

// 修改密码
export const changePasswordApi = (data) => {
  return request({
    url: '/auth/change-password',
    method: 'post',
    data
  })
}

// 更新用户信息
export const updateUserInfoApi = (data) => {
  return request({
    url: '/auth/update-profile',
    method: 'put',
    data
  })
}

// 忘记密码
export const forgotPasswordApi = (data) => {
  return request({
    url: '/auth/forgot-password',
    method: 'post',
    data
  })
}

// 重置密码
export const resetPasswordApi = (data) => {
  return request({
    url: '/auth/reset-password',
    method: 'post',
    data
  })
}

// 刷新token
export const refreshTokenApi = () => {
  return request({
    url: '/auth/refresh',
    method: 'post'
  })
}

// 登出
export const logoutApi = () => {
  return request({
    url: '/auth/logout',
    method: 'post'
  })
} 