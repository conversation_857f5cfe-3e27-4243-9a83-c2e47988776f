import axios from 'axios'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import router from '@/router'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.DEV ? '' : (import.meta.env.VITE_APP_BASE_API || 'http://localhost:8082'),
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

let loadingInstance = null

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 开发环境下打印请求信息
    if (import.meta.env.DEV) {
      console.log('发送请求:', config.method?.toUpperCase(), config.url, config.data)
    }

    // 显示loading
    if (config.showLoading !== false) {
      loadingInstance = ElLoading.service({
        lock: true,
        text: '请稍候...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
    }

    // 添加token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }

    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    return config
  },
  error => {
    if (import.meta.env.DEV) {
      console.error('请求拦截器错误:', error)
    }
    if (loadingInstance) {
      loadingInstance.close()
    }
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    if (loadingInstance) {
      loadingInstance.close()
    }

    const res = response.data

    // 如果返回的状态码不是200，说明接口有问题
    if (res.code && res.code !== 200) {
      ElMessage.error(res.message || '请求失败')

      // 401: 未授权，需要重新登录
      if (res.code === 401) {
        ElMessageBox.confirm('登录状态已过期，请重新登录', '系统提示', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          localStorage.removeItem('token')
          localStorage.removeItem('userInfo')
          router.push('/login')
        })
      }

      return Promise.reject(new Error(res.message || '请求失败'))
    }

    // 返回数据
    return res
  },
  error => {
    if (loadingInstance) {
      loadingInstance.close()
    }

    // 开发环境下打印错误详情
    if (import.meta.env.DEV) {
      console.error('响应拦截器错误:', error)
      console.error('错误详情:', {
        message: error.message,
        response: error.response,
        request: error.request,
        config: error.config
      })
    }

    let message = '请求失败'

    if (error.response) {
      const { status, data } = error.response
      if (import.meta.env.DEV) {
        console.error('响应错误:', status, data)
      }

      switch (status) {
        case 400:
          message = data.message || '请求参数错误'
          break
        case 401:
          message = data.message || '登录状态已过期，请重新登录'
          // 只有在非登录页面时才跳转
          if (!window.location.pathname.includes('/login')) {
            localStorage.removeItem('token')
            localStorage.removeItem('userInfo')
            router.push('/login')
          }
          break
        case 403:
          message = '没有权限访问该资源'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = data.message || `请求失败 (${status})`
      }
    } else if (error.code === 'ECONNABORTED') {
      message = '请求超时，请稍后重试'
    } else if (error.message.includes('Network Error')) {
      message = '网络连接失败，请检查网络'
    }

    ElMessage.error(message)
    return Promise.reject(error)
  }
)

// 封装GET请求
export function get(url, params, config = {}) {
  return service.get(url, { params, ...config })
}

// 封装POST请求
export function post(url, data, config = {}) {
  return service.post(url, data, config)
}

// 封装PUT请求
export function put(url, data, config = {}) {
  return service.put(url, data, config)
}

// 封装DELETE请求
export function del(url, params, config = {}) {
  return service.delete(url, { params, ...config })
}

// 导出axios实例
export default service 