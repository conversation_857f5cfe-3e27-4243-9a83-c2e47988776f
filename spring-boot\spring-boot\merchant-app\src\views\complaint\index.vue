<template>
  <div class="complaint-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">投诉管理</h1>
        <div class="header-stats">
          <el-statistic
            v-for="stat in stats"
            :key="stat.key"
            :title="stat.title"
            :value="stat.value"
            :value-style="{ color: stat.color }"
            class="stat-item"
          />
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <el-card>
        <el-form :model="filterForm" inline>
          <el-form-item label="投诉状态">
            <el-select v-model="filterForm.status" placeholder="全部状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="待处理" value="pending" />
              <el-option label="处理中" value="processing" />
              <el-option label="已解决" value="resolved" />
              <el-option label="已关闭" value="closed" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="投诉类型">
            <el-select v-model="filterForm.type" placeholder="全部类型" clearable>
              <el-option label="全部" value="" />
              <el-option label="服务质量" value="service" />
              <el-option label="配送问题" value="delivery" />
              <el-option label="价格争议" value="price" />
              <el-option label="服务态度" value="attitude" />
              <el-option label="其他问题" value="other" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="searchComplaints">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 投诉列表 -->
    <div class="complaint-list">
      <el-card>
        <el-table
          v-loading="loading"
          :data="complaints"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="id" label="投诉编号" width="120" />
          <el-table-column prop="title" label="投诉标题" min-width="200" />
          <el-table-column prop="orderId" label="订单号" width="150" />
          <el-table-column prop="typeText" label="投诉类型" width="120" />
          <el-table-column prop="customerName" label="客户" width="120" />
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="提交时间" width="180" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="viewComplaint(row)"
              >
                查看详情
              </el-button>
              <el-button
                v-if="row.status === 'pending'"
                type="success"
                size="small"
                @click="handleComplaint(row)"
              >
                处理
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 投诉详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="投诉详情"
      width="800px"
      :before-close="closeDetailDialog"
    >
      <div v-if="currentComplaint" class="complaint-detail">
        <div class="detail-section">
          <h3>基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="投诉编号">{{ currentComplaint.id }}</el-descriptions-item>
            <el-descriptions-item label="订单号">{{ currentComplaint.orderId }}</el-descriptions-item>
            <el-descriptions-item label="投诉类型">{{ currentComplaint.typeText }}</el-descriptions-item>
            <el-descriptions-item label="客户姓名">{{ currentComplaint.customerName }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ currentComplaint.customerPhone }}</el-descriptions-item>
            <el-descriptions-item label="提交时间">{{ currentComplaint.createTime }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="detail-section">
          <h3>投诉内容</h3>
          <div class="complaint-content">{{ currentComplaint.content }}</div>
          
          <!-- 投诉图片 -->
          <div v-if="currentComplaint.images && currentComplaint.images.length > 0" class="complaint-images">
            <h4>相关图片</h4>
            <div class="image-gallery">
              <el-image
                v-for="(image, index) in currentComplaint.images"
                :key="index"
                :src="image"
                :preview-src-list="currentComplaint.images"
                :initial-index="index"
                class="complaint-image"
                fit="cover"
              />
            </div>
          </div>
        </div>

        <!-- 处理记录 -->
        <div v-if="currentComplaint.processRecords && currentComplaint.processRecords.length > 0" class="detail-section">
          <h3>处理记录</h3>
          <el-timeline>
            <el-timeline-item
              v-for="(record, index) in currentComplaint.processRecords"
              :key="index"
              :timestamp="record.time"
            >
              <div class="process-record">
                <div class="record-title">{{ record.title }}</div>
                <div class="record-content">{{ record.content }}</div>
                <div class="record-operator">处理人：{{ record.operator }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDetailDialog">关闭</el-button>
          <el-button
            v-if="currentComplaint && currentComplaint.status === 'pending'"
            type="primary"
            @click="showHandleDialog = true"
          >
            立即处理
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 处理投诉对话框 -->
    <el-dialog
      v-model="showHandleDialog"
      title="处理投诉"
      width="600px"
    >
      <el-form
        ref="handleFormRef"
        :model="handleForm"
        :rules="handleRules"
        label-width="100px"
      >
        <el-form-item label="处理方式" prop="type">
          <el-select v-model="handleForm.type" placeholder="请选择处理方式">
            <el-option label="协调处理" value="mediate" />
            <el-option label="退款处理" value="refund" />
            <el-option label="赔偿处理" value="compensate" />
            <el-option label="驳回投诉" value="reject" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="处理说明" prop="note">
          <el-input
            v-model="handleForm.note"
            type="textarea"
            :rows="4"
            placeholder="请详细说明处理方案和结果"
          />
        </el-form-item>
        
        <el-form-item v-if="handleForm.type === 'refund'" label="退款金额" prop="refundAmount">
          <el-input-number
            v-model="handleForm.refundAmount"
            :min="0"
            :precision="2"
            placeholder="请输入退款金额"
          />
        </el-form-item>
        
        <el-form-item v-if="handleForm.type === 'compensate'" label="赔偿金额" prop="compensateAmount">
          <el-input-number
            v-model="handleForm.compensateAmount"
            :min="0"
            :precision="2"
            placeholder="请输入赔偿金额"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showHandleDialog = false">取消</el-button>
          <el-button
            type="primary"
            :loading="handling"
            @click="submitHandle"
          >
            提交处理
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { complaintApi } from '@/api/complaint'

// 响应式数据
const loading = ref(false)
const handling = ref(false)
const complaints = ref([])
const currentComplaint = ref(null)
const showDetailDialog = ref(false)
const showHandleDialog = ref(false)
const handleFormRef = ref()

// 统计数据
const stats = ref([
  { key: 'total', title: '总投诉数', value: 0, color: '#409eff' },
  { key: 'pending', title: '待处理', value: 0, color: '#e6a23c' },
  { key: 'processing', title: '处理中', value: 0, color: '#409eff' },
  { key: 'resolved', title: '已解决', value: 0, color: '#67c23a' }
])

// 筛选表单
const filterForm = reactive({
  status: '',
  type: '',
  dateRange: null
})

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 处理表单
const handleForm = reactive({
  type: '',
  note: '',
  refundAmount: 0,
  compensateAmount: 0
})

// 处理表单验证规则
const handleRules = {
  type: [
    { required: true, message: '请选择处理方式', trigger: 'change' }
  ],
  note: [
    { required: true, message: '请输入处理说明', trigger: 'blur' },
    { min: 10, max: 500, message: '说明长度在10到500个字符', trigger: 'blur' }
  ]
}

// 生命周期
onMounted(() => {
  fetchComplaints()
  fetchStats()
})

// 方法定义
const fetchComplaints = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...filterForm
    }

    const response = await complaintApi.getMerchantComplaints(params)
    const { data, total } = response.data
    
    complaints.value = data
    pagination.total = total
    
  } catch (error) {
    console.error('获取投诉列表失败:', error)
    ElMessage.error('获取投诉列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const fetchStats = async () => {
  try {
    const response = await complaintApi.getMerchantComplaintStats()
    const data = response.data
    
    stats.value.forEach(stat => {
      stat.value = data[stat.key] || 0
    })
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 搜索投诉
const searchComplaints = () => {
  pagination.page = 1
  fetchComplaints()
}

// 重置筛选
const resetFilter = () => {
  Object.assign(filterForm, {
    status: '',
    type: '',
    dateRange: null
  })
  searchComplaints()
}

// 查看投诉详情
const viewComplaint = async (complaint) => {
  try {
    const response = await complaintApi.getComplaintDetail(complaint.id)
    currentComplaint.value = response.data
    showDetailDialog.value = true
  } catch (error) {
    ElMessage.error('获取投诉详情失败')
  }
}

// 处理投诉
const handleComplaint = (complaint) => {
  currentComplaint.value = complaint
  showHandleDialog.value = true
  
  // 重置处理表单
  Object.assign(handleForm, {
    type: '',
    note: '',
    refundAmount: 0,
    compensateAmount: 0
  })
}

// 提交处理
const submitHandle = async () => {
  try {
    await handleFormRef.value.validate()
    handling.value = true
    
    const handleData = {
      type: handleForm.type,
      note: handleForm.note
    }
    
    if (handleForm.type === 'refund') {
      handleData.refundAmount = handleForm.refundAmount
    } else if (handleForm.type === 'compensate') {
      handleData.compensateAmount = handleForm.compensateAmount
    }
    
    await complaintApi.handleComplaint(currentComplaint.value.id, handleData)
    
    ElMessage.success('投诉处理成功')
    showHandleDialog.value = false
    
    // 刷新列表和统计
    fetchComplaints()
    fetchStats()
    
  } catch (error) {
    if (error !== 'validation failed') {
      ElMessage.error('处理失败，请重试')
    }
  } finally {
    handling.value = false
  }
}

// 关闭详情对话框
const closeDetailDialog = () => {
  showDetailDialog.value = false
  currentComplaint.value = null
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.page = 1
  fetchComplaints()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchComplaints()
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    pending: 'warning',
    processing: 'primary',
    resolved: 'success',
    closed: 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: '待处理',
    processing: '处理中',
    resolved: '已解决',
    closed: '已关闭'
  }
  return statusMap[status] || '未知'
}
</script>

<style scoped>
.complaint-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-stats {
  display: flex;
  gap: 40px;
}

.stat-item {
  text-align: center;
}

.filter-section {
  margin-bottom: 20px;
}

.complaint-list {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.complaint-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.complaint-content {
  line-height: 1.8;
  color: #606266;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.complaint-images h4 {
  margin: 16px 0 12px 0;
  font-size: 14px;
  color: #303133;
}

.image-gallery {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.complaint-image {
  width: 100px;
  height: 100px;
  border-radius: 8px;
  cursor: pointer;
}

.process-record {
  margin-bottom: 8px;
}

.record-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.record-content {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 4px;
}

.record-operator {
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
