<template>
  <div class="finance-page">
    <!-- 财务概览 -->
    <div class="finance-overview">
      <el-row :gutter="24">
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="amount">¥{{ financeData.totalEarnings }}</div>
              <div class="label">总收益</div>
              <div class="trend" :class="financeData.earningsTrend >= 0 ? 'up' : 'down'">
                {{ financeData.earningsTrend >= 0 ? '+' : '' }}{{ financeData.earningsTrend }}%
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="amount">¥{{ financeData.availableAmount }}</div>
              <div class="label">可提现金额</div>
              <div class="action">
                <el-button type="primary" size="small" @click="showWithdrawDialog">提现</el-button>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="amount">¥{{ financeData.monthEarnings }}</div>
              <div class="label">本月收益</div>
              <div class="trend" :class="financeData.monthTrend >= 0 ? 'up' : 'down'">
                {{ financeData.monthTrend >= 0 ? '+' : '' }}{{ financeData.monthTrend }}%
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="amount">{{ financeData.orderCount }}</div>
              <div class="label">订单数量</div>
              <div class="trend" :class="financeData.orderTrend >= 0 ? 'up' : 'down'">
                {{ financeData.orderTrend >= 0 ? '+' : '' }}{{ financeData.orderTrend }}%
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 功能区域 -->
    <el-row :gutter="24" style="margin-top: 24px;">
      <el-col :span="16">
        <!-- 收益明细 -->
        <el-card class="earnings-card">
          <template #header>
            <div class="card-header">
              <span>收益明细</span>
              <div class="header-actions">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="loadEarningsData"
                  size="small"
                />
                <el-button type="primary" size="small" @click="exportEarnings">导出</el-button>
              </div>
            </div>
          </template>
          
          <el-table :data="earningsList" v-loading="earningsLoading">
            <el-table-column prop="orderNumber" label="订单号" width="150" />
            <el-table-column prop="serviceName" label="服务项目" width="120" />
            <el-table-column prop="amount" label="订单金额" width="100">
              <template #default="{ row }">
                <span>¥{{ row.amount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="commission" label="平台抽成" width="100">
              <template #default="{ row }">
                <span>¥{{ row.commission }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="earnings" label="实际收益" width="100">
              <template #default="{ row }">
                <span class="earnings-amount">¥{{ row.earnings }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">{{ row.statusText }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="时间" width="140">
              <template #default="{ row }">
                <span>{{ formatTime(row.createTime) }}</span>
              </template>
            </el-table-column>
          </el-table>
          
          <el-pagination
            v-if="earningsTotal > 0"
            :current-page="earningsPage"
            :page-size="pageSize"
            :total="earningsTotal"
            @current-change="handleEarningsPageChange"
            style="margin-top: 16px; text-align: center;"
          />
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <!-- 提现记录 -->
        <el-card class="withdraw-card">
          <template #header>
            <span>提现记录</span>
          </template>
          
          <div class="withdraw-list" v-loading="withdrawLoading">
            <div 
              v-for="item in withdrawList" 
              :key="item.id" 
              class="withdraw-item"
            >
              <div class="withdraw-info">
                <div class="amount">¥{{ item.amount }}</div>
                <div class="time">{{ formatTime(item.createTime) }}</div>
              </div>
              <div class="withdraw-status">
                <el-tag :type="getWithdrawStatusType(item.status)">
                  {{ item.statusText }}
                </el-tag>
              </div>
            </div>
            
            <div v-if="!withdrawList.length && !withdrawLoading" class="empty-data">
              <el-empty description="暂无提现记录" :image-size="80" />
            </div>
          </div>
          
          <div class="withdraw-actions">
            <el-button type="text" @click="viewAllWithdraw">查看全部</el-button>
          </div>
        </el-card>

        <!-- 银行卡管理 -->
        <el-card class="bank-card" style="margin-top: 16px;">
          <template #header>
            <div class="card-header">
              <span>银行卡管理</span>
              <el-button type="text" @click="addBankCard">添加</el-button>
            </div>
          </template>
          
          <div class="bank-list" v-loading="bankLoading">
            <div 
              v-for="card in bankCards" 
              :key="card.id" 
              class="bank-item"
              :class="{ 'default': card.isDefault }"
            >
              <div class="bank-info">
                <div class="bank-name">{{ card.bankName }}</div>
                <div class="card-number">**** **** **** {{ card.cardNumber.slice(-4) }}</div>
              </div>
              <div class="bank-actions">
                <el-tag v-if="card.isDefault" type="success" size="small">默认</el-tag>
                <el-button type="text" size="small" @click="editBankCard(card)">编辑</el-button>
              </div>
            </div>
            
            <div v-if="!bankCards.length && !bankLoading" class="empty-data">
              <el-empty description="暂无银行卡" :image-size="60" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 提现对话框 -->
    <el-dialog
      v-model="withdrawDialogVisible"
      title="申请提现"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="withdrawForm" :rules="withdrawRules" ref="withdrawFormRef" label-width="100px">
        <el-form-item label="提现金额" prop="amount">
          <el-input-number
            v-model="withdrawForm.amount"
            :min="1"
            :max="financeData.availableAmount"
            :precision="2"
            style="width: 100%;"
            placeholder="请输入提现金额"
          />
          <div class="form-tip">
            可提现金额：¥{{ financeData.availableAmount }}
          </div>
        </el-form-item>
        
        <el-form-item label="提现银行卡" prop="bankCardId">
          <el-select v-model="withdrawForm.bankCardId" style="width: 100%;" placeholder="请选择银行卡">
            <el-option
              v-for="card in bankCards"
              :key="card.id"
              :label="`${card.bankName} **** ${card.cardNumber.slice(-4)}`"
              :value="card.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="手续费">
          <span>¥{{ withdrawFee }}</span>
          <div class="form-tip">
            实际到账：¥{{ (withdrawForm.amount - withdrawFee).toFixed(2) }}
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="withdrawDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitWithdraw" :loading="withdrawSubmitting">
          确认提现
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getFinanceData, getEarningsList, getWithdrawList, getBankCards, submitWithdrawRequest } from '@/api/finance'

export default {
  name: 'FinancePage',
  setup() {
    // 响应式数据
    const financeData = reactive({
      totalEarnings: '0.00',
      availableAmount: '0.00',
      monthEarnings: '0.00',
      orderCount: 0,
      earningsTrend: 0,
      monthTrend: 0,
      orderTrend: 0
    })

    const earningsList = ref([])
    const withdrawList = ref([])
    const bankCards = ref([])
    const dateRange = ref([])
    
    // 分页数据
    const earningsPage = ref(1)
    const earningsTotal = ref(0)
    const pageSize = ref(10)
    
    // 加载状态
    const earningsLoading = ref(false)
    const withdrawLoading = ref(false)
    const bankLoading = ref(false)
    
    // 提现相关
    const withdrawDialogVisible = ref(false)
    const withdrawSubmitting = ref(false)
    const withdrawForm = reactive({
      amount: 0,
      bankCardId: ''
    })
    
    const withdrawRules = {
      amount: [
        { required: true, message: '请输入提现金额', trigger: 'blur' },
        { type: 'number', min: 1, message: '提现金额不能小于1元', trigger: 'blur' }
      ],
      bankCardId: [
        { required: true, message: '请选择银行卡', trigger: 'change' }
      ]
    }
    
    // 计算手续费
    const withdrawFee = computed(() => {
      if (withdrawForm.amount <= 0) return 0
      // 提现手续费规则：1000以下免费，1000以上收取0.1%，最低2元
      if (withdrawForm.amount <= 1000) {
        return 0
      } else {
        return Math.max(2, withdrawForm.amount * 0.001)
      }
    })

    // 加载财务概览数据
    const loadFinanceData = async () => {
      try {
        const response = await getFinanceData()
        Object.assign(financeData, response.data)
      } catch (error) {
        console.error('加载财务数据失败:', error)
        ElMessage.error('加载财务数据失败')
      }
    }

    // 加载收益明细
    const loadEarningsData = async () => {
      earningsLoading.value = true
      try {
        const params = {
          page: earningsPage.value,
          pageSize: pageSize.value,
          startDate: dateRange.value?.[0],
          endDate: dateRange.value?.[1]
        }
        
        const response = await getEarningsList(params)
        earningsList.value = response.data.list
        earningsTotal.value = response.data.total
      } catch (error) {
        console.error('加载收益明细失败:', error)
        ElMessage.error('加载收益明细失败')
      } finally {
        earningsLoading.value = false
      }
    }

    // 加载提现记录
    const loadWithdrawData = async () => {
      withdrawLoading.value = true
      try {
        const response = await getWithdrawList({ pageSize: 5 })
        withdrawList.value = response.data.list
      } catch (error) {
        console.error('加载提现记录失败:', error)
      } finally {
        withdrawLoading.value = false
      }
    }

    // 加载银行卡
    const loadBankCards = async () => {
      bankLoading.value = true
      try {
        const response = await getBankCards()
        bankCards.value = response.data
      } catch (error) {
        console.error('加载银行卡失败:', error)
      } finally {
        bankLoading.value = false
      }
    }

    // 显示提现对话框
    const showWithdrawDialog = () => {
      if (bankCards.value.length === 0) {
        ElMessage.warning('请先添加银行卡')
        return
      }
      withdrawDialogVisible.value = true
      withdrawForm.amount = 0
      withdrawForm.bankCardId = bankCards.value.find(card => card.isDefault)?.id || ''
    }

    // 提交提现申请
    const submitWithdraw = async () => {
      try {
        await this.$refs.withdrawFormRef.validate()
        
        withdrawSubmitting.value = true
        
        await submitWithdrawRequest({
          amount: withdrawForm.amount,
          bankCardId: withdrawForm.bankCardId,
          fee: withdrawFee.value
        })
        
        ElMessage.success('提现申请已提交')
        withdrawDialogVisible.value = false
        
        // 刷新数据
        loadFinanceData()
        loadWithdrawData()
        
      } catch (error) {
        if (error !== false) { // 表单验证失败
          ElMessage.error('提现申请失败')
        }
      } finally {
        withdrawSubmitting.value = false
      }
    }

    // 工具方法
    const getStatusType = (status) => {
      const statusMap = {
        'COMPLETED': 'success',
        'PENDING': 'warning',
        'CANCELLED': 'danger'
      }
      return statusMap[status] || 'info'
    }

    const getWithdrawStatusType = (status) => {
      const statusMap = {
        'SUCCESS': 'success',
        'PENDING': 'warning',
        'FAILED': 'danger'
      }
      return statusMap[status] || 'info'
    }

    const formatTime = (time) => {
      return new Date(time).toLocaleString()
    }

    const handleEarningsPageChange = (page) => {
      earningsPage.value = page
      loadEarningsData()
    }

    const exportEarnings = () => {
      ElMessage.info('导出功能开发中')
    }

    const viewAllWithdraw = () => {
      this.$router.push('/finance/withdraw')
    }

    const addBankCard = () => {
      this.$router.push('/finance/bank-card/add')
    }

    const editBankCard = (card) => {
      this.$router.push(`/finance/bank-card/edit/${card.id}`)
    }

    onMounted(() => {
      loadFinanceData()
      loadEarningsData()
      loadWithdrawData()
      loadBankCards()
    })

    return {
      financeData,
      earningsList,
      withdrawList,
      bankCards,
      dateRange,
      earningsPage,
      earningsTotal,
      pageSize,
      earningsLoading,
      withdrawLoading,
      bankLoading,
      withdrawDialogVisible,
      withdrawSubmitting,
      withdrawForm,
      withdrawRules,
      withdrawFee,
      showWithdrawDialog,
      submitWithdraw,
      getStatusType,
      getWithdrawStatusType,
      formatTime,
      handleEarningsPageChange,
      loadEarningsData,
      exportEarnings,
      viewAllWithdraw,
      addBankCard,
      editBankCard
    }
  }
}
</script>

<style scoped>
.finance-page {
  padding: 24px;
}

.finance-overview .overview-card {
  text-align: center;
}

.card-content {
  padding: 20px 0;
}

.amount {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.trend {
  font-size: 12px;
}

.trend.up {
  color: #67c23a;
}

.trend.down {
  color: #f56c6c;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.earnings-amount {
  color: #67c23a;
  font-weight: bold;
}

.withdraw-list {
  max-height: 300px;
  overflow-y: auto;
}

.withdraw-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.withdraw-item:last-child {
  border-bottom: none;
}

.withdraw-info .amount {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 4px;
}

.withdraw-info .time {
  font-size: 12px;
  color: #909399;
}

.withdraw-actions {
  text-align: center;
  margin-top: 16px;
}

.bank-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
}

.bank-item.default {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.bank-info .bank-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.bank-info .card-number {
  font-size: 12px;
  color: #909399;
}

.bank-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.empty-data {
  text-align: center;
  padding: 20px 0;
}
</style>
