<!DOCTYPE html>
<html>
<head>
    <title>API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button { margin: 10px; padding: 10px 20px; }
        #result { margin-top: 20px; border: 1px solid #ccc; padding: 10px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>商家后端API测试</h1>

    <button onclick="testHealth()">Test Backend Connection</button>
    <button onclick="testLoginData()">Test Login Data Format</button>
    <button onclick="testLogin()">Test Real Login API</button>

    <div id="result"></div>

    <script>
        async function testHealth() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing health check...';

            try {
                const response = await fetch('/api/auth/info', {
                    method: 'GET'
                });

                const responseText = await response.text();

                resultDiv.innerHTML = `
                    <h3>Health Check - Status: ${response.status}</h3>
                    <h3>Response:</h3>
                    <pre>${responseText || 'Empty response'}</pre>
                    <h3>Headers:</h3>
                    <pre>${JSON.stringify(Object.fromEntries(response.headers), null, 2)}</pre>
                `;

                console.log('Health check response:', response);
                console.log('Health check text:', responseText);

            } catch (error) {
                resultDiv.innerHTML = `<h3>Health Check Error: ${error.message}</h3>`;
                console.error('Health check error:', error);
            }
        }

        async function testLoginData() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在测试登录数据格式...';

            try {
                const response = await fetch('/api/test/login-test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: '13900139000',
                        password: '123456'
                    })
                });

                const responseText = await response.text();

                resultDiv.innerHTML = `
                    <h3>登录数据测试 - 响应状态: ${response.status}</h3>
                    <h3>响应内容:</h3>
                    <pre>${responseText}</pre>
                `;

            } catch (error) {
                resultDiv.innerHTML = `<h3>登录数据测试错误: ${error.message}</h3>`;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在测试真实登录API...';

            try {
                const response = await fetch('/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: '13900139000',
                        password: '123456'
                    })
                });

                const responseText = await response.text();

                resultDiv.innerHTML = `
                    <h3>真实登录API - 响应状态: ${response.status}</h3>
                    <h3>响应内容:</h3>
                    <pre>${responseText}</pre>
                `;

                console.log('Response status:', response.status);
                console.log('Response text:', responseText);

            } catch (error) {
                resultDiv.innerHTML = `<h3>真实登录API错误: ${error.message}</h3>`;
                console.error('Error:', error);
            }
        }
    </script>
</body>
</html>
