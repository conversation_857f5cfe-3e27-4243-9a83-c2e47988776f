<!DOCTYPE html>
<html>
<head>
    <title>API测试</title>
</head>
<body>
    <h1>商家后端API测试</h1>
    <button onclick="testLogin()">测试登录API</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在测试...';
            
            try {
                const response = await fetch('/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: '13900139000',
                        password: '123456'
                    })
                });
                
                const responseText = await response.text();
                
                resultDiv.innerHTML = `
                    <h3>响应状态: ${response.status}</h3>
                    <h3>响应内容:</h3>
                    <pre>${responseText}</pre>
                `;
                
                console.log('Response status:', response.status);
                console.log('Response text:', responseText);
                
            } catch (error) {
                resultDiv.innerHTML = `<h3>错误: ${error.message}</h3>`;
                console.error('Error:', error);
            }
        }
    </script>
</body>
</html>
