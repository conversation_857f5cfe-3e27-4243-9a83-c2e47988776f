import request from '@/utils/request'

// 投诉相关API
export const complaintApi = {
  // 获取投诉列表
  getComplaints(params) {
    return request({
      url: '/user/complaints',
      method: 'get',
      params
    })
  },

  // 获取投诉详情
  getComplaintDetail(id) {
    return request({
      url: `/user/complaints/${id}`,
      method: 'get'
    })
  },

  // 创建投诉
  createComplaint(data) {
    return request({
      url: '/user/complaints',
      method: 'post',
      data
    })
  },

  // 补充投诉信息
  supplementComplaint(id, data) {
    return request({
      url: `/user/complaints/${id}/supplement`,
      method: 'post',
      data
    })
  },

  // 撤销投诉
  cancelComplaint(id) {
    return request({
      url: `/user/complaints/${id}/cancel`,
      method: 'post'
    })
  },

  // 确认解决
  confirmResolution(id) {
    return request({
      url: `/user/complaints/${id}/confirm`,
      method: 'post'
    })
  },

  // 上传投诉图片
  uploadImage(file) {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', 'complaint')
    
    return request({
      url: '/common/upload',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取投诉统计
  getComplaintStats() {
    return request({
      url: '/user/complaints/stats',
      method: 'get'
    })
  }
}

export default complaintApi
