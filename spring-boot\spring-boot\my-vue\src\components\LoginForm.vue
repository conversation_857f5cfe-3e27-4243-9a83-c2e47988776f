<template>
  <div class="login-form">
    <el-form
      ref="loginFormRef"
      :model="loginForm"
      :rules="loginRules"
      label-width="0"
      size="large"
    >
      <!-- 登录方式切换 -->
      <div class="login-tabs">
        <div
          class="tab-item"
          :class="{ active: loginType === 'password' }"
          @click="loginType = 'password'"
        >
          密码登录
        </div>
        <div
          class="tab-item"
          :class="{ active: loginType === 'sms' }"
          @click="loginType = 'sms'"
        >
          验证码登录
        </div>
      </div>

      <!-- 密码登录 -->
      <template v-if="loginType === 'password'">
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入手机号/用户名"
            prefix-icon="User"
            clearable
          />
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>
      </template>

      <!-- 验证码登录 -->
      <template v-else>
        <el-form-item prop="phone">
          <el-input
            v-model="loginForm.phone"
            placeholder="请输入手机号"
            prefix-icon="Phone"
            clearable
          />
        </el-form-item>
        <el-form-item prop="smsCode">
          <div class="sms-input-group">
            <el-input
              v-model="loginForm.smsCode"
              placeholder="请输入验证码"
              prefix-icon="Message"
              clearable
            />
            <el-button
              :disabled="smsCountdown > 0"
              @click="sendSmsCode"
              class="sms-btn"
            >
              {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
            </el-button>
          </div>
        </el-form-item>
      </template>

      <!-- 记住登录 -->
      <div class="login-options">
        <el-checkbox v-model="loginForm.remember">记住登录状态</el-checkbox>
        <a href="#" class="forgot-link">忘记密码？</a>
      </div>

      <!-- 超级管理员登录提示 -->
      <div class="super-admin-tip">
        <el-alert
          title="超级管理员账号"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <div class="tip-content">
              <p>账号: <strong>superadmin</strong></p>
              <p>密码: <strong>admin123456</strong></p>
              <p>可登录用户端、商家端、管理端</p>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 登录按钮 -->
      <el-form-item>
        <el-button
          type="primary"
          class="login-btn"
          :loading="loading"
          @click="handleLogin"
          block
        >
          {{ loading ? '登录中...' : '登录' }}
        </el-button>
      </el-form-item>

      <!-- 注册链接 -->
      <div class="register-link">
        还没有账号？
        <a href="#" @click="$emit('register')">立即注册</a>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'

const emit = defineEmits(['success', 'register'])
const userStore = useUserStore()

// 响应式数据
const loginFormRef = ref()
const loading = ref(false)
const loginType = ref('password') // 'password' | 'sms'
const smsCountdown = ref(0)

// 表单数据
const loginForm = reactive({
  username: 'superadmin',
  password: 'admin123456',
  phone: '',
  smsCode: '',
  remember: false
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入手机号或用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  smsCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '请输入6位数字验证码', trigger: 'blur' }
  ]
}

// 发送短信验证码
const sendSmsCode = async () => {
  if (!loginForm.phone) {
    ElMessage.warning('请先输入手机号')
    return
  }

  if (!/^1[3-9]\d{9}$/.test(loginForm.phone)) {
    ElMessage.warning('请输入正确的手机号')
    return
  }

  try {
    ElMessage.success('验证码已发送')

    // 开始倒计时
    smsCountdown.value = 60
    const timer = setInterval(() => {
      smsCountdown.value--
      if (smsCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)

  } catch (error) {
    ElMessage.error(error.message || '发送验证码失败')
  }
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()
    loading.value = true

    let loginData = {}

    if (loginType.value === 'password') {
      loginData = {
        username: loginForm.username,
        password: loginForm.password,
        loginType: 'PASSWORD'
      }
    } else {
      loginData = {
        phone: loginForm.phone,
        smsCode: loginForm.smsCode,
        loginType: 'SMS'
      }
    }

    // 调用登录API
    await userStore.login(loginData)

    ElMessage.success('登录成功')
    emit('success')

  } catch (error) {
    ElMessage.error(error.message || '登录失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-form {
  padding: 20px 0;
}

.login-tabs {
  display: flex;
  margin-bottom: 24px;
  background: #f5f5f5;
  border-radius: 8px;
  padding: 4px;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 500;
}

.tab-item.active {
  background: #ffffff;
  color: #409eff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sms-input-group {
  display: flex;
  gap: 12px;
}

.sms-input-group .el-input {
  flex: 1;
}

.sms-btn {
  white-space: nowrap;
  min-width: 100px;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.forgot-link {
  color: #409eff;
  text-decoration: none;
  font-size: 14px;
}

.forgot-link:hover {
  text-decoration: underline;
}

.super-admin-tip {
  margin-bottom: 24px;
}

.tip-content p {
  margin: 4px 0;
  font-size: 14px;
}

.login-btn {
  height: 48px;
  font-size: 16px;
  font-weight: bold;
  border-radius: 8px;
}

.register-link {
  text-align: center;
  margin-top: 24px;
  color: #666;
  font-size: 14px;
}

.register-link a {
  color: #409eff;
  text-decoration: none;
  font-weight: 500;
}

.register-link a:hover {
  text-decoration: underline;
}
</style>