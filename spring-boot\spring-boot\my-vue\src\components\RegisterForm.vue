<template>
  <div class="register-form">
    <el-form
      ref="registerFormRef"
      :model="registerForm"
      :rules="registerRules"
      label-width="0"
      size="large"
    >
      <!-- 手机号 -->
      <el-form-item prop="phone">
        <el-input
          v-model="registerForm.phone"
          placeholder="请输入手机号"
          prefix-icon="Phone"
          clearable
        />
      </el-form-item>

      <!-- 验证码 -->
      <el-form-item prop="smsCode">
        <div class="sms-input-group">
          <el-input
            v-model="registerForm.smsCode"
            placeholder="请输入验证码"
            prefix-icon="Message"
            clearable
          />
          <el-button
            :disabled="smsCountdown > 0"
            @click="sendSmsCode"
            class="sms-btn"
          >
            {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
          </el-button>
        </div>
      </el-form-item>

      <!-- 密码 -->
      <el-form-item prop="password">
        <el-input
          v-model="registerForm.password"
          type="password"
          placeholder="请设置密码（6-20位）"
          prefix-icon="Lock"
          show-password
          clearable
        />
      </el-form-item>

      <!-- 确认密码 -->
      <el-form-item prop="confirmPassword">
        <el-input
          v-model="registerForm.confirmPassword"
          type="password"
          placeholder="请确认密码"
          prefix-icon="Lock"
          show-password
          clearable
        />
      </el-form-item>

      <!-- 昵称 -->
      <el-form-item prop="nickname">
        <el-input
          v-model="registerForm.nickname"
          placeholder="请输入昵称"
          prefix-icon="User"
          clearable
        />
      </el-form-item>

      <!-- 用户协议 -->
      <div class="agreement">
        <el-checkbox v-model="registerForm.agreed">
          我已阅读并同意
          <a href="#" @click.prevent="showUserAgreement">《用户协议》</a>
          和
          <a href="#" @click.prevent="showPrivacyPolicy">《隐私政策》</a>
        </el-checkbox>
      </div>

      <!-- 注册按钮 -->
      <el-form-item>
        <el-button
          type="primary"
          class="register-btn"
          :loading="loading"
          @click="handleRegister"
          block
        >
          {{ loading ? '注册中...' : '注册' }}
        </el-button>
      </el-form-item>

      <!-- 登录链接 -->
      <div class="login-link">
        已有账号？
        <a href="#" @click="$emit('login')">立即登录</a>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'

const emit = defineEmits(['success', 'login'])
const userStore = useUserStore()

// 响应式数据
const registerFormRef = ref()
const loading = ref(false)
const smsCountdown = ref(0)

// 表单数据
const registerForm = reactive({
  phone: '',
  smsCode: '',
  password: '',
  confirmPassword: '',
  nickname: '',
  agreed: false
})

// 表单验证规则
const registerRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  smsCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '请输入6位数字验证码', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== registerForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '昵称长度为2-20位', trigger: 'blur' }
  ]
}

// 发送短信验证码
const sendSmsCode = async () => {
  if (!registerForm.phone) {
    ElMessage.warning('请先输入手机号')
    return
  }

  if (!/^1[3-9]\d{9}$/.test(registerForm.phone)) {
    ElMessage.warning('请输入正确的手机号')
    return
  }

  try {
    ElMessage.success('验证码已发送')

    // 开始倒计时
    smsCountdown.value = 60
    const timer = setInterval(() => {
      smsCountdown.value--
      if (smsCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)

  } catch (error) {
    ElMessage.error(error.message || '发送验证码失败')
  }
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return

  if (!registerForm.agreed) {
    ElMessage.warning('请先同意用户协议和隐私政策')
    return
  }

  try {
    await registerFormRef.value.validate()
    loading.value = true

    const registerData = {
      phone: registerForm.phone,
      smsCode: registerForm.smsCode,
      password: registerForm.password,
      nickname: registerForm.nickname
    }

    // 调用注册API
    await userStore.register(registerData)

    ElMessage.success('注册成功')
    emit('success')

  } catch (error) {
    ElMessage.error(error.message || '注册失败')
  } finally {
    loading.value = false
  }
}

// 显示用户协议
const showUserAgreement = () => {
  ElMessage.info('用户协议功能开发中...')
}

// 显示隐私政策
const showPrivacyPolicy = () => {
  ElMessage.info('隐私政策功能开发中...')
}
</script>

<style scoped>
.register-form {
  padding: 20px 0;
}

.sms-input-group {
  display: flex;
  gap: 12px;
}

.sms-input-group .el-input {
  flex: 1;
}

.sms-btn {
  white-space: nowrap;
  min-width: 100px;
}

.agreement {
  margin-bottom: 24px;
  font-size: 14px;
  color: #666;
}

.agreement a {
  color: #409eff;
  text-decoration: none;
}

.agreement a:hover {
  text-decoration: underline;
}

.register-btn {
  height: 48px;
  font-size: 16px;
  font-weight: bold;
  border-radius: 8px;
}

.login-link {
  text-align: center;
  margin-top: 24px;
  color: #666;
  font-size: 14px;
}

.login-link a {
  color: #409eff;
  text-decoration: none;
  font-weight: 500;
}

.login-link a:hover {
  text-decoration: underline;
}
</style>