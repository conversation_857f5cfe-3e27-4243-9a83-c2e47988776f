<template>
  <div class="home-page">
    <!-- 导航栏 -->
    <header class="header">
      <div class="container">
        <div class="nav-brand">
          <img src="/logo.png" alt="洗护平台" class="logo" />
          <h1>洗护平台</h1>
        </div>

        <!-- 搜索栏 -->
        <div class="search-bar">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索商家、服务..."
            class="search-input"
            @keyup.enter="handleSearch"
          >
            <template #append>
              <el-button @click="handleSearch" type="primary">
                <el-icon><Search /></el-icon>
              </el-button>
            </template>
          </el-input>
        </div>

        <!-- 导航菜单 -->
        <nav class="nav-menu">
          <router-link to="/" class="nav-link" exact-active-class="active">首页</router-link>
          <router-link to="/services" class="nav-link">全部服务</router-link>
          <router-link to="/merchants" class="nav-link">优质商家</router-link>
          <router-link to="/help" class="nav-link">帮助中心</router-link>
        </nav>

        <!-- 用户操作区 -->
        <div class="nav-actions">
          <template v-if="userStore.isAuthenticated">
            <!-- 已登录状态 -->
            <div class="user-menu">
              <el-dropdown @command="handleUserCommand">
                <div class="user-info">
                  <el-avatar :src="userStore.avatar" :size="32">
                    <el-icon><User /></el-icon>
                  </el-avatar>
                  <span class="username">{{ userStore.nickname || '用户' }}</span>
                  <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="profile">
                      <el-icon><User /></el-icon>
                      个人中心
                    </el-dropdown-item>
                    <el-dropdown-item command="orders">
                      <el-icon><List /></el-icon>
                      我的订单
                    </el-dropdown-item>
                    <el-dropdown-item command="favorites">
                      <el-icon><Star /></el-icon>
                      我的收藏
                    </el-dropdown-item>
                    <el-dropdown-item command="settings">
                      <el-icon><Setting /></el-icon>
                      账户设置
                    </el-dropdown-item>
                    <el-dropdown-item divided command="logout">
                      <el-icon><SwitchButton /></el-icon>
                      退出登录
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
          <template v-else>
            <!-- 未登录状态 -->
            <div class="auth-buttons">
              <el-button @click="showLoginDialog" type="primary" plain>登录</el-button>
              <el-button @click="showRegisterDialog" type="primary">注册</el-button>
            </div>
          </template>
        </div>
      </div>
    </header>

    <!-- 登录对话框 -->
    <el-dialog
      v-model="loginDialogVisible"
      title="用户登录"
      width="400px"
      :close-on-click-modal="false"
    >
      <LoginForm @success="handleLoginSuccess" @register="showRegisterDialog" />
    </el-dialog>

    <!-- 注册对话框 -->
    <el-dialog
      v-model="registerDialogVisible"
      title="用户注册"
      width="400px"
      :close-on-click-modal="false"
    >
      <RegisterForm @success="handleRegisterSuccess" @login="showLoginDialog" />
    </el-dialog>

    <!-- 主要内容 -->
    <main class="main-content">
      <!-- 轮播图区域 -->
      <section class="banner-section">
        <div class="container">
          <div class="banner-wrapper">
            <el-carousel height="400px" indicator-position="outside" autoplay>
              <el-carousel-item v-for="banner in banners" :key="banner.id">
                <div class="banner-item" :style="{ backgroundImage: `url(${banner.image})` }">
                  <div class="banner-content">
                    <h2 class="banner-title">{{ banner.title }}</h2>
                    <p class="banner-subtitle">{{ banner.subtitle }}</p>
                    <el-button
                      type="primary"
                      size="large"
                      @click="handleBannerClick(banner)"
                      class="banner-btn"
                    >
                      {{ banner.buttonText || '立即体验' }}
                    </el-button>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>
      </section>

      <!-- 快速入口 -->
      <section class="quick-entry-section">
        <div class="container">
          <div class="quick-entries">
            <div
              v-for="entry in quickEntries"
              :key="entry.id"
              class="quick-entry-item"
              @click="handleQuickEntry(entry)"
            >
              <div class="entry-icon">
                <el-icon :size="32" :color="entry.color">
                  <component :is="entry.icon" />
                </el-icon>
              </div>
              <span class="entry-label">{{ entry.label }}</span>
            </div>
          </div>
        </div>
      </section>

      <!-- 服务分类 -->
      <section class="categories-section">
        <div class="container">
          <div class="section-header">
            <h3 class="section-title">热门分类</h3>
            <router-link to="/services" class="view-all">查看全部</router-link>
          </div>
          <div class="categories-grid">
            <div
              v-for="category in serviceCategories"
              :key="category.id"
              class="category-card"
              @click="goToCategory(category)"
            >
              <div class="category-image">
                <img :src="category.image" :alt="category.name" />
              </div>
              <div class="category-info">
                <h4 class="category-name">{{ category.name }}</h4>
                <p class="category-desc">{{ category.description }}</p>
                <div class="category-stats">
                  <span class="service-count">{{ category.serviceCount }}个服务</span>
                  <span class="price-range">¥{{ category.minPrice }}-{{ category.maxPrice }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 服务特色 -->
      <section class="features-section">
        <div class="container">
          <h3 class="section-title">为什么选择我们</h3>
          <div class="features-grid">
            <div class="feature-card">
              <div class="feature-icon">
                <el-icon><Truck /></el-icon>
              </div>
              <h4>上门取送</h4>
              <p>专业师傅上门取送，省时省力，让您足不出户享受洗护服务</p>
            </div>
            
            <div class="feature-card">
              <div class="feature-icon">
                <el-icon><Lock /></el-icon>
              </div>
              <h4>品质保障</h4>
              <p>严格的质量控制体系，专业设备和技术，确保衣物安全无损</p>
            </div>
            
            <div class="feature-card">
              <div class="feature-icon">
                <el-icon><Timer /></el-icon>
              </div>
              <h4>快速便捷</h4>
              <p>24小时内完成服务，急件可当日取送，满足您的紧急需求</p>
            </div>
            
            <div class="feature-card">
              <div class="feature-icon">
                <el-icon><Star /></el-icon>
              </div>
              <h4>专业团队</h4>
              <p>经验丰富的洗护师傅，针对不同面料提供专业的护理方案</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 热门商家 -->
      <section class="merchants-section" v-if="popularMerchants.length > 0">
        <div class="container">
          <div class="section-header">
            <h3 class="section-title">热门商家</h3>
            <router-link to="/merchants" class="view-all">查看更多</router-link>
          </div>
          <div class="merchants-grid">
            <div
              v-for="merchant in popularMerchants"
              :key="merchant.id"
              class="merchant-card"
              @click="goToMerchant(merchant.id)"
            >
              <div class="merchant-image">
                <el-avatar 
                  :src="merchant.avatar" 
                  :alt="merchant.name"
                  :size="180"
                  shape="square"
                  fit="cover"
                >
                  <el-icon><Shop /></el-icon>
                </el-avatar>
                <div class="merchant-status" :class="{ online: merchant.isOnline }">
                  {{ merchant.isOnline ? '营业中' : '休息中' }}
                </div>
              </div>
              <div class="merchant-info">
                <h4 class="merchant-name">{{ merchant.name }}</h4>
                <div class="merchant-rating">
                  <el-rate v-model="merchant.rating" disabled size="small" />
                  <span class="rating-text">{{ merchant.rating }}</span>
                </div>
                <div class="merchant-distance">距离 {{ merchant.distance }}km</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 推荐服务 -->
      <section class="services-section" v-if="recommendedServices.length > 0">
        <div class="container">
          <div class="section-header">
            <h3 class="section-title">推荐服务</h3>
            <router-link to="/services?type=recommended" class="view-all">查看更多</router-link>
          </div>
          <div class="services-grid">
            <div
              v-for="service in recommendedServices"
              :key="service.id"
              class="service-card"
              @click="goToServiceDetail(service.id)"
            >
              <div class="service-image">
                <img :src="getServiceImage(service)" :alt="service.title" />
                <div v-if="service.hasDiscount" class="discount-badge">
                  {{ calculateDiscount(service) }}折
                </div>
                <div v-if="service.isRecommended" class="recommended-badge">推荐</div>
              </div>
              <div class="service-content">
                <h4 class="service-title">{{ service.title }}</h4>
                <p class="service-description">{{ service.description }}</p>
                <div class="service-meta">
                  <span class="merchant-name">{{ service.merchantName }}</span>
                  <span class="service-category">{{ getCategoryLabel(service.category) }}</span>
                </div>
                <div class="service-footer">
                  <div class="price-info">
                    <span class="current-price">¥{{ service.price }}</span>
                    <span v-if="service.originalPrice && service.originalPrice > service.price"
                          class="original-price">¥{{ service.originalPrice }}</span>
                  </div>
                  <div class="rating-info">
                    <el-rate
                      v-model="service.rating"
                      disabled
                      size="small"
                      show-score
                      text-color="#ff9900"
                    />
                    <span class="review-count">({{ service.reviewCount }})</span>
                  </div>
                </div>
                <div class="service-actions">
                  <el-button type="primary" size="small" @click.stop="contactMerchant(service)">
                    咨询商家
                  </el-button>
                  <el-button type="default" size="small" @click.stop="favoriteService(service)">
                    <el-icon><Star /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 热门服务 -->
      <section class="popular-services-section" v-if="popularServices.length > 0">
        <div class="container">
          <div class="section-header">
            <h3 class="section-title">热门服务</h3>
            <router-link to="/services?type=popular" class="view-all">查看更多</router-link>
          </div>
          <div class="services-grid">
            <div
              v-for="service in popularServices"
              :key="service.id"
              class="service-card"
              @click="goToServiceDetail(service.id)"
            >
              <div class="service-image">
                <img :src="getServiceImage(service)" :alt="service.title" />
                <div class="popular-badge">热门</div>
              </div>
              <div class="service-content">
                <h4 class="service-title">{{ service.title }}</h4>
                <p class="service-description">{{ service.description }}</p>
                <div class="service-meta">
                  <span class="merchant-name">{{ service.merchantName }}</span>
                  <span class="order-count">已售{{ service.orderCount }}单</span>
                </div>
                <div class="service-footer">
                  <div class="price-info">
                    <span class="current-price">¥{{ service.price }}</span>
                  </div>
                  <div class="rating-info">
                    <el-rate
                      v-model="service.rating"
                      disabled
                      size="small"
                      show-score
                      text-color="#ff9900"
                    />
                  </div>
                </div>
                <div class="service-actions">
                  <el-button type="primary" size="small" @click.stop="contactMerchant(service)">
                    咨询商家
                  </el-button>
                  <el-button type="default" size="small" @click.stop="favoriteService(service)">
                    <el-icon><Star /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 服务流程 -->
      <section class="process-section">
        <div class="container">
          <h3 class="section-title">服务流程</h3>
          <div class="process-steps">
            <div class="step-item">
              <div class="step-number">1</div>
              <div class="step-content">
                <h4>在线预约</h4>
                <p>选择商家和服务，填写取件地址和时间</p>
              </div>
            </div>
            
            <div class="step-item">
              <div class="step-number">2</div>
              <div class="step-content">
                <h4>上门取件</h4>
                <p>专业师傅按时上门，检查衣物并确认服务</p>
              </div>
            </div>
            
            <div class="step-item">
              <div class="step-number">3</div>
              <div class="step-content">
                <h4>专业洗护</h4>
                <p>使用专业设备和技术，精心护理您的衣物</p>
              </div>
            </div>
            
            <div class="step-item">
              <div class="step-number">4</div>
              <div class="step-content">
                <h4>送货上门</h4>
                <p>洗护完成后，按时送达指定地址</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h4>洗护系统</h4>
            <p>专业的洗护服务平台，为您提供便捷、优质的洗护体验</p>
          </div>
          
          <div class="footer-section">
            <h4>服务支持</h4>
            <ul>
              <li><router-link to="/help">帮助中心</router-link></li>
              <li><router-link to="/contact">联系客服</router-link></li>
              <li><a href="#">服务条款</a></li>
              <li><a href="#">隐私政策</a></li>
            </ul>
          </div>
          
          <div class="footer-section">
            <h4>关于我们</h4>
            <ul>
              <li><a href="#">公司介绍</a></li>
              <li><a href="#">加入我们</a></li>
              <li><a href="#">商家入驻</a></li>
              <li><a href="#">合作伙伴</a></li>
            </ul>
          </div>
          
          <div class="footer-section">
            <h4>联系方式</h4>
            <ul>
              <li>客服热线：400-123-4567</li>
              <li>邮箱：<EMAIL></li>
              <li>地址：北京市朝阳区xxx街道</li>
            </ul>
          </div>
        </div>
        
        <div class="footer-bottom">
          <p>&copy; 2024 洗护系统. All rights reserved.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import {
  Search, User, ArrowDown, List, Star, Setting, SwitchButton,
  Truck, Lock, Timer, Camera, Shop, Washing, Sunny, House,
  ShirtFilled, ShoeFilled
} from '@element-plus/icons-vue'
import { merchantApi, serviceApi } from '@/services/api'
import { chatApi, SERVICE_CATEGORIES } from '@/services/merchantService'
import LoginForm from '@/components/LoginForm.vue'
import RegisterForm from '@/components/RegisterForm.vue'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const searchKeyword = ref('')
const loginDialogVisible = ref(false)
const registerDialogVisible = ref(false)
const popularMerchants = ref([])
const recommendedServices = ref([])
const popularServices = ref([])

// 轮播图数据
const banners = ref([
  {
    id: 1,
    title: '专业洗护服务',
    subtitle: '让您的衣物焕然一新，享受便捷的上门取送服务',
    buttonText: '立即预约',
    image: '/images/banner1.jpg',
    link: '/services'
  },
  {
    id: 2,
    title: '品质保证',
    subtitle: '严格的质量控制，专业的洗护技术',
    buttonText: '了解更多',
    image: '/images/banner2.jpg',
    link: '/about'
  },
  {
    id: 3,
    title: '便民服务',
    subtitle: '24小时在线预约，快速响应您的需求',
    buttonText: '立即体验',
    image: '/images/banner3.jpg',
    link: '/merchants'
  }
])

// 快速入口数据
const quickEntries = ref([
  {
    id: 1,
    label: '衣物清洗',
    icon: 'Washing',
    color: '#409eff',
    link: '/services?category=wash'
  },
  {
    id: 2,
    label: '干洗服务',
    icon: 'Sunny',
    color: '#67c23a',
    link: '/services?category=dry'
  },
  {
    id: 3,
    label: '鞋类护理',
    icon: 'ShoeFilled',
    color: '#e6a23c',
    link: '/services?category=shoes'
  },
  {
    id: 4,
    label: '家纺清洗',
    icon: 'House',
    color: '#f56c6c',
    link: '/services?category=home'
  },
  {
    id: 5,
    label: '上门取送',
    icon: 'Truck',
    color: '#909399',
    link: '/services?type=pickup'
  },
  {
    id: 6,
    label: '急件处理',
    icon: 'Timer',
    color: '#ff4757',
    link: '/services?type=urgent'
  }
])

// 服务分类数据
const serviceCategories = ref([
  {
    id: 1,
    name: '衣物清洗',
    description: '日常衣物专业清洗',
    image: '/images/category-wash.jpg',
    serviceCount: 156,
    minPrice: 15,
    maxPrice: 80,
    category: 'wash'
  },
  {
    id: 2,
    name: '干洗服务',
    description: '高档衣物干洗护理',
    image: '/images/category-dry.jpg',
    serviceCount: 89,
    minPrice: 25,
    maxPrice: 150,
    category: 'dry'
  },
  {
    id: 3,
    name: '鞋类护理',
    description: '各类鞋子清洗保养',
    image: '/images/category-shoes.jpg',
    serviceCount: 67,
    minPrice: 20,
    maxPrice: 100,
    category: 'shoes'
  },
  {
    id: 4,
    name: '家纺清洗',
    description: '床单被套窗帘清洗',
    image: '/images/category-home.jpg',
    serviceCount: 45,
    minPrice: 30,
    maxPrice: 120,
    category: 'home'
  }
])

const fetchPopularMerchants = async () => {
  try {
    const response = await merchantApi.getPopularMerchants()
    popularMerchants.value = response.data?.slice(0, 6) || []
  } catch (error) {
    console.error('获取热门商家失败:', error)
    popularMerchants.value = []
  }
}

// 获取推荐服务
const fetchRecommendedServices = async () => {
  try {
    const response = await serviceApi.getRecommendedServices({ limit: 6 })
    recommendedServices.value = response.data || []
  } catch (error) {
    console.error('获取推荐服务失败:', error)
    recommendedServices.value = []
  }
}

// 获取热门服务
const fetchPopularServices = async () => {
  try {
    const response = await serviceApi.getHotServices({ limit: 6 })
    popularServices.value = response.data || []
  } catch (error) {
    console.error('获取热门服务失败:', error)
    popularServices.value = []
  }
}

const goToMerchant = (merchantId) => {
  router.push(`/merchants/${merchantId}`)
}

const goToServiceDetail = (serviceId) => {
  router.push(`/services/${serviceId}`)
}

const contactMerchant = async (service) => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }

  try {
    // 发送服务咨询消息
    await chatApi.sendServiceInquiry({
      receiverId: service.merchantId,
      content: `我对您的服务"${service.title}"感兴趣，请问有什么优惠吗？`,
      serviceId: service.id
    })
    ElMessage.success('咨询消息已发送')
    router.push('/chat')
  } catch (error) {
    console.error('发送咨询失败:', error)
    ElMessage.error('发送咨询失败')
  }
}

const favoriteService = async (service) => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }

  try {
    // 暂时显示成功消息，后续可以实现收藏功能
    ElMessage.success('收藏成功')
  } catch (error) {
    console.error('收藏失败:', error)
    ElMessage.error('收藏失败')
  }
}

const getServiceImage = (service) => {
  try {
    const images = JSON.parse(service.imageUrls || '[]')
    return images.length > 0 ? images[0] : '/default-service.jpg'
  } catch {
    return '/default-service.jpg'
  }
}

const getCategoryLabel = (category) => {
  const categoryInfo = SERVICE_CATEGORIES[category]
  return categoryInfo ? categoryInfo.label : category
}

const calculateDiscount = (service) => {
  if (!service.hasDiscount || !service.originalPrice || service.originalPrice <= service.price) {
    return 0
  }
  return Math.round((service.price / service.originalPrice) * 10)
}

// 搜索功能
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  router.push(`/search?q=${encodeURIComponent(searchKeyword.value)}`)
}

// 显示登录对话框
const showLoginDialog = () => {
  loginDialogVisible.value = true
  registerDialogVisible.value = false
}

// 显示注册对话框
const showRegisterDialog = () => {
  registerDialogVisible.value = true
  loginDialogVisible.value = false
}

// 登录成功处理
const handleLoginSuccess = () => {
  loginDialogVisible.value = false
  ElMessage.success('登录成功')
}

// 注册成功处理
const handleRegisterSuccess = () => {
  registerDialogVisible.value = false
  ElMessage.success('注册成功')
}

// 用户菜单命令处理
const handleUserCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'orders':
      router.push('/orders')
      break
    case 'favorites':
      router.push('/favorites')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 轮播图点击处理
const handleBannerClick = (banner) => {
  if (banner.link) {
    router.push(banner.link)
  }
}

// 快速入口点击处理
const handleQuickEntry = (entry) => {
  if (!userStore.isAuthenticated && entry.requireAuth !== false) {
    showLoginDialog()
    return
  }
  router.push(entry.link)
}

// 分类点击处理
const goToCategory = (category) => {
  router.push(`/services?category=${category.category}`)
}

const handleLogout = async () => {
  try {
    await userStore.logout()
    ElMessage.success('退出成功')
    router.push('/')
  } catch (error) {
    ElMessage.error('退出失败')
  }
}

onMounted(() => {
  fetchPopularMerchants()
  fetchRecommendedServices()
  fetchPopularServices()
})
</script>

<style scoped>
.home-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 导航栏 */
.header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 70px;
  max-width: 1200px;
  margin: 0 auto;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  height: 40px;
  width: auto;
}

.nav-brand h1 {
  color: #ff6900;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

/* 搜索栏 */
.search-bar {
  flex: 1;
  max-width: 500px;
  margin: 0 40px;
}

.search-input {
  width: 100%;
}

.search-input :deep(.el-input__wrapper) {
  border-radius: 20px 0 0 20px;
  border-right: none;
}

.search-input :deep(.el-input-group__append) {
  border-radius: 0 20px 20px 0;
  background: #ff6900;
  border-color: #ff6900;
}

.search-input :deep(.el-input-group__append .el-button) {
  background: transparent;
  border: none;
  color: #ffffff;
}

.nav-menu {
  display: flex;
  gap: 30px;
}

.nav-link {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
  color: #ff6900;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background: #ff6900;
  border-radius: 2px;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  color: #333;
  padding: 8px 12px;
  border-radius: 20px;
  background: #f8f9fa;
  transition: background 0.3s ease;
}

.user-info:hover {
  background: #e9ecef;
}

/* 用户菜单 */
.user-menu {
  position: relative;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 20px;
  cursor: pointer;
  transition: background 0.3s;
}

.user-info:hover {
  background: #f5f5f5;
}

.username {
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.dropdown-icon {
  color: #999;
  font-size: 12px;
}

.auth-buttons {
  display: flex;
  gap: 12px;
}

.auth-buttons .el-button {
  border-radius: 20px;
  padding: 8px 20px;
  font-weight: 500;
}

/* 主要内容 */
.main-content {
  flex: 1;
}

/* 轮播图区域 */
.banner-section {
  background: #f5f5f5;
  padding: 20px 0;
}

.banner-wrapper {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.banner-item {
  height: 400px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  display: flex;
  align-items: center;
}

.banner-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.1));
}

.banner-content {
  position: relative;
  z-index: 2;
  color: #ffffff;
  padding: 0 60px;
  max-width: 600px;
}

.banner-title {
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 16px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.banner-subtitle {
  font-size: 20px;
  margin-bottom: 32px;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.banner-btn {
  border-radius: 25px;
  padding: 12px 32px;
  font-size: 16px;
  font-weight: bold;
  box-shadow: 0 4px 15px rgba(255, 105, 0, 0.3);
}

/* 快速入口 */
.quick-entry-section {
  background: #ffffff;
  padding: 40px 0;
  border-bottom: 1px solid #f0f0f0;
}

.quick-entries {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.quick-entry-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border-radius: 12px;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-entry-item:hover {
  background: #f0f9ff;
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.entry-icon {
  width: 60px;
  height: 60px;
  background: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.entry-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  text-align: center;
}

/* 服务分类 */
.categories-section {
  background: #ffffff;
  padding: 60px 0;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.category-card {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.category-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #ff6900;
}

.category-image {
  height: 180px;
  overflow: hidden;
}

.category-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.category-card:hover .category-image img {
  transform: scale(1.05);
}

.category-info {
  padding: 20px;
}

.category-name {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 0 0 8px 0;
}

.category-desc {
  font-size: 14px;
  color: #666;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.category-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.service-count {
  color: #999;
}

.price-range {
  color: #ff6900;
  font-weight: bold;
}

/* 英雄区域 - 保留原有样式 */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
}

.hero-section .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-title {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 20px;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 20px;
  margin-bottom: 40px;
  opacity: 0.9;
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: 20px;
}

.hero-image {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.hero-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 20px;
  font-weight: 500;
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  border-radius: 12px;
}

.hero-placeholder p {
  margin-top: 20px;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.btn-primary {
  background: #409eff;
  color: white;
  border-color: #409eff;
}

.btn-primary:hover {
  background: #337ecc;
  border-color: #337ecc;
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  color: #409eff;
  border-color: #409eff;
}

.btn-outline:hover {
  background: #409eff;
  color: white;
  transform: translateY(-2px);
}

.btn-large {
  padding: 16px 32px;
  font-size: 16px;
}

/* 通用样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  font-size: 32px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 50px;
  color: #333;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
}

.view-all {
  color: #409eff;
  text-decoration: none;
  font-weight: 500;
}

.view-all:hover {
  text-decoration: underline;
}

/* 服务特色 */
.features-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.feature-card {
  text-align: center;
  padding: 40px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-8px);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 32px;
  color: white;
}

.feature-card h4 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

/* 热门商家 */
.merchants-section {
  padding: 80px 0;
}

.merchants-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.merchant-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.merchant-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.merchant-image {
  position: relative;
  height: 180px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.merchant-image .el-avatar {
  width: 100%;
  height: 100%;
}

.merchant-status {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
  background: #909399;
}

.merchant-status.online {
  background: #67c23a;
}

.merchant-info {
  padding: 20px;
  text-align: center;
}

.merchant-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.merchant-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
}

.rating-text {
  font-size: 14px;
  color: #666;
}

.merchant-distance {
  font-size: 12px;
  color: #999;
}

/* 服务流程 */
.process-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.step-item {
  text-align: center;
  position: relative;
}

.step-number {
  width: 60px;
  height: 60px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 600;
  margin: 0 auto 20px;
}

.step-content h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.step-content p {
  color: #666;
  line-height: 1.6;
}

/* 页脚 */
.footer {
  background: #2c3e50;
  color: white;
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #409eff;
}

.footer-section p {
  color: #bdc3c7;
  line-height: 1.6;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section li {
  margin-bottom: 8px;
}

.footer-section a {
  color: #bdc3c7;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: #409eff;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #34495e;
  color: #95a5a6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header .container {
    flex-direction: column;
    height: auto;
    padding: 15px 20px;
  }
  
  .nav-menu {
    margin: 15px 0;
  }
  
  .hero-section .container {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
  
  .hero-title {
    font-size: 36px;
  }
  
  .hero-actions {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .merchants-grid {
    grid-template-columns: 1fr;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .process-steps {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }
}

/* 服务展示 */
.services-section,
.popular-services-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.popular-services-section {
  background: white;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
}

.service-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.service-image {
  position: relative;
  height: 200px;
  overflow: hidden;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.service-card:hover .service-image img {
  transform: scale(1.05);
}

.discount-badge,
.recommended-badge,
.popular-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: #f56c6c;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.recommended-badge {
  background: #409eff;
}

.popular-badge {
  background: #e6a23c;
}

.service-content {
  padding: 20px;
}

.service-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
  line-height: 1.4;
}

.service-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 12px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.service-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 12px;
  color: #999;
}

.service-meta .merchant-name {
  font-weight: 500;
  color: #409eff;
}

.service-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.price-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-price {
  font-size: 18px;
  font-weight: 600;
  color: #f56c6c;
}

.original-price {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
}

.rating-info {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.review-count {
  color: #999;
}

.service-actions {
  display: flex;
  gap: 8px;
}

.service-actions .el-button {
  flex: 1;
}
</style>