<template>
  <div class="complaint-detail-container">
    <div v-loading="loading" class="detail-content">
      <!-- 返回按钮 -->
      <div class="back-button">
        <el-button @click="goBack" text>
          <el-icon><ArrowLeft /></el-icon>
          返回投诉列表
        </el-button>
      </div>

      <div v-if="complaint" class="complaint-detail">
        <!-- 投诉基本信息 -->
        <div class="complaint-header">
          <div class="header-left">
            <h1 class="complaint-title">{{ complaint.title }}</h1>
            <div class="complaint-meta">
              <span class="meta-item">投诉编号：{{ complaint.id }}</span>
              <span class="meta-item">订单号：{{ complaint.orderId }}</span>
              <span class="meta-item">投诉类型：{{ complaint.typeText }}</span>
              <span class="meta-item">提交时间：{{ formatTime(complaint.createTime) }}</span>
            </div>
          </div>
          <div class="header-right">
            <el-tag 
              :type="getStatusType(complaint.status)"
              size="large"
              class="status-tag"
            >
              {{ getStatusText(complaint.status) }}
            </el-tag>
          </div>
        </div>

        <!-- 投诉内容 -->
        <div class="complaint-content">
          <h3>投诉内容</h3>
          <div class="content-text">{{ complaint.content }}</div>
          
          <!-- 投诉图片 -->
          <div v-if="complaint.images && complaint.images.length > 0" class="complaint-images">
            <h4>相关图片</h4>
            <div class="image-gallery">
              <el-image
                v-for="(image, index) in complaint.images"
                :key="index"
                :src="image"
                :preview-src-list="complaint.images"
                :initial-index="index"
                class="complaint-image"
                fit="cover"
              />
            </div>
          </div>
        </div>

        <!-- 处理进度 -->
        <div class="complaint-progress">
          <h3>处理进度</h3>
          <el-timeline>
            <el-timeline-item
              v-for="(step, index) in progressSteps"
              :key="index"
              :timestamp="step.time"
              :type="step.type"
            >
              <div class="progress-content">
                <div class="progress-title">{{ step.title }}</div>
                <div v-if="step.content" class="progress-description">{{ step.content }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>

        <!-- 官方回复 -->
        <div v-if="complaint.reply" class="complaint-reply">
          <h3>官方回复</h3>
          <div class="reply-content">
            <div class="reply-header">
              <span class="reply-author">客服回复</span>
              <span class="reply-time">{{ formatTime(complaint.replyTime) }}</span>
            </div>
            <div class="reply-text">{{ complaint.reply }}</div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="complaint-actions">
          <el-button 
            v-if="complaint.status === 'pending'"
            type="danger"
            @click="cancelComplaint"
          >
            撤销投诉
          </el-button>
          
          <el-button 
            v-if="complaint.status === 'processing'"
            type="primary"
            @click="showSupplementDialog = true"
          >
            补充信息
          </el-button>
          
          <el-button 
            v-if="complaint.status === 'resolved'"
            type="success"
            @click="confirmResolution"
          >
            确认解决
          </el-button>
        </div>
      </div>
    </div>

    <!-- 补充信息对话框 -->
    <el-dialog
      v-model="showSupplementDialog"
      title="补充投诉信息"
      width="600px"
    >
      <el-form
        ref="supplementFormRef"
        :model="supplementForm"
        :rules="supplementRules"
        label-width="100px"
      >
        <el-form-item label="补充内容" prop="content">
          <el-input
            v-model="supplementForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入需要补充的信息"
          />
        </el-form-item>
        
        <el-form-item label="补充图片">
          <el-upload
            v-model:file-list="supplementForm.images"
            action="#"
            list-type="picture-card"
            :auto-upload="false"
            :limit="3"
            accept="image/*"
          >
            <el-icon><Plus /></el-icon>
            <template #tip>
              <div class="el-upload__tip">
                最多上传3张图片
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showSupplementDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            :loading="supplementing"
            @click="submitSupplement"
          >
            提交补充
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Plus } from '@element-plus/icons-vue'
import { complaintApi } from '@/api/complaint'
import { formatTime } from '@/utils/date'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const complaint = ref(null)
const progressSteps = ref([])
const showSupplementDialog = ref(false)
const supplementing = ref(false)
const supplementFormRef = ref()

// 补充信息表单
const supplementForm = reactive({
  content: '',
  images: []
})

// 补充信息验证规则
const supplementRules = {
  content: [
    { required: true, message: '请输入补充内容', trigger: 'blur' },
    { min: 10, max: 500, message: '内容长度在10到500个字符', trigger: 'blur' }
  ]
}

// 生命周期
onMounted(() => {
  fetchComplaintDetail()
})

// 方法定义
const fetchComplaintDetail = async () => {
  try {
    loading.value = true
    const complaintId = route.params.id
    
    const response = await complaintApi.getComplaintDetail(complaintId)
    complaint.value = response.data
    
    // 构建进度步骤
    buildProgressSteps()
    
  } catch (error) {
    console.error('获取投诉详情失败:', error)
    ElMessage.error('获取投诉详情失败')
  } finally {
    loading.value = false
  }
}

// 构建进度步骤
const buildProgressSteps = () => {
  const steps = [
    {
      title: '投诉已提交',
      content: '您的投诉已成功提交，我们会尽快处理',
      time: complaint.value.createTime,
      type: 'primary'
    }
  ]
  
  if (complaint.value.status !== 'pending') {
    steps.push({
      title: '投诉处理中',
      content: '客服正在处理您的投诉',
      time: complaint.value.processTime || complaint.value.createTime,
      type: 'primary'
    })
  }
  
  if (complaint.value.status === 'resolved') {
    steps.push({
      title: '投诉已解决',
      content: complaint.value.reply,
      time: complaint.value.replyTime,
      type: 'success'
    })
  } else if (complaint.value.status === 'closed') {
    steps.push({
      title: '投诉已关闭',
      content: '投诉处理完成',
      time: complaint.value.closeTime,
      type: 'info'
    })
  }
  
  progressSteps.value = steps
}

// 返回列表
const goBack = () => {
  router.push('/complaint')
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    pending: 'warning',
    processing: 'primary',
    resolved: 'success',
    closed: 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: '待处理',
    processing: '处理中',
    resolved: '已解决',
    closed: '已关闭'
  }
  return statusMap[status] || '未知'
}

// 撤销投诉
const cancelComplaint = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要撤销这个投诉吗？撤销后无法恢复。',
      '确认撤销',
      {
        confirmButtonText: '确定撤销',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await complaintApi.cancelComplaint(complaint.value.id)
    ElMessage.success('投诉已撤销')
    
    // 刷新详情
    fetchComplaintDetail()
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('撤销失败，请重试')
    }
  }
}

// 提交补充信息
const submitSupplement = async () => {
  try {
    await supplementFormRef.value.validate()
    supplementing.value = true
    
    // 上传图片
    const imageUrls = []
    for (const file of supplementForm.images) {
      if (file.raw) {
        try {
          const uploadResponse = await complaintApi.uploadImage(file.raw)
          imageUrls.push(uploadResponse.data.url)
        } catch (error) {
          console.error('图片上传失败:', error)
        }
      }
    }
    
    // 提交补充信息
    const supplementData = {
      content: supplementForm.content,
      images: imageUrls
    }
    
    await complaintApi.supplementComplaint(complaint.value.id, supplementData)
    
    ElMessage.success('补充信息提交成功')
    showSupplementDialog.value = false
    
    // 重置表单
    supplementForm.content = ''
    supplementForm.images = []
    
    // 刷新详情
    fetchComplaintDetail()
    
  } catch (error) {
    if (error !== 'validation failed') {
      ElMessage.error('提交失败，请重试')
    }
  } finally {
    supplementing.value = false
  }
}

// 确认解决
const confirmResolution = async () => {
  try {
    await ElMessageBox.confirm(
      '确认投诉已解决？确认后投诉将被关闭。',
      '确认解决',
      {
        confirmButtonText: '确认解决',
        cancelButtonText: '取消',
        type: 'success'
      }
    )
    
    await complaintApi.confirmResolution(complaint.value.id)
    ElMessage.success('感谢您的确认')
    
    // 刷新详情
    fetchComplaintDetail()
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败，请重试')
    }
  }
}
</script>

<style scoped>
.complaint-detail-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.back-button {
  margin-bottom: 20px;
}

.complaint-detail {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.complaint-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.complaint-title {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.complaint-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.meta-item {
  font-size: 14px;
  color: #909399;
}

.status-tag {
  font-size: 14px;
  padding: 8px 16px;
}

.complaint-content,
.complaint-progress,
.complaint-reply {
  margin-bottom: 24px;
}

.complaint-content h3,
.complaint-progress h3,
.complaint-reply h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.content-text {
  line-height: 1.8;
  color: #606266;
  margin-bottom: 16px;
}

.complaint-images h4 {
  margin: 16px 0 12px 0;
  font-size: 16px;
  color: #303133;
}

.image-gallery {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.complaint-image {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  cursor: pointer;
}

.progress-content {
  margin-bottom: 8px;
}

.progress-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.progress-description {
  color: #606266;
  line-height: 1.6;
}

.reply-content {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.reply-author {
  font-weight: 600;
  color: #409eff;
}

.reply-time {
  font-size: 14px;
  color: #909399;
}

.reply-text {
  line-height: 1.8;
  color: #606266;
}

.complaint-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
