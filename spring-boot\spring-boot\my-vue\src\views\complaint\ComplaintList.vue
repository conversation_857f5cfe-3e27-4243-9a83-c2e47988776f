<template>
  <div class="complaint-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">我的投诉</h1>
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建投诉
        </el-button>
      </div>
      
      <!-- 状态筛选 -->
      <div class="filter-section">
        <el-tabs v-model="activeTab" @tab-change="handleTabChange">
          <el-tab-pane label="全部" name="all"></el-tab-pane>
          <el-tab-pane label="待处理" name="pending"></el-tab-pane>
          <el-tab-pane label="处理中" name="processing"></el-tab-pane>
          <el-tab-pane label="已解决" name="resolved"></el-tab-pane>
          <el-tab-pane label="已关闭" name="closed"></el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 投诉列表 -->
    <div class="complaint-list" v-loading="loading">
      <div v-if="complaints.length === 0 && !loading" class="empty-state">
        <el-empty description="暂无投诉记录">
          <el-button type="primary" @click="showCreateDialog = true">新建投诉</el-button>
        </el-empty>
      </div>
      
      <div v-else>
        <div 
          v-for="complaint in complaints" 
          :key="complaint.id" 
          class="complaint-item"
          @click="viewComplaintDetail(complaint.id)"
        >
          <div class="complaint-header">
            <h3 class="complaint-title">{{ complaint.title }}</h3>
            <el-tag 
              :type="getStatusType(complaint.status)"
              class="complaint-status"
            >
              {{ getStatusText(complaint.status) }}
            </el-tag>
          </div>
          
          <div class="complaint-info">
            <span class="complaint-type">{{ complaint.typeText }}</span>
            <span class="complaint-order">订单号：{{ complaint.orderId }}</span>
          </div>
          
          <div class="complaint-content">{{ complaint.content }}</div>
          
          <div class="complaint-footer">
            <span class="complaint-time">{{ formatTime(complaint.createTime) }}</span>
            <div v-if="complaint.status === 'resolved' && complaint.reply" class="has-reply">
              <el-icon><ChatDotRound /></el-icon>
              已回复
            </div>
          </div>
        </div>
        
        <!-- 加载更多 -->
        <div v-if="hasMore" class="load-more">
          <el-button 
            :loading="loadingMore" 
            @click="loadMore"
            text
          >
            {{ loadingMore ? '加载中...' : '加载更多' }}
          </el-button>
        </div>
        
        <div v-else-if="complaints.length > 0" class="no-more">
          没有更多了
        </div>
      </div>
    </div>

    <!-- 新建投诉对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新建投诉"
      width="600px"
      :before-close="handleCloseDialog"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="100px"
      >
        <el-form-item label="订单号" prop="orderId">
          <el-input
            v-model="createForm.orderId"
            placeholder="请输入订单号"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="投诉类型" prop="type">
          <el-select v-model="createForm.type" placeholder="请选择投诉类型">
            <el-option
              v-for="option in typeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="投诉标题" prop="title">
          <el-input
            v-model="createForm.title"
            placeholder="请输入投诉标题"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="投诉内容" prop="content">
          <el-input
            v-model="createForm.content"
            type="textarea"
            :rows="4"
            placeholder="请详细描述您遇到的问题"
          />
        </el-form-item>
        
        <el-form-item label="相关图片">
          <el-upload
            v-model:file-list="createForm.images"
            action="#"
            list-type="picture-card"
            :auto-upload="false"
            :limit="3"
            accept="image/*"
            @change="handleImageChange"
          >
            <el-icon><Plus /></el-icon>
            <template #tip>
              <div class="el-upload__tip">
                最多上传3张图片，支持jpg、png格式
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseDialog">取消</el-button>
          <el-button 
            type="primary" 
            :loading="submitting"
            @click="submitComplaint"
          >
            提交投诉
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, ChatDotRound } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { complaintApi } from '@/api/complaint'
import { formatTime } from '@/utils/date'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const loadingMore = ref(false)
const hasMore = ref(true)
const page = ref(1)
const pageSize = ref(10)
const activeTab = ref('all')
const complaints = ref([])

// 新建投诉相关
const showCreateDialog = ref(false)
const submitting = ref(false)
const createFormRef = ref()

// 投诉类型选项
const typeOptions = [
  { value: 'service', label: '服务质量' },
  { value: 'delivery', label: '配送问题' },
  { value: 'price', label: '价格争议' },
  { value: 'attitude', label: '服务态度' },
  { value: 'other', label: '其他问题' }
]

// 新建投诉表单
const createForm = reactive({
  orderId: '',
  type: '',
  title: '',
  content: '',
  images: []
})

// 表单验证规则
const createRules = {
  orderId: [
    { required: true, message: '请输入订单号', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择投诉类型', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入投诉标题', trigger: 'blur' },
    { min: 5, max: 50, message: '标题长度在5到50个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入投诉内容', trigger: 'blur' },
    { min: 10, max: 500, message: '内容长度在10到500个字符', trigger: 'blur' }
  ]
}

// 生命周期
onMounted(() => {
  fetchComplaints()
})

// 方法定义
const fetchComplaints = async () => {
  try {
    loading.value = true
    
    const params = {
      page: page.value,
      pageSize: pageSize.value,
      status: activeTab.value === 'all' ? undefined : activeTab.value
    }

    const response = await complaintApi.getComplaints(params)
    const { data, total } = response.data
    
    if (page.value === 1) {
      complaints.value = data
    } else {
      complaints.value.push(...data)
    }
    
    hasMore.value = complaints.value.length < total
  } catch (error) {
    console.error('获取投诉列表失败:', error)
    ElMessage.error('获取投诉列表失败')
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 切换标签
const handleTabChange = (tab) => {
  activeTab.value = tab
  page.value = 1
  hasMore.value = true
  fetchComplaints()
}

// 加载更多
const loadMore = () => {
  if (hasMore.value && !loadingMore.value) {
    loadingMore.value = true
    page.value++
    fetchComplaints()
  }
}

// 查看投诉详情
const viewComplaintDetail = (id) => {
  router.push(`/complaint/detail/${id}`)
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    pending: 'warning',
    processing: 'primary',
    resolved: 'success',
    closed: 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: '待处理',
    processing: '处理中',
    resolved: '已解决',
    closed: '已关闭'
  }
  return statusMap[status] || '未知'
}

// 处理图片变化
const handleImageChange = (file, fileList) => {
  createForm.images = fileList
}

// 关闭对话框
const handleCloseDialog = () => {
  if (submitting.value) {
    ElMessage.warning('正在提交中，请稍候...')
    return
  }
  
  showCreateDialog.value = false
  resetCreateForm()
}

// 重置表单
const resetCreateForm = () => {
  createFormRef.value?.resetFields()
  Object.assign(createForm, {
    orderId: '',
    type: '',
    title: '',
    content: '',
    images: []
  })
}

// 提交投诉
const submitComplaint = async () => {
  try {
    await createFormRef.value.validate()
    submitting.value = true
    
    // 上传图片
    const imageUrls = []
    for (const file of createForm.images) {
      if (file.raw) {
        try {
          const uploadResponse = await complaintApi.uploadImage(file.raw)
          imageUrls.push(uploadResponse.data.url)
        } catch (error) {
          console.error('图片上传失败:', error)
        }
      }
    }
    
    // 提交投诉
    const complaintData = {
      orderId: createForm.orderId,
      type: createForm.type,
      title: createForm.title,
      content: createForm.content,
      images: imageUrls
    }
    
    await complaintApi.createComplaint(complaintData)
    
    ElMessage.success('投诉提交成功')
    showCreateDialog.value = false
    resetCreateForm()
    
    // 刷新列表
    page.value = 1
    fetchComplaints()
    
  } catch (error) {
    if (error !== 'validation failed') {
      ElMessage.error('提交失败，请重试')
    }
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.complaint-list-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.filter-section {
  border-bottom: 1px solid #e4e7ed;
}

.complaint-list {
  min-height: 400px;
}

.complaint-item {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.complaint-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.complaint-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.complaint-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.complaint-info {
  display: flex;
  gap: 20px;
  margin-bottom: 12px;
  font-size: 14px;
  color: #909399;
}

.complaint-content {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 12px;
}

.complaint-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #909399;
}

.has-reply {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #67c23a;
}

.load-more, .no-more {
  text-align: center;
  padding: 20px;
  color: #909399;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
