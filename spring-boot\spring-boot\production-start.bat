@echo off
chcp 65001 >nul
echo ========================================
echo 🚀 洗护平台生产环境启动脚本
echo ========================================
echo.

:: 检查Java环境
echo [1/8] 检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java环境未安装或配置错误
    pause
    exit /b 1
)
echo ✅ Java环境检查通过

:: 检查Node.js环境
echo.
echo [2/8] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js环境未安装或配置错误
    pause
    exit /b 1
)
echo ✅ Node.js环境检查通过

:: 检查MySQL连接
echo.
echo [3/8] 检查MySQL数据库连接...
mysql -u root -p123456 -e "SELECT 1;" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ MySQL数据库连接失败，请检查数据库服务
    pause
    exit /b 1
)
echo ✅ 数据库连接正常

:: 编译后端项目
echo.
echo [4/8] 编译后端项目...
echo 编译管理后端 (Spring-boot-vue)...
cd spring-boot\Spring-boot-vue
call mvn clean package -DskipTests -q
if %errorlevel% neq 0 (
    echo ❌ 管理后端编译失败
    pause
    exit /b 1
)
echo ✅ 管理后端编译成功

cd ..\..
echo 编译用户后端 (spring-boot-1)...
cd spring-boot\spring-boot-1
call mvn clean package -DskipTests -q
if %errorlevel% neq 0 (
    echo ❌ 用户后端编译失败
    pause
    exit /b 1
)
echo ✅ 用户后端编译成功

cd ..\..
echo 编译商户后端 (spring-boot2)...
cd spring-boot\spring-boot2
call mvn clean package -DskipTests -q
if %errorlevel% neq 0 (
    echo ❌ 商户后端编译失败
    pause
    exit /b 1
)
echo ✅ 商户后端编译成功

:: 构建前端项目
cd ..\..
echo.
echo [5/8] 构建前端项目...
echo 构建用户前端 (my-vue)...
cd spring-boot\my-vue
call npm install --silent
call npm run build
if %errorlevel% neq 0 (
    echo ❌ 用户前端构建失败
    pause
    exit /b 1
)
echo ✅ 用户前端构建成功

cd ..\..
echo 构建商户前端 (merchant-app)...
cd spring-boot\merchant-app
call npm install --silent
call npm run build
if %errorlevel% neq 0 (
    echo ❌ 商户前端构建失败
    pause
    exit /b 1
)
echo ✅ 商户前端构建成功

cd ..\..
echo 构建管理前端 (spring.application.name)...
cd spring-boot\spring.application.name
call npm install --silent
call npm run build
if %errorlevel% neq 0 (
    echo ❌ 管理前端构建失败
    pause
    exit /b 1
)
echo ✅ 管理前端构建成功

:: 启动后端服务
cd ..\..
echo.
echo [6/8] 启动后端服务...
echo.
echo ========================================
echo 正在启动洗护平台生产环境...
echo ========================================
echo.
echo 服务端口分配:
echo - 管理后端: http://localhost:8080
echo - 用户后端: http://localhost:8081  
echo - 商户后端: http://localhost:8082
echo - 用户前端: http://localhost:5173
echo - 商户前端: http://localhost:5174
echo - 管理前端: http://localhost:5175
echo.

:: 启动后端服务
echo 启动管理后端服务 (端口: 8080)...
start "管理后端" cmd /k "cd spring-boot\Spring-boot-vue && java -jar -Dspring.profiles.active=prod target\Spring-boot-vue-0.0.1-SNAPSHOT.jar"
timeout /t 5 /nobreak >nul

echo 启动用户后端服务 (端口: 8081)...
start "用户后端" cmd /k "cd spring-boot\spring-boot-1 && java -jar -Dspring.profiles.active=prod target\laundry-care-backend-1.0.0.jar"
timeout /t 5 /nobreak >nul

echo 启动商户后端服务 (端口: 8082)...
start "商户后端" cmd /k "cd spring-boot\spring-boot2 && java -jar -Dspring.profiles.active=prod target\spring-boot2-0.0.1-SNAPSHOT.jar"
timeout /t 5 /nobreak >nul

:: 等待后端服务启动
echo.
echo [7/8] 等待后端服务启动 (60秒)...
timeout /t 60 /nobreak >nul

:: 启动前端服务
echo.
echo [8/8] 启动前端服务...
echo 启动用户前端服务 (端口: 5173)...
start "用户前端" cmd /k "cd spring-boot\my-vue && npm run preview"
timeout /t 3 /nobreak >nul

echo 启动商户前端服务 (端口: 5174)...
start "商户前端" cmd /k "cd spring-boot\merchant-app && npm run preview"
timeout /t 3 /nobreak >nul

echo 启动管理前端服务 (端口: 5175)...
start "管理前端" cmd /k "cd spring-boot\spring.application.name && npm run preview"

echo.
echo ========================================
echo ✅ 生产环境启动完成！
echo ========================================
echo.
echo 🌐 访问地址:
echo 👤 用户端: http://localhost:5173
echo 🏪 商户端: http://localhost:5174  
echo 👨‍💼 管理端: http://localhost:5175
echo.
echo 📊 后端API:
echo 🔧 管理API: http://localhost:8080
echo 👤 用户API: http://localhost:8081
echo 🏪 商户API: http://localhost:8082
echo.
echo 🔐 超级管理员账号:
echo 用户名: superadmin
echo 密码: super123
echo 可在所有三个系统中登录
echo.
echo 💡 提示:
echo - 生产环境已启用，请确保数据库已正确配置
echo - 如需查看日志，请检查各服务窗口
echo - 建议配置反向代理和SSL证书
echo - 定期备份数据库和重要文件
echo.
echo 按任意键退出...
pause >nul
