-- 洗护平台定价策略和优惠活动数据

-- 定价策略表
INSERT INTO pricing_strategies (id, name, description, strategy_type, discount_type, discount_value, min_quantity, max_quantity, start_date, end_date, status, created_at) VALUES
-- 数量优惠策略
(1, '衣物洗护批量优惠', '衣物洗护服务批量优惠', 'QUANTITY', 'PERCENTAGE', 10.00, 5, 10, '2024-01-01', '2024-12-31', 'ACTIVE', NOW()),
(2, '衣物洗护大批量优惠', '衣物洗护服务大批量优惠', 'QUANTITY', 'PERCENTAGE', 15.00, 11, 20, '2024-01-01', '2024-12-31', 'ACTIVE', NOW()),
(3, '衣物洗护超大批量优惠', '衣物洗护服务超大批量优惠', 'QUANTITY', 'PERCENTAGE', 20.00, 21, 999, '2024-01-01', '2024-12-31', 'ACTIVE', NOW()),

-- 会员等级优惠策略
(4, '银卡会员优惠', '银卡会员专享优惠', 'MEMBERSHIP', 'PERCENTAGE', 5.00, 1, 999, '2024-01-01', '2024-12-31', 'ACTIVE', NOW()),
(5, '金卡会员优惠', '金卡会员专享优惠', 'MEMBERSHIP', 'PERCENTAGE', 10.00, 1, 999, '2024-01-01', '2024-12-31', 'ACTIVE', NOW()),
(6, '钻石会员优惠', '钻石会员专享优惠', 'MEMBERSHIP', 'PERCENTAGE', 15.00, 1, 999, '2024-01-01', '2024-12-31', 'ACTIVE', NOW()),

-- 首次消费优惠
(7, '新用户首单优惠', '新用户首次消费优惠', 'FIRST_ORDER', 'PERCENTAGE', 20.00, 1, 1, '2024-01-01', '2024-12-31', 'ACTIVE', NOW()),
(8, '新用户奢侈品首单优惠', '新用户奢侈品护理首次消费优惠', 'FIRST_ORDER', 'FIXED', 50.00, 1, 1, '2024-01-01', '2024-12-31', 'ACTIVE', NOW()),

-- 节假日优惠策略
(9, '春节特惠', '春节期间特别优惠', 'HOLIDAY', 'PERCENTAGE', 15.00, 1, 999, '2024-02-08', '2024-02-18', 'ACTIVE', NOW()),
(10, '五一劳动节优惠', '五一劳动节优惠活动', 'HOLIDAY', 'PERCENTAGE', 12.00, 1, 999, '2024-05-01', '2024-05-05', 'ACTIVE', NOW()),
(11, '国庆节优惠', '国庆节优惠活动', 'HOLIDAY', 'PERCENTAGE', 12.00, 1, 999, '2024-10-01', '2024-10-07', 'ACTIVE', NOW()),

-- 满额优惠策略
(12, '满100减10', '单笔订单满100元减10元', 'AMOUNT', 'FIXED', 10.00, 100, 999999, '2024-01-01', '2024-12-31', 'ACTIVE', NOW()),
(13, '满200减25', '单笔订单满200元减25元', 'AMOUNT', 'FIXED', 25.00, 200, 999999, '2024-01-01', '2024-12-31', 'ACTIVE', NOW()),
(14, '满500减70', '单笔订单满500元减70元', 'AMOUNT', 'FIXED', 70.00, 500, 999999, '2024-01-01', '2024-12-31', 'ACTIVE', NOW()),

-- 特殊服务优惠
(15, '上门服务优惠', '上门服务专享优惠', 'SERVICE_TYPE', 'PERCENTAGE', 8.00, 1, 999, '2024-01-01', '2024-12-31', 'ACTIVE', NOW()),
(16, '深夜服务优惠', '深夜时段服务优惠', 'TIME_BASED', 'PERCENTAGE', 5.00, 1, 999, '2024-01-01', '2024-12-31', 'ACTIVE', NOW());

-- 优惠券模板表
INSERT INTO coupon_templates (id, name, description, coupon_type, discount_type, discount_value, min_order_amount, max_discount_amount, valid_days, total_quantity, per_user_limit, applicable_categories, status, created_at) VALUES
-- 通用优惠券
(1, '新用户专享券', '新用户注册专享优惠券', 'WELCOME', 'PERCENTAGE', 20.00, 50.00, 50.00, 30, 10000, 1, 'ALL', 'ACTIVE', NOW()),
(2, '满减优惠券', '满100减15优惠券', 'DISCOUNT', 'FIXED', 15.00, 100.00, 15.00, 30, 5000, 3, 'ALL', 'ACTIVE', NOW()),
(3, '会员专享券', '会员专享8折优惠券', 'MEMBERSHIP', 'PERCENTAGE', 20.00, 200.00, 100.00, 60, 3000, 2, 'ALL', 'ACTIVE', NOW()),

-- 分类专用优惠券
(4, '衣物洗护券', '衣物洗护专用优惠券', 'CATEGORY', 'PERCENTAGE', 15.00, 80.00, 30.00, 30, 2000, 5, '1', 'ACTIVE', NOW()),
(5, '鞋类护理券', '鞋类护理专用优惠券', 'CATEGORY', 'FIXED', 10.00, 60.00, 10.00, 30, 1500, 3, '2', 'ACTIVE', NOW()),
(6, '汽车洗护券', '汽车洗护专用优惠券', 'CATEGORY', 'PERCENTAGE', 12.00, 100.00, 40.00, 45, 1000, 2, '3', 'ACTIVE', NOW()),
(7, '宠物美容券', '宠物美容专用优惠券', 'CATEGORY', 'FIXED', 20.00, 80.00, 20.00, 30, 800, 3, '4', 'ACTIVE', NOW()),
(8, '奢侈品护理券', '奢侈品护理专用优惠券', 'CATEGORY', 'PERCENTAGE', 10.00, 300.00, 100.00, 60, 500, 1, '10', 'ACTIVE', NOW()),
(9, '个人护理券', '个人护理专用优惠券', 'CATEGORY', 'PERCENTAGE', 18.00, 120.00, 50.00, 30, 1200, 4, '8', 'ACTIVE', NOW()),
(10, '家居清洁券', '家居清洁专用优惠券', 'CATEGORY', 'FIXED', 25.00, 150.00, 25.00, 45, 1000, 2, '9', 'ACTIVE', NOW()),

-- 节假日特殊优惠券
(11, '春节大礼包', '春节期间超值优惠券', 'HOLIDAY', 'PERCENTAGE', 25.00, 200.00, 80.00, 15, 2000, 1, 'ALL', 'ACTIVE', NOW()),
(12, '情人节特惠券', '情人节专属优惠券', 'HOLIDAY', 'FIXED', 30.00, 150.00, 30.00, 7, 1000, 1, '1,5,8', 'ACTIVE', NOW()),
(13, '母亲节关爱券', '母亲节专属优惠券', 'HOLIDAY', 'PERCENTAGE', 20.00, 100.00, 60.00, 10, 1500, 1, '8,12', 'ACTIVE', NOW()),
(14, '双十一狂欢券', '双十一购物狂欢券', 'HOLIDAY', 'PERCENTAGE', 30.00, 300.00, 150.00, 3, 5000, 2, 'ALL', 'ACTIVE', NOW()),

-- 高端服务专用券
(15, '奢侈品专家券', '奢侈品护理专家服务券', 'PREMIUM', 'PERCENTAGE', 8.00, 500.00, 200.00, 90, 200, 1, '10', 'ACTIVE', NOW()),
(16, '古董修复券', '古董文物修复专用券', 'PREMIUM', 'FIXED', 100.00, 800.00, 100.00, 180, 50, 1, '10', 'ACTIVE', NOW()),

-- 组合服务券
(17, '全屋清洁套餐券', '全屋清洁服务套餐券', 'PACKAGE', 'PERCENTAGE', 22.00, 400.00, 150.00, 60, 300, 1, '9', 'ACTIVE', NOW()),
(18, '汽车全套护理券', '汽车全套护理服务券', 'PACKAGE', 'FIXED', 80.00, 350.00, 80.00, 45, 200, 1, '3', 'ACTIVE', NOW()),
(19, '宠物SPA套餐券', '宠物SPA全套服务券', 'PACKAGE', 'PERCENTAGE', 15.00, 250.00, 80.00, 30, 150, 2, '4', 'ACTIVE', NOW()),
(20, '个人护理套餐券', '个人护理全套服务券', 'PACKAGE', 'PERCENTAGE', 20.00, 300.00, 120.00, 60, 100, 1, '8', 'ACTIVE', NOW());

-- 活动促销表
INSERT INTO promotions (id, name, description, promotion_type, start_date, end_date, discount_type, discount_value, min_order_amount, max_discount_amount, applicable_services, target_users, usage_limit, current_usage, status, created_at) VALUES
-- 开业促销活动
(1, '平台开业大酬宾', '平台开业期间全场优惠活动', 'GRAND_OPENING', '2024-01-01', '2024-01-31', 'PERCENTAGE', 25.00, 100.00, 100.00, 'ALL', 'ALL', 10000, 0, 'ACTIVE', NOW()),
(2, '新春开门红', '新春期间特别促销活动', 'SEASONAL', '2024-02-08', '2024-02-25', 'PERCENTAGE', 20.00, 150.00, 80.00, 'ALL', 'ALL', 5000, 0, 'ACTIVE', NOW()),

-- 品类专项促销
(3, '衣物洗护月', '衣物洗护专项促销月', 'CATEGORY_SPECIAL', '2024-03-01', '2024-03-31', 'PERCENTAGE', 18.00, 80.00, 50.00, '1', 'ALL', 3000, 0, 'ACTIVE', NOW()),
(4, '汽车美容节', '汽车美容专项促销节', 'CATEGORY_SPECIAL', '2024-04-01', '2024-04-30', 'FIXED', 50.00, 200.00, 50.00, '3', 'ALL', 2000, 0, 'ACTIVE', NOW()),
(5, '宠物关爱月', '宠物美容护理关爱月', 'CATEGORY_SPECIAL', '2024-05-01', '2024-05-31', 'PERCENTAGE', 22.00, 100.00, 60.00, '4', 'ALL', 1500, 0, 'ACTIVE', NOW()),

-- 会员专享促销
(6, '金卡会员专享', '金卡会员专享促销活动', 'MEMBERSHIP_EXCLUSIVE', '2024-01-01', '2024-12-31', 'PERCENTAGE', 12.00, 200.00, 80.00, 'ALL', 'GOLD_MEMBER', 999999, 0, 'ACTIVE', NOW()),
(7, '钻石会员尊享', '钻石会员尊享促销活动', 'MEMBERSHIP_EXCLUSIVE', '2024-01-01', '2024-12-31', 'PERCENTAGE', 18.00, 300.00, 150.00, 'ALL', 'DIAMOND_MEMBER', 999999, 0, 'ACTIVE', NOW()),

-- 限时抢购活动
(8, '每日限时抢购', '每日限时抢购特价服务', 'FLASH_SALE', '2024-01-01', '2024-12-31', 'PERCENTAGE', 40.00, 50.00, 100.00, 'SELECTED', 'ALL', 100, 0, 'ACTIVE', NOW()),
(9, '周末特惠抢购', '周末特惠限时抢购', 'FLASH_SALE', '2024-01-01', '2024-12-31', 'PERCENTAGE', 35.00, 100.00, 80.00, 'SELECTED', 'ALL', 200, 0, 'ACTIVE', NOW()),

-- 满额赠送活动
(10, '满额送券活动', '满额消费赠送优惠券', 'SPEND_AND_GET', '2024-01-01', '2024-12-31', 'GIFT', 0.00, 300.00, 0.00, 'ALL', 'ALL', 999999, 0, 'ACTIVE', NOW()),
(11, '满额送服务', '满额消费赠送免费服务', 'SPEND_AND_GET', '2024-01-01', '2024-12-31', 'GIFT', 0.00, 500.00, 0.00, 'ALL', 'ALL', 999999, 0, 'ACTIVE', NOW()),

-- 推荐奖励活动
(12, '推荐有礼', '推荐新用户注册奖励活动', 'REFERRAL', '2024-01-01', '2024-12-31', 'FIXED', 20.00, 0.00, 20.00, 'ALL', 'ALL', 999999, 0, 'ACTIVE', NOW()),
(13, '老带新双重奖励', '老用户推荐新用户双重奖励', 'REFERRAL', '2024-01-01', '2024-12-31', 'PERCENTAGE', 10.00, 100.00, 50.00, 'ALL', 'ALL', 999999, 0, 'ACTIVE', NOW()),

-- 节假日特别活动
(14, '情人节甜蜜优惠', '情人节期间甜蜜优惠活动', 'HOLIDAY_SPECIAL', '2024-02-10', '2024-02-20', 'PERCENTAGE', 20.00, 120.00, 60.00, '1,5,8', 'ALL', 2000, 0, 'ACTIVE', NOW()),
(15, '母亲节感恩回馈', '母亲节感恩回馈活动', 'HOLIDAY_SPECIAL', '2024-05-08', '2024-05-15', 'PERCENTAGE', 25.00, 150.00, 80.00, '8,12', 'ALL', 1500, 0, 'ACTIVE', NOW()),
(16, '双十一购物狂欢', '双十一购物狂欢节', 'HOLIDAY_SPECIAL', '2024-11-09', '2024-11-15', 'PERCENTAGE', 35.00, 200.00, 150.00, 'ALL', 'ALL', 10000, 0, 'ACTIVE', NOW());
