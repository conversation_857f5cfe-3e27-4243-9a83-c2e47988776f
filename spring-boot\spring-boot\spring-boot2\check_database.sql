-- 检查数据库和表结构
USE laundry_system;

-- 显示所有表
SHOW TABLES;

-- 检查users表结构
DESCRIBE users;

-- 检查users表数据
SELECT id, username, name, email, phone, role, status, created_time, updated_time 
FROM users 
LIMIT 10;

-- 检查是否有用户名为13900139000的用户
SELECT * FROM users WHERE username = '13900139000';

-- 检查merchants表结构（如果存在）
DESCRIBE merchants;

-- 检查merchants表数据
SELECT id, user_id, shop_name, status, created_time, updated_time 
FROM merchants 
LIMIT 10;
