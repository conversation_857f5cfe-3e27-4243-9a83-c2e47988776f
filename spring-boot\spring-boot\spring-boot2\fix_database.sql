-- 修复数据库问题
USE laundry_system;

-- 删除可能存在的有问题的表
DROP TABLE IF EXISTS merchants;
DROP TABLE IF EXISTS users;

-- 重新创建users表
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    name VARCHAR(100) COMMENT '姓名',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    avatar VARCHAR(500) COMMENT '头像',
    role ENUM('ADMIN', 'MERCHANT', 'CUSTOMER') NOT NULL DEFAULT 'MERCHANT' COMMENT '角色',
    status ENUM('ACTIVE', 'INACTIVE', 'LOCKED', 'DELETED') NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
    last_login_time DATETIME NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人'
) COMMENT '用户表';

-- 重新创建merchants表
CREATE TABLE merchants (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    shop_name VARCHAR(200) NOT NULL COMMENT '店铺名称',
    description TEXT COMMENT '店铺描述',
    address VARCHAR(500) COMMENT '地址',
    province VARCHAR(50) COMMENT '省份',
    city VARCHAR(50) COMMENT '城市',
    district VARCHAR(50) COMMENT '区县',
    latitude DECIMAL(10,6) COMMENT '纬度',
    longitude DECIMAL(10,6) COMMENT '经度',
    business_hours VARCHAR(200) COMMENT '营业时间',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    business_license VARCHAR(100) COMMENT '营业执照',
    id_card_front VARCHAR(500) COMMENT '身份证正面',
    id_card_back VARCHAR(500) COMMENT '身份证背面',
    certification_status ENUM('PENDING', 'APPROVED', 'REJECTED') NOT NULL DEFAULT 'PENDING' COMMENT '认证状态',
    certification_remark VARCHAR(500) COMMENT '认证备注',
    status ENUM('ACTIVE', 'INACTIVE', 'SUSPENDED') NOT NULL DEFAULT 'ACTIVE' COMMENT '商家状态',
    balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '余额',
    service_rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '服务评分',
    total_orders INT DEFAULT 0 COMMENT '总订单数',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    FOREIGN KEY (user_id) REFERENCES users(id)
) COMMENT '商家表';

-- 插入测试用户
INSERT INTO users (username, password, name, email, phone, role, status) VALUES
('13900139000', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfZEKtY30xZHzJ5zKP.H9O1u', '测试商家', '<EMAIL>', '13900139000', 'MERCHANT', 'ACTIVE'),
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfZEKtY30xZHzJ5zKP.H9O1u', '管理员', '<EMAIL>', '13800138000', 'ADMIN', 'ACTIVE');

-- 插入测试商家
INSERT INTO merchants (user_id, shop_name, description, address, province, city, district, contact_phone, certification_status, status) VALUES
(1, '测试洗护店', '专业洗护服务', '北京市朝阳区测试街道123号', '北京市', '北京市', '朝阳区', '13900139000', 'APPROVED', 'ACTIVE');

-- 验证数据
SELECT 'Users:' as table_name;
SELECT id, username, name, role, status, created_time FROM users;

SELECT 'Merchants:' as table_name;
SELECT id, user_id, shop_name, status, created_time FROM merchants;
