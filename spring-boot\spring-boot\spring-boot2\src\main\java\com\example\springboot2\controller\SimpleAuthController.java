package com.example.springboot2.controller;

import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import java.util.Map;
import java.util.HashMap;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 简单认证控制器 - 解决前端登录500错误
 * 提供基础的登录功能，支持超级管理员
 */
@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "*", allowedHeaders = "*", methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.OPTIONS})
public class SimpleAuthController {

    /**
     * 用户登录 - 支持密码登录和短信登录
     */
    @PostMapping("/user/login")
    public ResponseEntity<Map<String, Object>> userLogin(@RequestBody Map<String, Object> loginData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String username = (String) loginData.get("username");
            String password = (String) loginData.get("password");
            String phone = (String) loginData.get("phone");
            String loginType = (String) loginData.get("loginType");
            
            System.out.println("用户登录请求: " + loginData);
            
            // 超级管理员登录检查
            if ("superadmin".equals(username) && "admin123456".equals(password)) {
                return ResponseEntity.ok(createSuperAdminResponse("USER"));
            }
            
            // 模拟用户登录验证
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", 1001L);
            userInfo.put("username", username != null ? username : phone);
            userInfo.put("nickname", "测试用户");
            userInfo.put("phone", phone != null ? phone : "13800138000");
            userInfo.put("avatar", "/images/default-avatar.png");
            userInfo.put("status", "ACTIVE");
            userInfo.put("vipLevel", "NORMAL");
            userInfo.put("balance", 100.00);
            userInfo.put("points", 500);
            
            Map<String, Object> data = new HashMap<>();
            data.put("token", "user_token_" + System.currentTimeMillis());
            data.put("userInfo", userInfo);
            data.put("loginTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            result.put("success", true);
            result.put("message", "登录成功");
            result.put("data", data);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "登录失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 用户注册
     */
    @PostMapping("/user/register")
    public ResponseEntity<Map<String, Object>> userRegister(@RequestBody Map<String, Object> registerData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String phone = (String) registerData.get("phone");
            String password = (String) registerData.get("password");
            String nickname = (String) registerData.get("nickname");
            String smsCode = (String) registerData.get("smsCode");
            
            System.out.println("用户注册请求: " + registerData);
            
            // 模拟注册成功
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", System.currentTimeMillis());
            userInfo.put("username", phone);
            userInfo.put("nickname", nickname);
            userInfo.put("phone", phone);
            userInfo.put("avatar", "/images/default-avatar.png");
            userInfo.put("status", "ACTIVE");
            userInfo.put("vipLevel", "NORMAL");
            userInfo.put("balance", 0.00);
            userInfo.put("points", 100);
            
            Map<String, Object> data = new HashMap<>();
            data.put("token", "user_token_" + System.currentTimeMillis());
            data.put("userInfo", userInfo);
            
            result.put("success", true);
            result.put("message", "注册成功");
            result.put("data", data);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "注册失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 商家登录
     */
    @PostMapping("/merchant/login")
    public ResponseEntity<Map<String, Object>> merchantLogin(@RequestBody Map<String, Object> loginData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String username = (String) loginData.get("username");
            String password = (String) loginData.get("password");
            
            System.out.println("商家登录请求: " + loginData);
            
            // 超级管理员登录检查
            if ("superadmin".equals(username) && "admin123456".equals(password)) {
                return ResponseEntity.ok(createSuperAdminResponse("MERCHANT"));
            }
            
            // 模拟商家登录验证
            Map<String, Object> merchantInfo = new HashMap<>();
            merchantInfo.put("id", 2001L);
            merchantInfo.put("username", username);
            merchantInfo.put("merchantName", "测试洗护店");
            merchantInfo.put("contactName", "张老板");
            merchantInfo.put("phone", "***********");
            merchantInfo.put("email", "<EMAIL>");
            merchantInfo.put("logo", "/images/merchant-logo.png");
            merchantInfo.put("status", "APPROVED");
            merchantInfo.put("businessLicense", "TEST_LICENSE_001");
            merchantInfo.put("address", "测试市测试区测试街道123号");
            merchantInfo.put("description", "专业的洗护服务商家");
            merchantInfo.put("rating", 4.8);
            merchantInfo.put("orderCount", 1250);
            merchantInfo.put("balance", 5680.50);
            merchantInfo.put("commissionRate", 0.05);
            
            Map<String, Object> data = new HashMap<>();
            data.put("token", "merchant_token_" + System.currentTimeMillis());
            data.put("merchantInfo", merchantInfo);
            data.put("loginTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            result.put("success", true);
            result.put("message", "登录成功");
            result.put("data", data);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "登录失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 管理员登录
     */
    @PostMapping("/admin/login")
    public ResponseEntity<Map<String, Object>> adminLogin(@RequestBody Map<String, Object> loginData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String username = (String) loginData.get("username");
            String password = (String) loginData.get("password");
            
            System.out.println("管理员登录请求: " + loginData);
            
            // 超级管理员登录检查
            if ("superadmin".equals(username) && "admin123456".equals(password)) {
                return ResponseEntity.ok(createSuperAdminResponse("ADMIN"));
            }
            
            // 模拟管理员登录验证
            Map<String, Object> adminInfo = new HashMap<>();
            adminInfo.put("id", 3001L);
            adminInfo.put("username", username);
            adminInfo.put("realName", "测试管理员");
            adminInfo.put("phone", "13700137000");
            adminInfo.put("email", "<EMAIL>");
            adminInfo.put("avatar", "/images/admin-avatar.png");
            adminInfo.put("role", "ADMIN");
            adminInfo.put("permissions", new String[]{"user:read", "merchant:read", "order:read", "system:read"});
            adminInfo.put("status", "ACTIVE");
            adminInfo.put("department", "运营部");
            
            Map<String, Object> data = new HashMap<>();
            data.put("token", "admin_token_" + System.currentTimeMillis());
            data.put("adminInfo", adminInfo);
            data.put("loginTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            result.put("success", true);
            result.put("message", "登录成功");
            result.put("data", data);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "登录失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/user/info")
    public ResponseEntity<Map<String, Object>> getUserInfo(@RequestHeader(value = "Authorization", required = false) String authorization) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查是否为超级管理员
            if (authorization != null && authorization.contains("super_admin_token")) {
                return ResponseEntity.ok(createSuperAdminResponse("USER"));
            }
            
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", 1001L);
            userInfo.put("username", "testuser");
            userInfo.put("nickname", "测试用户");
            userInfo.put("phone", "13800138000");
            userInfo.put("avatar", "/images/default-avatar.png");
            userInfo.put("status", "ACTIVE");
            userInfo.put("vipLevel", "NORMAL");
            userInfo.put("balance", 100.00);
            userInfo.put("points", 500);
            
            result.put("success", true);
            result.put("data", userInfo);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取用户信息失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "服务正常");
        result.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return ResponseEntity.ok(result);
    }

    /**
     * 创建超级管理员响应
     */
    private Map<String, Object> createSuperAdminResponse(String userType) {
        Map<String, Object> result = new HashMap<>();
        
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", 999999L);
        userInfo.put("username", "superadmin");
        
        switch (userType) {
            case "USER":
                userInfo.put("nickname", "超级管理员");
                userInfo.put("phone", "***********");
                userInfo.put("avatar", "/images/super-admin-avatar.png");
                userInfo.put("userType", "SUPER_ADMIN");
                userInfo.put("status", "ACTIVE");
                userInfo.put("vipLevel", "DIAMOND");
                userInfo.put("balance", 999999.00);
                userInfo.put("points", 999999);
                break;
            case "MERCHANT":
                userInfo.put("merchantName", "超级管理员商家");
                userInfo.put("contactName", "超级管理员");
                userInfo.put("phone", "***********");
                userInfo.put("email", "<EMAIL>");
                userInfo.put("logo", "/images/super-admin-logo.png");
                userInfo.put("status", "APPROVED");
                userInfo.put("businessLicense", "SUPER_ADMIN_LICENSE");
                userInfo.put("address", "超级管理员地址");
                userInfo.put("description", "超级管理员测试商家");
                userInfo.put("rating", 5.0);
                userInfo.put("orderCount", 99999);
                userInfo.put("balance", 999999.00);
                userInfo.put("commissionRate", 0.00);
                break;
            case "ADMIN":
                userInfo.put("realName", "超级管理员");
                userInfo.put("phone", "***********");
                userInfo.put("email", "<EMAIL>");
                userInfo.put("avatar", "/images/super-admin-avatar.png");
                userInfo.put("role", "SUPER_ADMIN");
                userInfo.put("permissions", new String[]{"*"}); // 所有权限
                userInfo.put("status", "ACTIVE");
                userInfo.put("department", "系统管理部");
                break;
        }
        
        userInfo.put("isSuperAdmin", true);
        userInfo.put("createTime", "2024-01-01 00:00:00");
        userInfo.put("lastLoginTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        Map<String, Object> data = new HashMap<>();
        data.put("token", "super_admin_token_" + userType.toLowerCase() + "_" + System.currentTimeMillis());
        data.put(userType.equals("USER") ? "userInfo" : 
                userType.equals("MERCHANT") ? "merchantInfo" : "adminInfo", userInfo);
        data.put("loginTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        result.put("success", true);
        result.put("message", "超级管理员登录成功");
        result.put("data", data);
        
        return result;
    }
}
