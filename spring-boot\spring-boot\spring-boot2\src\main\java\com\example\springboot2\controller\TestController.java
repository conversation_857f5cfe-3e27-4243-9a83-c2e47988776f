package com.example.springboot2.controller;

import com.example.springboot2.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 测试控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/test")
public class TestController {

    /**
     * 简单的健康检查
     */
    @GetMapping("/health")
    public Result<String> health() {
        log.info("健康检查请求");
        return Result.success("服务正常运行");
    }

    /**
     * 测试POST请求
     */
    @PostMapping("/echo")
    public Result<Map<String, Object>> echo(@RequestBody Map<String, Object> data) {
        log.info("收到测试数据: {}", data);
        return Result.success("数据接收成功", data);
    }

    /**
     * 测试登录数据格式
     */
    @PostMapping("/login-test")
    public Result<Map<String, Object>> loginTest(@RequestBody Map<String, Object> data) {
        log.info("收到登录测试数据: {}", data);
        
        String username = (String) data.get("username");
        String password = (String) data.get("password");
        
        if (username == null || username.trim().isEmpty()) {
            return Result.error(400, "用户名不能为空");
        }
        
        if (password == null || password.trim().isEmpty()) {
            return Result.error(400, "密码不能为空");
        }
        
        return Result.success("登录数据格式正确", Map.of(
            "username", username,
            "password", "***",
            "message", "数据格式验证通过"
        ));
    }
}
