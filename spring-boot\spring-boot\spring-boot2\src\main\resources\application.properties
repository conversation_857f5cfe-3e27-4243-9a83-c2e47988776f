# Application Configuration
spring.application.name=merchant-backend
server.port=8082

# API Configuration
# server.servlet.context-path=/api

# Database Configuration (MySQL) - 临时使用root用户
spring.datasource.url=************************************************************************************************************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.jdbc.time_zone=Asia/Shanghai

# JWT Configuration - 生产环境密钥（512位以上）
jwt.secret=mySecretKey123456789012345678901234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789
jwt.expiration=86400000

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
file.upload.path=./uploads/

# Logging Configuration
logging.level.com.example.springboot2=DEBUG
logging.level.org.springframework.security=DEBUG

# CORS Configuration
cors.allowed-origins=http://localhost:3000,http://localhost:5173,http://localhost:5174,http://localhost:8081
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=*
cors.allow-credentials=true
