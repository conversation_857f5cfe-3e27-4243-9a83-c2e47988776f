<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed Jun 18 22:59:01 CST 2025 -->
<title>All Classes and Interfaces (spring-boot2 0.0.1-SNAPSHOT API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-06-18">
<meta name="description" content="class index">
<meta name="generator" content="javadoc/AllClassesIndexWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="all-classes-index-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="index-all.html">索引</a></li>
<li><a href="help-doc.html#all-classes">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="All Classes and Interfaces" class="title">All Classes and Interfaces</h1>
</div>
<div id="all-classes-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="all-classes-table-tab0" role="tab" aria-selected="true" aria-controls="all-classes-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="all-classes-table-tab1" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab1', 2)" class="table-tab">接口</button><button id="all-classes-table-tab2" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab2', 2)" class="table-tab">类</button><button id="all-classes-table-tab3" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab3', 2)" class="table-tab">Enum Classes</button><button id="all-classes-table-tab5" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab5', 2)" class="table-tab">异常错误</button></div>
<div id="all-classes-table.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="all-classes-table-tab0">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/controller/AuthController.html" title="com.example.springboot2.controller中的类">AuthController</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">认证控制器</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/service/AuthService.html" title="com.example.springboot2.service中的类">AuthService</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">认证服务类</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/entity/BaseEntity.html" title="com.example.springboot2.entity中的类">BaseEntity</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">基础实体类</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab5"><a href="com/example/springboot2/exception/BusinessException.html" title="com.example.springboot2.exception中的类">BusinessException</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab5">
<div class="block">业务异常类</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/config/CorsConfig.html" title="com.example.springboot2.config中的类">CorsConfig</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">跨域配置</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/entity/Coupon.html" title="com.example.springboot2.entity中的类">Coupon</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">优惠券实体类</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="com/example/springboot2/entity/Coupon.CouponStatus.html" title="enum class in com.example.springboot2.entity">Coupon.CouponStatus</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">
<div class="block">优惠券状态枚举</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab3"><a href="com/example/springboot2/entity/Coupon.CouponType.html" title="enum class in com.example.springboot2.entity">Coupon.CouponType</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab3">
<div class="block">优惠券类型枚举</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/controller/CouponController.html" title="com.example.springboot2.controller中的类">CouponController</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">优惠券控制器</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/example/springboot2/repository/CouponRepository.html" title="com.example.springboot2.repository中的接口">CouponRepository</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">优惠券Repository</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/service/CouponService.html" title="com.example.springboot2.service中的类">CouponService</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">优惠券服务类</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/service/CouponService.CouponStats.html" title="com.example.springboot2.service中的类">CouponService.CouponStats</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">优惠券统计数据DTO</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/controller/DashboardController.html" title="com.example.springboot2.controller中的类">DashboardController</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">仪表板控制器</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/service/DashboardService.html" title="com.example.springboot2.service中的类">DashboardService</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">仪表板服务类</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/config/DataInitializer.html" title="com.example.springboot2.config中的类">DataInitializer</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">数据初始化器</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/controller/FileController.html" title="com.example.springboot2.controller中的类">FileController</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">文件上传控制器</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/exception/GlobalExceptionHandler.html" title="com.example.springboot2.exception中的类">GlobalExceptionHandler</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">全局异常处理器</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">商品实体类</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="com/example/springboot2/entity/Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">
<div class="block">商品状态枚举</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab3"><a href="com/example/springboot2/entity/Goods.GoodsType.html" title="enum class in com.example.springboot2.entity">Goods.GoodsType</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab3">
<div class="block">商品类型枚举</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/entity/GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">商品分类实体类</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/controller/GoodsCategoryController.html" title="com.example.springboot2.controller中的类">GoodsCategoryController</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">商品分类控制器</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/example/springboot2/repository/GoodsCategoryRepository.html" title="com.example.springboot2.repository中的接口">GoodsCategoryRepository</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">商品分类Repository</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/service/GoodsCategoryService.html" title="com.example.springboot2.service中的类">GoodsCategoryService</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">商品分类服务类</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/controller/GoodsController.html" title="com.example.springboot2.controller中的类">GoodsController</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">商品控制器</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/example/springboot2/repository/GoodsRepository.html" title="com.example.springboot2.repository中的接口">GoodsRepository</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">商品Repository</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/service/GoodsService.html" title="com.example.springboot2.service中的类">GoodsService</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">商品服务类</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/service/GoodsService.GoodsStats.html" title="com.example.springboot2.service中的类">GoodsService.GoodsStats</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">商品统计数据DTO</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/config/JpaConfig.html" title="com.example.springboot2.config中的类">JpaConfig</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">JPA配置</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/security/JwtAuthenticationEntryPoint.html" title="com.example.springboot2.security中的类">JwtAuthenticationEntryPoint</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">JWT认证入口点</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/security/JwtAuthenticationFilter.html" title="com.example.springboot2.security中的类">JwtAuthenticationFilter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">JWT认证过滤器</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/util/JwtUtil.html" title="com.example.springboot2.util中的类">JwtUtil</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">JWT工具类</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/example/springboot2/util/JwtUtil.ClaimsResolver.html" title="com.example.springboot2.util中的接口">JwtUtil.ClaimsResolver</a>&lt;<a href="com/example/springboot2/util/JwtUtil.ClaimsResolver.html" title="JwtUtil.ClaimsResolver中的类型参数">T</a>&gt;</div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">声明解析器接口</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/service/LaundryBusinessService.html" title="com.example.springboot2.service中的类">LaundryBusinessService</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">洗护业务服务类</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/controller/LaundryController.html" title="com.example.springboot2.controller中的类">LaundryController</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">洗护业务控制器</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">洗护订单实体类</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">
<div class="block">洗护订单状态枚举</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/entity/LaundryOrderItem.html" title="com.example.springboot2.entity中的类">LaundryOrderItem</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">洗护订单项实体类</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/example/springboot2/repository/LaundryOrderRepository.html" title="com.example.springboot2.repository中的接口">LaundryOrderRepository</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">洗护订单Repository</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/entity/LaundryService.html" title="com.example.springboot2.entity中的类">LaundryService</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">洗护服务实体类</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="com/example/springboot2/entity/LaundryService.ServiceType.html" title="enum class in com.example.springboot2.entity">LaundryService.ServiceType</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">
<div class="block">洗护服务类型枚举</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/example/springboot2/repository/LaundryServiceRepository.html" title="com.example.springboot2.repository中的接口">LaundryServiceRepository</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">洗护服务Repository</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/dto/LoginRequest.html" title="com.example.springboot2.dto中的类">LoginRequest</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">登录请求DTO</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/dto/LoginResponse.html" title="com.example.springboot2.dto中的类">LoginResponse</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">登录响应DTO</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/dto/LoginResponse.UserInfo.html" title="com.example.springboot2.dto中的类">LoginResponse.UserInfo</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">商家实体类</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="com/example/springboot2/entity/Merchant.CertificationStatus.html" title="enum class in com.example.springboot2.entity">Merchant.CertificationStatus</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">
<div class="block">认证状态枚举</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab3"><a href="com/example/springboot2/entity/Merchant.MerchantStatus.html" title="enum class in com.example.springboot2.entity">Merchant.MerchantStatus</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab3">
<div class="block">商家状态枚举</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/controller/MerchantController.html" title="com.example.springboot2.controller中的类">MerchantController</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">商家控制器</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/controller/MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">商家财务管理控制器</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/example/springboot2/repository/MerchantRepository.html" title="com.example.springboot2.repository中的接口">MerchantRepository</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">商家Repository</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/service/MerchantService.html" title="com.example.springboot2.service中的类">MerchantService</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">商家服务类</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/controller/MerchantServiceController.html" title="com.example.springboot2.controller中的类">MerchantServiceController</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">商家服务管理控制器</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/service/MerchantServiceManagementService.html" title="com.example.springboot2.service中的类">MerchantServiceManagementService</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/entity/Order.html" title="com.example.springboot2.entity中的类">Order</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">订单实体类</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="com/example/springboot2/entity/Order.OrderStatus.html" title="enum class in com.example.springboot2.entity">Order.OrderStatus</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">
<div class="block">订单状态枚举</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab3"><a href="com/example/springboot2/entity/Order.OrderType.html" title="enum class in com.example.springboot2.entity">Order.OrderType</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab3">
<div class="block">订单类型枚举</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/controller/OrderController.html" title="com.example.springboot2.controller中的类">OrderController</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">订单控制器</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/entity/OrderItem.html" title="com.example.springboot2.entity中的类">OrderItem</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">订单项实体类</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/example/springboot2/repository/OrderRepository.html" title="com.example.springboot2.repository中的接口">OrderRepository</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">订单Repository</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/service/OrderService.html" title="com.example.springboot2.service中的类">OrderService</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">订单服务类</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/service/OrderService.OrderStats.html" title="com.example.springboot2.service中的类">OrderService.OrderStats</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">订单统计数据DTO</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/common/PageResult.html" title="com.example.springboot2.common中的类">PageResult</a>&lt;<a href="com/example/springboot2/common/PageResult.html" title="PageResult中的类型参数">T</a>&gt;</div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">分页响应结果类</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/config/PasswordConfig.html" title="com.example.springboot2.config中的类">PasswordConfig</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">密码编码器配置 - 解决循环依赖问题</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/controller/RegionController.html" title="com.example.springboot2.controller中的类">RegionController</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">地区数据控制器</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/dto/RegisterRequest.html" title="com.example.springboot2.dto中的类">RegisterRequest</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">注册请求DTO</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/common/Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="com/example/springboot2/common/Result.html" title="Result中的类型参数">T</a>&gt;</div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">统一响应结果类</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/config/SecurityConfig.html" title="com.example.springboot2.config中的类">SecurityConfig</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Spring Security配置</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/controller/SimpleAuthController.html" title="com.example.springboot2.controller中的类">SimpleAuthController</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">简单认证控制器 - 解决前端登录500错误
 提供基础的登录功能，支持超级管理员</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/SpringBoot2Application.html" title="com.example.springboot2中的类">SpringBoot2Application</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/entity/User.html" title="com.example.springboot2.entity中的类">User</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">用户实体类</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="com/example/springboot2/entity/User.UserRole.html" title="enum class in com.example.springboot2.entity">User.UserRole</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">
<div class="block">用户角色枚举</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab3"><a href="com/example/springboot2/entity/User.UserStatus.html" title="enum class in com.example.springboot2.entity">User.UserStatus</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab3">
<div class="block">用户状态枚举</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/example/springboot2/repository/UserRepository.html" title="com.example.springboot2.repository中的接口">UserRepository</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">用户Repository</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/example/springboot2/service/UserService.html" title="com.example.springboot2.service中的类">UserService</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">用户服务类</div>
</div>
</div>
</div>
</div>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
