<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed Jun 18 22:33:52 CST 2025 -->
<title>类 com.example.springboot2.common.Result的使用 (spring-boot2 0.0.1-SNAPSHOT API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-06-18">
<meta name="description" content="use: package: com.example.springboot2.common, class: Result">
<meta name="generator" content="javadoc/ClassUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-use-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../Result.html" title="com.example.springboot2.common中的类">类</a></li>
<li class="nav-bar-cell1-rev">使用</li>
<li><a href="../package-tree.html">树</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html#use">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="类的使用 com.example.springboot2.common.Result" class="title">类的使用<br>com.example.springboot2.common.Result</h1>
</div>
<div class="caption"><span>使用<a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>的程序包</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">程序包</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="#com.example.springboot2.common">com.example.springboot2.common</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.example.springboot2.controller">com.example.springboot2.controller</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.example.springboot2.exception">com.example.springboot2.exception</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<section class="class-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.example.springboot2.common">
<h2><a href="../package-summary.html">com.example.springboot2.common</a>中<a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>的使用</h2>
<div class="caption"><span>返回<a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>的<a href="../package-summary.html">com.example.springboot2.common</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>static &lt;T&gt;&nbsp;<a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;T&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Result.</span><code><a href="../Result.html#badRequest()" class="member-name-link">badRequest</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static &lt;T&gt;&nbsp;<a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;T&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Result.</span><code><a href="../Result.html#badRequest(java.lang.String)" class="member-name-link">badRequest</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;message)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static &lt;T&gt;&nbsp;<a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;T&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Result.</span><code><a href="../Result.html#error()" class="member-name-link">error</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static &lt;T&gt;&nbsp;<a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;T&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Result.</span><code><a href="../Result.html#error(java.lang.Integer,java.lang.String)" class="member-name-link">error</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>&nbsp;code,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;message)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static &lt;T&gt;&nbsp;<a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;T&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Result.</span><code><a href="../Result.html#error(java.lang.String)" class="member-name-link">error</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;message)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static &lt;T&gt;&nbsp;<a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;T&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Result.</span><code><a href="../Result.html#forbidden()" class="member-name-link">forbidden</a>()</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static &lt;T&gt;&nbsp;<a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;T&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Result.</span><code><a href="../Result.html#forbidden(java.lang.String)" class="member-name-link">forbidden</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;message)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static &lt;T&gt;&nbsp;<a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;T&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Result.</span><code><a href="../Result.html#notFound()" class="member-name-link">notFound</a>()</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static &lt;T&gt;&nbsp;<a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;T&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Result.</span><code><a href="../Result.html#notFound(java.lang.String)" class="member-name-link">notFound</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;message)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static &lt;T&gt;&nbsp;<a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;T&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Result.</span><code><a href="../Result.html#success()" class="member-name-link">success</a>()</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static &lt;T&gt;&nbsp;<a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;T&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Result.</span><code><a href="../Result.html#success(java.lang.String,T)" class="member-name-link">success</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;message,
 T&nbsp;data)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static &lt;T&gt;&nbsp;<a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;T&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Result.</span><code><a href="../Result.html#success(T)" class="member-name-link">success</a><wbr>(T&nbsp;data)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static &lt;T&gt;&nbsp;<a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;T&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Result.</span><code><a href="../Result.html#unauthorized()" class="member-name-link">unauthorized</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static &lt;T&gt;&nbsp;<a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;T&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Result.</span><code><a href="../Result.html#unauthorized(java.lang.String)" class="member-name-link">unauthorized</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;message)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.example.springboot2.controller">
<h2><a href="../../controller/package-summary.html">com.example.springboot2.controller</a>中<a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>的使用</h2>
<div class="caption"><span>返回<a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>的<a href="../../controller/package-summary.html">com.example.springboot2.controller</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LaundryController.</span><code><a href="../../controller/LaundryController.html#acceptLaundryOrder(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">acceptLaundryOrder</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;request,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">接单</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantFinanceController.</span><code><a href="../../controller/MerchantFinanceController.html#addBankCard(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">addBankCard</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;bankCardData,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">添加银行卡</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">CouponController.</span><code><a href="../../controller/CouponController.html#batchDeleteCoupons(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">batchDeleteCoupons</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&gt;&gt;&nbsp;request,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">批量删除优惠券</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsController.</span><code><a href="../../controller/GoodsController.html#batchDeleteGoods(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">batchDeleteGoods</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&gt;&gt;&nbsp;request,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">批量删除商品</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantServiceController.</span><code><a href="../../controller/MerchantServiceController.html#batchOperateServices(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">batchOperateServices</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;operationData,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">批量操作服务</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">CouponController.</span><code><a href="../../controller/CouponController.html#batchUpdateCouponStatus(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">batchUpdateCouponStatus</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;request,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">批量更新优惠券状态</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsController.</span><code><a href="../../controller/GoodsController.html#batchUpdateGoodsStatus(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">batchUpdateGoodsStatus</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;request,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">批量更新商品状态</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LaundryController.</span><code><a href="../../controller/LaundryController.html#batchUpdateServiceStatus(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">batchUpdateServiceStatus</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;request,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">批量更新服务状态</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">OrderController.</span><code><a href="../../controller/OrderController.html#cancelOrder(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">cancelOrder</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;request,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">取消订单</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantFinanceController.</span><code><a href="../../controller/MerchantFinanceController.html#cancelWithdrawal(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">cancelWithdrawal</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;withdrawalId,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">取消提现申请</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">AuthController.</span><code><a href="../../controller/AuthController.html#changePassword(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">changePassword</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;request,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">修改密码</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">OrderController.</span><code><a href="../../controller/OrderController.html#confirmOrder(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">confirmOrder</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">确认收货</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantServiceController.</span><code><a href="../../controller/MerchantServiceController.html#copyService(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">copyService</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;serviceId,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">复制服务</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../entity/Coupon.html" title="com.example.springboot2.entity中的类">Coupon</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">CouponController.</span><code><a href="../../controller/CouponController.html#createCoupon(com.example.springboot2.entity.Coupon,org.springframework.security.core.Authentication)" class="member-name-link">createCoupon</a><wbr>(@Valid <a href="../../entity/Coupon.html" title="com.example.springboot2.entity中的类">Coupon</a>&nbsp;coupon,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">创建优惠券</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsController.</span><code><a href="../../controller/GoodsController.html#createGoods(com.example.springboot2.entity.Goods,org.springframework.security.core.Authentication)" class="member-name-link">createGoods</a><wbr>(@Valid <a href="../../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&nbsp;goods,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">创建商品</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../entity/GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsCategoryController.</span><code><a href="../../controller/GoodsCategoryController.html#createGoodsCategory(com.example.springboot2.entity.GoodsCategory,org.springframework.security.core.Authentication)" class="member-name-link">createGoodsCategory</a><wbr>(@Valid <a href="../../entity/GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&nbsp;category,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">创建商品分类</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../entity/LaundryService.html" title="com.example.springboot2.entity中的类">LaundryService</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LaundryController.</span><code><a href="../../controller/LaundryController.html#createLaundryService(com.example.springboot2.entity.LaundryService,org.springframework.security.core.Authentication)" class="member-name-link">createLaundryService</a><wbr>(@Valid <a href="../../entity/LaundryService.html" title="com.example.springboot2.entity中的类">LaundryService</a>&nbsp;service,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">创建洗护服务</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantFinanceController.</span><code><a href="../../controller/MerchantFinanceController.html#deleteBankCard(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">deleteBankCard</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;cardId,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">删除银行卡</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">CouponController.</span><code><a href="../../controller/CouponController.html#deleteCoupon(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">deleteCoupon</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">删除优惠券</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsController.</span><code><a href="../../controller/GoodsController.html#deleteGoods(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">deleteGoods</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">删除商品</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsCategoryController.</span><code><a href="../../controller/GoodsCategoryController.html#deleteGoodsCategory(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">deleteGoodsCategory</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">删除商品分类</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LaundryController.</span><code><a href="../../controller/LaundryController.html#deleteLaundryService(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">deleteLaundryService</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">删除洗护服务</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantServiceController.</span><code><a href="../../controller/MerchantServiceController.html#deleteService(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">deleteService</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;serviceId,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">删除服务</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantFinanceController.</span><code><a href="../../controller/MerchantFinanceController.html#exportFinanceData(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">exportFinanceData</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;exportRequest,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">导出财务数据</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantFinanceController.</span><code><a href="../../controller/MerchantFinanceController.html#getAutoWithdrawSettings(org.springframework.security.core.Authentication)" class="member-name-link">getAutoWithdrawSettings</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取自动提现设置</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantFinanceController.</span><code><a href="../../controller/MerchantFinanceController.html#getBankCards(org.springframework.security.core.Authentication)" class="member-name-link">getBankCards</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取银行卡信息</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../../entity/GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsCategoryController.</span><code><a href="../../controller/GoodsCategoryController.html#getCategoriesTree(org.springframework.security.core.Authentication)" class="member-name-link">getCategoriesTree</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取所有分类（树形结构）</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../entity/GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsCategoryController.</span><code><a href="../../controller/GoodsCategoryController.html#getCategoryDetail(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">getCategoryDetail</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取分类详情</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantController.</span><code><a href="../../controller/MerchantController.html#getCertificationInfo(org.springframework.security.core.Authentication)" class="member-name-link">getCertificationInfo</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取认证信息</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;org.springframework.data.domain.Page&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantFinanceController.</span><code><a href="../../controller/MerchantFinanceController.html#getCommissionDetails(java.lang.String,java.lang.String,int,int,org.springframework.security.core.Authentication)" class="member-name-link">getCommissionDetails</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;startDate,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;endDate,
 int&nbsp;page,
 int&nbsp;size,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取佣金明细</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../entity/Coupon.html" title="com.example.springboot2.entity中的类">Coupon</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">CouponController.</span><code><a href="../../controller/CouponController.html#getCouponDetail(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">getCouponDetail</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取优惠券详情</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../PageResult.html" title="com.example.springboot2.common中的类">PageResult</a>&lt;<a href="../../entity/Coupon.html" title="com.example.springboot2.entity中的类">Coupon</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">CouponController.</span><code><a href="../../controller/CouponController.html#getCouponList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getCouponList</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>&nbsp;current,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>&nbsp;size,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;status,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;couponType,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">分页查询优惠券列表</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../service/CouponService.CouponStats.html" title="com.example.springboot2.service中的类">CouponService.CouponStats</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">CouponController.</span><code><a href="../../controller/CouponController.html#getCouponStats(org.springframework.security.core.Authentication)" class="member-name-link">getCouponStats</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取优惠券统计数据</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">DashboardController.</span><code><a href="../../controller/DashboardController.html#getCustomerStats(java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getCustomerStats</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;period,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取客户统计</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantFinanceController.</span><code><a href="../../controller/MerchantFinanceController.html#getDepositInfo(org.springframework.security.core.Authentication)" class="member-name-link">getDepositInfo</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取保证金信息</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantFinanceController.</span><code><a href="../../controller/MerchantFinanceController.html#getFinanceOverview(org.springframework.security.core.Authentication)" class="member-name-link">getFinanceOverview</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取财务概览</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantFinanceController.</span><code><a href="../../controller/MerchantFinanceController.html#getFinanceReports(java.lang.String,java.lang.String,java.lang.String,java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getFinanceReports</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;reportType,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;period,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;startDate,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;endDate,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取财务报表</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../../entity/GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsCategoryController.</span><code><a href="../../controller/GoodsCategoryController.html#getGoodsCategories(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">getGoodsCategories</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;parentId,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取商品分类列表</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsController.</span><code><a href="../../controller/GoodsController.html#getGoodsDetail(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">getGoodsDetail</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取商品详情</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../PageResult.html" title="com.example.springboot2.common中的类">PageResult</a>&lt;<a href="../../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsController.</span><code><a href="../../controller/GoodsController.html#getGoodsList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">getGoodsList</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>&nbsp;current,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>&nbsp;size,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;status,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;categoryId,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">分页查询商品列表</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../service/GoodsService.GoodsStats.html" title="com.example.springboot2.service中的类">GoodsService.GoodsStats</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsController.</span><code><a href="../../controller/GoodsController.html#getGoodsStats(org.springframework.security.core.Authentication)" class="member-name-link">getGoodsStats</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取商品统计数据</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsController.</span><code><a href="../../controller/GoodsController.html#getHotGoods(java.lang.Integer,org.springframework.security.core.Authentication)" class="member-name-link">getHotGoods</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>&nbsp;limit,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取热销商品</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;org.springframework.data.domain.Page&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantFinanceController.</span><code><a href="../../controller/MerchantFinanceController.html#getIncomeDetails(java.lang.String,java.lang.String,java.lang.String,int,int,org.springframework.security.core.Authentication)" class="member-name-link">getIncomeDetails</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;type,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;startDate,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;endDate,
 int&nbsp;page,
 int&nbsp;size,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取收入明细</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantFinanceController.</span><code><a href="../../controller/MerchantFinanceController.html#getIncomeStatistics(java.lang.String,java.lang.String,java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getIncomeStatistics</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;period,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;startDate,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;endDate,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取收入统计</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LaundryController.</span><code><a href="../../controller/LaundryController.html#getLaundryOrderDetail(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">getLaundryOrderDetail</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取洗护订单详情</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../PageResult.html" title="com.example.springboot2.common中的类">PageResult</a>&lt;<a href="../../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LaundryController.</span><code><a href="../../controller/LaundryController.html#getLaundryOrders(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getLaundryOrders</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>&nbsp;current,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>&nbsp;size,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;orderNo,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;status,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">分页查询洗护订单列表</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../entity/LaundryService.html" title="com.example.springboot2.entity中的类">LaundryService</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LaundryController.</span><code><a href="../../controller/LaundryController.html#getLaundryServiceDetail(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">getLaundryServiceDetail</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取洗护服务详情</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../PageResult.html" title="com.example.springboot2.common中的类">PageResult</a>&lt;<a href="../../entity/LaundryService.html" title="com.example.springboot2.entity中的类">LaundryService</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LaundryController.</span><code><a href="../../controller/LaundryController.html#getLaundryServices(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getLaundryServices</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>&nbsp;current,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>&nbsp;size,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;serviceType,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">分页查询洗护服务列表</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">DashboardController.</span><code><a href="../../controller/DashboardController.html#getMerchantAfterSales(org.springframework.security.core.Authentication)" class="member-name-link">getMerchantAfterSales</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取商家首页售后数据</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">DashboardController.</span><code><a href="../../controller/DashboardController.html#getMerchantCategorySales(org.springframework.security.core.Authentication)" class="member-name-link">getMerchantCategorySales</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取商品分类销售数据</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">DashboardController.</span><code><a href="../../controller/DashboardController.html#getMerchantGoods(org.springframework.security.core.Authentication)" class="member-name-link">getMerchantGoods</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取商家首页商品数据</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">DashboardController.</span><code><a href="../../controller/DashboardController.html#getMerchantHotGoods(java.lang.Integer,org.springframework.security.core.Authentication)" class="member-name-link">getMerchantHotGoods</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>&nbsp;limit,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取热销商品数据</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantController.</span><code><a href="../../controller/MerchantController.html#getMerchantInfo(org.springframework.security.core.Authentication)" class="member-name-link">getMerchantInfo</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取商家信息</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">DashboardController.</span><code><a href="../../controller/DashboardController.html#getMerchantOrders(org.springframework.security.core.Authentication)" class="member-name-link">getMerchantOrders</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取商家首页订单数据</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">DashboardController.</span><code><a href="../../controller/DashboardController.html#getMerchantOverview(org.springframework.security.core.Authentication)" class="member-name-link">getMerchantOverview</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取商家首页概览数据</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">DashboardController.</span><code><a href="../../controller/DashboardController.html#getMerchantPendingTasks(org.springframework.security.core.Authentication)" class="member-name-link">getMerchantPendingTasks</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取商家首页待处理事项</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">DashboardController.</span><code><a href="../../controller/DashboardController.html#getMerchantSalesTrend(java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getMerchantSalesTrend</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;period,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取商家首页销售趋势数据</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;org.springframework.data.domain.Page&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantServiceController.</span><code><a href="../../controller/MerchantServiceController.html#getMerchantServices(java.lang.String,java.lang.String,int,int,org.springframework.security.core.Authentication)" class="member-name-link">getMerchantServices</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;status,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;category,
 int&nbsp;page,
 int&nbsp;size,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取商家的服务列表</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">DashboardController.</span><code><a href="../../controller/DashboardController.html#getMerchantTransaction(org.springframework.security.core.Authentication)" class="member-name-link">getMerchantTransaction</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取商家首页交易数据</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">DashboardController.</span><code><a href="../../controller/DashboardController.html#getMerchantViolations(org.springframework.security.core.Authentication)" class="member-name-link">getMerchantViolations</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取商家首页违规数据</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../entity/Order.html" title="com.example.springboot2.entity中的类">Order</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">OrderController.</span><code><a href="../../controller/OrderController.html#getOrderDetail(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">getOrderDetail</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取订单详情</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../PageResult.html" title="com.example.springboot2.common中的类">PageResult</a>&lt;<a href="../../entity/Order.html" title="com.example.springboot2.entity中的类">Order</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">OrderController.</span><code><a href="../../controller/OrderController.html#getOrdersList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getOrdersList</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>&nbsp;current,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>&nbsp;size,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;orderNo,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;status,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;orderType,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">分页查询订单列表</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../service/OrderService.OrderStats.html" title="com.example.springboot2.service中的类">OrderService.OrderStats</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">OrderController.</span><code><a href="../../controller/OrderController.html#getOrderStats(org.springframework.security.core.Authentication)" class="member-name-link">getOrderStats</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取订单统计数据</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">DashboardController.</span><code><a href="../../controller/DashboardController.html#getRealTimeStats(org.springframework.security.core.Authentication)" class="member-name-link">getRealTimeStats</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取实时统计数据</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">RegionController.</span><code><a href="../../controller/RegionController.html#getRegionData()" class="member-name-link">getRegionData</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">获取地区数据</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">DashboardController.</span><code><a href="../../controller/DashboardController.html#getRevenueStats(java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getRevenueStats</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;period,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取收入统计</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantServiceController.</span><code><a href="../../controller/MerchantServiceController.html#getServiceCategories()" class="member-name-link">getServiceCategories</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">获取服务分类列表</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantServiceController.</span><code><a href="../../controller/MerchantServiceController.html#getServiceDetail(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">getServiceDetail</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;serviceId,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取服务详情</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantServiceController.</span><code><a href="../../controller/MerchantServiceController.html#getServiceOrderStatistics(java.lang.Long,java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getServiceOrderStatistics</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;serviceId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;period,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取服务订单统计</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;org.springframework.data.domain.Page&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantServiceController.</span><code><a href="../../controller/MerchantServiceController.html#getServiceReviews(java.lang.Long,java.lang.Integer,int,int,org.springframework.security.core.Authentication)" class="member-name-link">getServiceReviews</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;serviceId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>&nbsp;rating,
 int&nbsp;page,
 int&nbsp;size,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取服务评价</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantServiceController.</span><code><a href="../../controller/MerchantServiceController.html#getServiceStatistics(org.springframework.security.core.Authentication)" class="member-name-link">getServiceStatistics</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取服务统计</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantFinanceController.</span><code><a href="../../controller/MerchantFinanceController.html#getTaxInfo(java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getTaxInfo</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;year,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取税务信息</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../../entity/Order.html" title="com.example.springboot2.entity中的类">Order</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">OrderController.</span><code><a href="../../controller/OrderController.html#getTodayOrders(org.springframework.security.core.Authentication)" class="member-name-link">getTodayOrders</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取今日订单</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../dto/LoginResponse.UserInfo.html" title="com.example.springboot2.dto中的类">LoginResponse.UserInfo</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">AuthController.</span><code><a href="../../controller/AuthController.html#getUserInfo(org.springframework.security.core.Authentication)" class="member-name-link">getUserInfo</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取用户信息</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../../entity/Coupon.html" title="com.example.springboot2.entity中的类">Coupon</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">CouponController.</span><code><a href="../../controller/CouponController.html#getValidCoupons(org.springframework.security.core.Authentication)" class="member-name-link">getValidCoupons</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取有效优惠券</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;org.springframework.data.domain.Page&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantFinanceController.</span><code><a href="../../controller/MerchantFinanceController.html#getWithdrawalRecords(java.lang.String,int,int,org.springframework.security.core.Authentication)" class="member-name-link">getWithdrawalRecords</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;status,
 int&nbsp;page,
 int&nbsp;size,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取提现记录</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../dto/LoginResponse.html" title="com.example.springboot2.dto中的类">LoginResponse</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">AuthController.</span><code><a href="../../controller/AuthController.html#login(com.example.springboot2.dto.LoginRequest,jakarta.servlet.http.HttpServletRequest)" class="member-name-link">login</a><wbr>(@Valid <a href="../../dto/LoginRequest.html" title="com.example.springboot2.dto中的类">LoginRequest</a>&nbsp;request,
 jakarta.servlet.http.HttpServletRequest&nbsp;httpRequest)</code></div>
<div class="col-last even-row-color">
<div class="block">用户登录</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">AuthController.</span><code><a href="../../controller/AuthController.html#logout()" class="member-name-link">logout</a>()</code></div>
<div class="col-last odd-row-color">
<div class="block">退出登录</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantFinanceController.</span><code><a href="../../controller/MerchantFinanceController.html#payDeposit(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">payDeposit</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;paymentData,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">缴纳保证金</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantServiceController.</span><code><a href="../../controller/MerchantServiceController.html#publishService(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">publishService</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;serviceData,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">发布新服务</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">AuthController.</span><code><a href="../../controller/AuthController.html#refreshToken(jakarta.servlet.http.HttpServletRequest)" class="member-name-link">refreshToken</a><wbr>(jakarta.servlet.http.HttpServletRequest&nbsp;request)</code></div>
<div class="col-last even-row-color">
<div class="block">刷新token</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">OrderController.</span><code><a href="../../controller/OrderController.html#refundOrder(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">refundOrder</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;request,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">申请退款</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">AuthController.</span><code><a href="../../controller/AuthController.html#register(com.example.springboot2.dto.RegisterRequest)" class="member-name-link">register</a><wbr>(@Valid <a href="../../dto/RegisterRequest.html" title="com.example.springboot2.dto中的类">RegisterRequest</a>&nbsp;request)</code></div>
<div class="col-last even-row-color">
<div class="block">用户注册</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LaundryController.</span><code><a href="../../controller/LaundryController.html#rejectLaundryOrder(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">rejectLaundryOrder</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;request,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">拒绝订单</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantServiceController.</span><code><a href="../../controller/MerchantServiceController.html#replyToReview(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">replyToReview</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;reviewId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;replyData,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">回复评价</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantFinanceController.</span><code><a href="../../controller/MerchantFinanceController.html#requestDepositRefund(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">requestDepositRefund</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;request,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">申请保证金退还</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantFinanceController.</span><code><a href="../../controller/MerchantFinanceController.html#requestWithdrawal(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">requestWithdrawal</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;withdrawalData,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">申请提现</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">AuthController.</span><code><a href="../../controller/AuthController.html#resetPassword(java.util.Map)" class="member-name-link">resetPassword</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;request)</code></div>
<div class="col-last odd-row-color">
<div class="block">重置密码</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">AuthController.</span><code><a href="../../controller/AuthController.html#sendVerifyCode(java.util.Map)" class="member-name-link">sendVerifyCode</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;request)</code></div>
<div class="col-last even-row-color">
<div class="block">发送验证码</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantFinanceController.</span><code><a href="../../controller/MerchantFinanceController.html#setAutoWithdrawSettings(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">setAutoWithdrawSettings</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;settings,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">设置自动提现</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantFinanceController.</span><code><a href="../../controller/MerchantFinanceController.html#setDefaultBankCard(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">setDefaultBankCard</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;cardId,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">设置默认银行卡</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantServiceController.</span><code><a href="../../controller/MerchantServiceController.html#setServicePricing(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">setServicePricing</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;serviceId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;pricingData,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">设置服务价格</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">OrderController.</span><code><a href="../../controller/OrderController.html#shipOrder(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">shipOrder</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;request,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">发货</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantController.</span><code><a href="../../controller/MerchantController.html#submitCertification(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">submitCertification</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;request,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">提交商家认证</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantServiceController.</span><code><a href="../../controller/MerchantServiceController.html#toggleServiceStatus(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">toggleServiceStatus</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;serviceId,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">上架/下架服务</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../entity/Coupon.html" title="com.example.springboot2.entity中的类">Coupon</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">CouponController.</span><code><a href="../../controller/CouponController.html#updateCoupon(java.lang.Long,com.example.springboot2.entity.Coupon,org.springframework.security.core.Authentication)" class="member-name-link">updateCoupon</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 @Valid <a href="../../entity/Coupon.html" title="com.example.springboot2.entity中的类">Coupon</a>&nbsp;coupon,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">更新优惠券</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">CouponController.</span><code><a href="../../controller/CouponController.html#updateCouponStatus(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">updateCouponStatus</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;request,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">更新优惠券状态</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantController.</span><code><a href="../../controller/MerchantController.html#updateEmail(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">updateEmail</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;request,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">修改邮箱</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsController.</span><code><a href="../../controller/GoodsController.html#updateGoods(java.lang.Long,com.example.springboot2.entity.Goods,org.springframework.security.core.Authentication)" class="member-name-link">updateGoods</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 @Valid <a href="../../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&nbsp;goods,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">更新商品</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../entity/GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsCategoryController.</span><code><a href="../../controller/GoodsCategoryController.html#updateGoodsCategory(java.lang.Long,com.example.springboot2.entity.GoodsCategory,org.springframework.security.core.Authentication)" class="member-name-link">updateGoodsCategory</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 @Valid <a href="../../entity/GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&nbsp;category,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">更新商品分类</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsController.</span><code><a href="../../controller/GoodsController.html#updateGoodsStatus(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">updateGoodsStatus</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;request,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">更新商品状态</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LaundryController.</span><code><a href="../../controller/LaundryController.html#updateLaundryOrderStatus(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">updateLaundryOrderStatus</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;request,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">更新洗护订单状态</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../entity/LaundryService.html" title="com.example.springboot2.entity中的类">LaundryService</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LaundryController.</span><code><a href="../../controller/LaundryController.html#updateLaundryService(java.lang.Long,com.example.springboot2.entity.LaundryService,org.springframework.security.core.Authentication)" class="member-name-link">updateLaundryService</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 @Valid <a href="../../entity/LaundryService.html" title="com.example.springboot2.entity中的类">LaundryService</a>&nbsp;service,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">更新洗护服务</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantController.</span><code><a href="../../controller/MerchantController.html#updateMerchantInfo(com.example.springboot2.entity.Merchant,org.springframework.security.core.Authentication)" class="member-name-link">updateMerchantInfo</a><wbr>(@Valid <a href="../../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchantInfo,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">更新商家信息</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">OrderController.</span><code><a href="../../controller/OrderController.html#updateOrderStatus(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">updateOrderStatus</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;request,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">更新订单状态</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantController.</span><code><a href="../../controller/MerchantController.html#updatePhone(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">updatePhone</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;request,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">修改手机号</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantServiceController.</span><code><a href="../../controller/MerchantServiceController.html#updateService(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">updateService</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;serviceId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&nbsp;serviceData,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">更新服务信息</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">FileController.</span><code><a href="../../controller/FileController.html#uploadFile(org.springframework.web.multipart.MultipartFile)" class="member-name-link">uploadFile</a><wbr>(org.springframework.web.multipart.MultipartFile&nbsp;file)</code></div>
<div class="col-last odd-row-color">
<div class="block">文件上传</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">FileController.</span><code><a href="../../controller/FileController.html#uploadFiles(org.springframework.web.multipart.MultipartFile%5B%5D)" class="member-name-link">uploadFiles</a><wbr>(org.springframework.web.multipart.MultipartFile[]&nbsp;files)</code></div>
<div class="col-last even-row-color">
<div class="block">批量文件上传</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantServiceController.</span><code><a href="../../controller/MerchantServiceController.html#uploadServiceImages(java.util.List,org.springframework.security.core.Authentication)" class="member-name-link">uploadServiceImages</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;org.springframework.web.multipart.MultipartFile&gt;&nbsp;files,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">上传服务图片</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.example.springboot2.exception">
<h2><a href="../../exception/package-summary.html">com.example.springboot2.exception</a>中<a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>的使用</h2>
<div class="caption"><span>返回<a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>的<a href="../../exception/package-summary.html">com.example.springboot2.exception</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GlobalExceptionHandler.</span><code><a href="../../exception/GlobalExceptionHandler.html#handleAccessDeniedException(org.springframework.security.access.AccessDeniedException)" class="member-name-link">handleAccessDeniedException</a><wbr>(org.springframework.security.access.AccessDeniedException&nbsp;e)</code></div>
<div class="col-last even-row-color">
<div class="block">处理授权异常</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GlobalExceptionHandler.</span><code><a href="../../exception/GlobalExceptionHandler.html#handleAuthenticationException(org.springframework.security.core.AuthenticationException)" class="member-name-link">handleAuthenticationException</a><wbr>(org.springframework.security.core.AuthenticationException&nbsp;e)</code></div>
<div class="col-last odd-row-color">
<div class="block">处理认证异常</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GlobalExceptionHandler.</span><code><a href="../../exception/GlobalExceptionHandler.html#handleBindException(org.springframework.validation.BindException)" class="member-name-link">handleBindException</a><wbr>(org.springframework.validation.BindException&nbsp;e)</code></div>
<div class="col-last even-row-color">
<div class="block">处理绑定异常</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GlobalExceptionHandler.</span><code><a href="../../exception/GlobalExceptionHandler.html#handleBusinessException(com.example.springboot2.exception.BusinessException)" class="member-name-link">handleBusinessException</a><wbr>(<a href="../../exception/BusinessException.html" title="com.example.springboot2.exception中的类">BusinessException</a>&nbsp;e)</code></div>
<div class="col-last odd-row-color">
<div class="block">处理业务异常</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GlobalExceptionHandler.</span><code><a href="../../exception/GlobalExceptionHandler.html#handleConstraintViolationException(jakarta.validation.ConstraintViolationException)" class="member-name-link">handleConstraintViolationException</a><wbr>(jakarta.validation.ConstraintViolationException&nbsp;e)</code></div>
<div class="col-last even-row-color">
<div class="block">处理约束违反异常</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GlobalExceptionHandler.</span><code><a href="../../exception/GlobalExceptionHandler.html#handleException(java.lang.Exception)" class="member-name-link">handleException</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Exception.html" title="java.lang中的类或接口" class="external-link">Exception</a>&nbsp;e)</code></div>
<div class="col-last odd-row-color">
<div class="block">处理其他异常</div>
</div>
<div class="col-first even-row-color"><code><a href="../Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Void.html" title="java.lang中的类或接口" class="external-link">Void</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GlobalExceptionHandler.</span><code><a href="../../exception/GlobalExceptionHandler.html#handleMethodArgumentNotValidException(org.springframework.web.bind.MethodArgumentNotValidException)" class="member-name-link">handleMethodArgumentNotValidException</a><wbr>(org.springframework.web.bind.MethodArgumentNotValidException&nbsp;e)</code></div>
<div class="col-last even-row-color">
<div class="block">处理参数校验异常</div>
</div>
</div>
</section>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
