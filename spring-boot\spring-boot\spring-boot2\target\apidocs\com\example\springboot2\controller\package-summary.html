<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed Jun 18 22:33:52 CST 2025 -->
<title>com.example.springboot2.controller (spring-boot2 0.0.1-SNAPSHOT API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-06-18">
<meta name="description" content="declaration: package: com.example.springboot2.controller">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../index.html">概览</a></li>
<li class="nav-bar-cell1-rev">程序包</li>
<li>类</li>
<li><a href="package-use.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html#package">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="程序包 com.example.springboot2.controller" class="title">程序包 com.example.springboot2.controller</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">com.example.springboot2.controller</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">程序包</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="../package-summary.html">com.example.springboot2</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="caption"><span>类</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="AuthController.html" title="com.example.springboot2.controller中的类">AuthController</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">认证控制器</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="CouponController.html" title="com.example.springboot2.controller中的类">CouponController</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">优惠券控制器</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="DashboardController.html" title="com.example.springboot2.controller中的类">DashboardController</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">仪表板控制器</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="FileController.html" title="com.example.springboot2.controller中的类">FileController</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">文件上传控制器</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="GoodsCategoryController.html" title="com.example.springboot2.controller中的类">GoodsCategoryController</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">商品分类控制器</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="GoodsController.html" title="com.example.springboot2.controller中的类">GoodsController</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">商品控制器</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="LaundryController.html" title="com.example.springboot2.controller中的类">LaundryController</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">洗护业务控制器</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="MerchantController.html" title="com.example.springboot2.controller中的类">MerchantController</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">商家控制器</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">商家财务管理控制器</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="MerchantServiceController.html" title="com.example.springboot2.controller中的类">MerchantServiceController</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">商家服务管理控制器</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="OrderController.html" title="com.example.springboot2.controller中的类">OrderController</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">订单控制器</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="RegionController.html" title="com.example.springboot2.controller中的类">RegionController</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">地区数据控制器</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="SimpleAuthController.html" title="com.example.springboot2.controller中的类">SimpleAuthController</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">简单认证控制器 - 解决前端登录500错误
 提供基础的登录功能，支持超级管理员</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
