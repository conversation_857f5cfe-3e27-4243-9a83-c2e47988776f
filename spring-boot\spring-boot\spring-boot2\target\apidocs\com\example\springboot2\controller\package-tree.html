<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed Jun 18 22:33:52 CST 2025 -->
<title>com.example.springboot2.controller 类分层结构 (spring-boot2 0.0.1-SNAPSHOT API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-06-18">
<meta name="description" content="tree: package: com.example.springboot2.controller">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li>使用</li>
<li class="nav-bar-cell1-rev">树</li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html#tree">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">程序包com.example.springboot2.controller的分层结构</h1>
<span class="package-hierarchy-label">程序包分层结构:</span>
<ul class="horizontal">
<li><a href="../../../../overview-tree.html">所有程序包</a></li>
</ul>
</div>
<section class="hierarchy">
<h2 title="类分层结构">类分层结构</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="java.lang中的类或接口">Object</a>
<ul>
<li class="circle">com.example.springboot2.controller.<a href="AuthController.html" class="type-name-link" title="com.example.springboot2.controller中的类">AuthController</a></li>
<li class="circle">com.example.springboot2.controller.<a href="CouponController.html" class="type-name-link" title="com.example.springboot2.controller中的类">CouponController</a></li>
<li class="circle">com.example.springboot2.controller.<a href="DashboardController.html" class="type-name-link" title="com.example.springboot2.controller中的类">DashboardController</a></li>
<li class="circle">com.example.springboot2.controller.<a href="FileController.html" class="type-name-link" title="com.example.springboot2.controller中的类">FileController</a></li>
<li class="circle">com.example.springboot2.controller.<a href="GoodsCategoryController.html" class="type-name-link" title="com.example.springboot2.controller中的类">GoodsCategoryController</a></li>
<li class="circle">com.example.springboot2.controller.<a href="GoodsController.html" class="type-name-link" title="com.example.springboot2.controller中的类">GoodsController</a></li>
<li class="circle">com.example.springboot2.controller.<a href="LaundryController.html" class="type-name-link" title="com.example.springboot2.controller中的类">LaundryController</a></li>
<li class="circle">com.example.springboot2.controller.<a href="MerchantController.html" class="type-name-link" title="com.example.springboot2.controller中的类">MerchantController</a></li>
<li class="circle">com.example.springboot2.controller.<a href="MerchantFinanceController.html" class="type-name-link" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></li>
<li class="circle">com.example.springboot2.controller.<a href="MerchantServiceController.html" class="type-name-link" title="com.example.springboot2.controller中的类">MerchantServiceController</a></li>
<li class="circle">com.example.springboot2.controller.<a href="OrderController.html" class="type-name-link" title="com.example.springboot2.controller中的类">OrderController</a></li>
<li class="circle">com.example.springboot2.controller.<a href="RegionController.html" class="type-name-link" title="com.example.springboot2.controller中的类">RegionController</a></li>
<li class="circle">com.example.springboot2.controller.<a href="SimpleAuthController.html" class="type-name-link" title="com.example.springboot2.controller中的类">SimpleAuthController</a></li>
</ul>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
