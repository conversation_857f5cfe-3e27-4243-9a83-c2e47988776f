<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed Jun 18 22:33:52 CST 2025 -->
<title>程序包 com.example.springboot2.dto的使用 (spring-boot2 0.0.1-SNAPSHOT API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-06-18">
<meta name="description" content="use: package: com.example.springboot2.dto">
<meta name="generator" content="javadoc/PackageUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-use-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="nav-bar-cell1-rev">使用</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html#use">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="程序包的使用 com.example.springboot2.dto" class="title">程序包的使用<br>com.example.springboot2.dto</h1>
</div>
<div class="caption"><span>使用<a href="package-summary.html">com.example.springboot2.dto</a>的程序包</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">程序包</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="#com.example.springboot2.controller">com.example.springboot2.controller</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.example.springboot2.service">com.example.springboot2.service</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<section class="package-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.example.springboot2.controller">
<div class="caption"><span><a href="../controller/package-summary.html">com.example.springboot2.controller</a>使用的<a href="package-summary.html">com.example.springboot2.dto</a>中的类</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="class-use/LoginRequest.html#com.example.springboot2.controller">LoginRequest</a></div>
<div class="col-last even-row-color">
<div class="block">登录请求DTO</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/LoginResponse.html#com.example.springboot2.controller">LoginResponse</a></div>
<div class="col-last odd-row-color">
<div class="block">登录响应DTO</div>
</div>
<div class="col-first even-row-color"><a href="class-use/LoginResponse.UserInfo.html#com.example.springboot2.controller">LoginResponse.UserInfo</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="class-use/RegisterRequest.html#com.example.springboot2.controller">RegisterRequest</a></div>
<div class="col-last odd-row-color">
<div class="block">注册请求DTO</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.example.springboot2.service">
<div class="caption"><span><a href="../service/package-summary.html">com.example.springboot2.service</a>使用的<a href="package-summary.html">com.example.springboot2.dto</a>中的类</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="class-use/LoginRequest.html#com.example.springboot2.service">LoginRequest</a></div>
<div class="col-last even-row-color">
<div class="block">登录请求DTO</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/LoginResponse.html#com.example.springboot2.service">LoginResponse</a></div>
<div class="col-last odd-row-color">
<div class="block">登录响应DTO</div>
</div>
<div class="col-first even-row-color"><a href="class-use/RegisterRequest.html#com.example.springboot2.service">RegisterRequest</a></div>
<div class="col-last even-row-color">
<div class="block">注册请求DTO</div>
</div>
</div>
</section>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
