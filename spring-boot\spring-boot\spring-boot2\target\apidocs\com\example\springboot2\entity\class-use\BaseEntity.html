<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed Jun 18 22:59:01 CST 2025 -->
<title>类 com.example.springboot2.entity.BaseEntity的使用 (spring-boot2 0.0.1-SNAPSHOT API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-06-18">
<meta name="description" content="use: package: com.example.springboot2.entity, class: BaseEntity">
<meta name="generator" content="javadoc/ClassUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-use-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../BaseEntity.html" title="com.example.springboot2.entity中的类">类</a></li>
<li class="nav-bar-cell1-rev">使用</li>
<li><a href="../package-tree.html">树</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html#use">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="类的使用 com.example.springboot2.entity.BaseEntity" class="title">类的使用<br>com.example.springboot2.entity.BaseEntity</h1>
</div>
<div class="caption"><span>使用<a href="../BaseEntity.html" title="com.example.springboot2.entity中的类">BaseEntity</a>的程序包</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">程序包</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="#com.example.springboot2.entity">com.example.springboot2.entity</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<section class="class-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.example.springboot2.entity">
<h2><a href="../package-summary.html">com.example.springboot2.entity</a>中<a href="../BaseEntity.html" title="com.example.springboot2.entity中的类">BaseEntity</a>的使用</h2>
<div class="caption"><span><a href="../package-summary.html">com.example.springboot2.entity</a>中<a href="../BaseEntity.html" title="com.example.springboot2.entity中的类">BaseEntity</a>的子类</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../Coupon.html" class="type-name-link" title="com.example.springboot2.entity中的类">Coupon</a></code></div>
<div class="col-last even-row-color">
<div class="block">优惠券实体类</div>
</div>
<div class="col-first odd-row-color"><code>class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="../Goods.html" class="type-name-link" title="com.example.springboot2.entity中的类">Goods</a></code></div>
<div class="col-last odd-row-color">
<div class="block">商品实体类</div>
</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../GoodsCategory.html" class="type-name-link" title="com.example.springboot2.entity中的类">GoodsCategory</a></code></div>
<div class="col-last even-row-color">
<div class="block">商品分类实体类</div>
</div>
<div class="col-first odd-row-color"><code>class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="../LaundryOrder.html" class="type-name-link" title="com.example.springboot2.entity中的类">LaundryOrder</a></code></div>
<div class="col-last odd-row-color">
<div class="block">洗护订单实体类</div>
</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../LaundryOrderItem.html" class="type-name-link" title="com.example.springboot2.entity中的类">LaundryOrderItem</a></code></div>
<div class="col-last even-row-color">
<div class="block">洗护订单项实体类</div>
</div>
<div class="col-first odd-row-color"><code>class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="../LaundryService.html" class="type-name-link" title="com.example.springboot2.entity中的类">LaundryService</a></code></div>
<div class="col-last odd-row-color">
<div class="block">洗护服务实体类</div>
</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../Merchant.html" class="type-name-link" title="com.example.springboot2.entity中的类">Merchant</a></code></div>
<div class="col-last even-row-color">
<div class="block">商家实体类</div>
</div>
<div class="col-first odd-row-color"><code>class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="../Order.html" class="type-name-link" title="com.example.springboot2.entity中的类">Order</a></code></div>
<div class="col-last odd-row-color">
<div class="block">订单实体类</div>
</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../OrderItem.html" class="type-name-link" title="com.example.springboot2.entity中的类">OrderItem</a></code></div>
<div class="col-last even-row-color">
<div class="block">订单项实体类</div>
</div>
<div class="col-first odd-row-color"><code>class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="../User.html" class="type-name-link" title="com.example.springboot2.entity中的类">User</a></code></div>
<div class="col-last odd-row-color">
<div class="block">用户实体类</div>
</div>
</div>
</section>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
