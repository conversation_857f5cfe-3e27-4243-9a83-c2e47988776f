<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed Jun 18 22:59:01 CST 2025 -->
<title>类 com.example.springboot2.entity.GoodsCategory的使用 (spring-boot2 0.0.1-SNAPSHOT API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-06-18">
<meta name="description" content="use: package: com.example.springboot2.entity, class: GoodsCategory">
<meta name="generator" content="javadoc/ClassUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-use-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">类</a></li>
<li class="nav-bar-cell1-rev">使用</li>
<li><a href="../package-tree.html">树</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html#use">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="类的使用 com.example.springboot2.entity.GoodsCategory" class="title">类的使用<br>com.example.springboot2.entity.GoodsCategory</h1>
</div>
<div class="caption"><span>使用<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>的程序包</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">程序包</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="#com.example.springboot2.controller">com.example.springboot2.controller</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.example.springboot2.repository">com.example.springboot2.repository</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.example.springboot2.service">com.example.springboot2.service</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<section class="class-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.example.springboot2.controller">
<h2><a href="../../controller/package-summary.html">com.example.springboot2.controller</a>中<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>的使用</h2>
<div class="caption"><span>返回变量类型为<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>的类型的<a href="../../controller/package-summary.html">com.example.springboot2.controller</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="../../common/Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsCategoryController.</span><code><a href="../../controller/GoodsCategoryController.html#createGoodsCategory(com.example.springboot2.entity.GoodsCategory,org.springframework.security.core.Authentication)" class="member-name-link">createGoodsCategory</a><wbr>(@Valid <a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&nbsp;category,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">创建商品分类</div>
</div>
<div class="col-first odd-row-color"><code><a href="../../common/Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsCategoryController.</span><code><a href="../../controller/GoodsCategoryController.html#getCategoriesTree(org.springframework.security.core.Authentication)" class="member-name-link">getCategoriesTree</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取所有分类（树形结构）</div>
</div>
<div class="col-first even-row-color"><code><a href="../../common/Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsCategoryController.</span><code><a href="../../controller/GoodsCategoryController.html#getCategoryDetail(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">getCategoryDetail</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取分类详情</div>
</div>
<div class="col-first odd-row-color"><code><a href="../../common/Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsCategoryController.</span><code><a href="../../controller/GoodsCategoryController.html#getGoodsCategories(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">getGoodsCategories</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;parentId,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取商品分类列表</div>
</div>
<div class="col-first even-row-color"><code><a href="../../common/Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsCategoryController.</span><code><a href="../../controller/GoodsCategoryController.html#updateGoodsCategory(java.lang.Long,com.example.springboot2.entity.GoodsCategory,org.springframework.security.core.Authentication)" class="member-name-link">updateGoodsCategory</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 @Valid <a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&nbsp;category,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">更新商品分类</div>
</div>
</div>
<div class="caption"><span>参数类型为<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>的<a href="../../controller/package-summary.html">com.example.springboot2.controller</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="../../common/Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsCategoryController.</span><code><a href="../../controller/GoodsCategoryController.html#createGoodsCategory(com.example.springboot2.entity.GoodsCategory,org.springframework.security.core.Authentication)" class="member-name-link">createGoodsCategory</a><wbr>(@Valid <a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&nbsp;category,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">创建商品分类</div>
</div>
<div class="col-first odd-row-color"><code><a href="../../common/Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsCategoryController.</span><code><a href="../../controller/GoodsCategoryController.html#updateGoodsCategory(java.lang.Long,com.example.springboot2.entity.GoodsCategory,org.springframework.security.core.Authentication)" class="member-name-link">updateGoodsCategory</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 @Valid <a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&nbsp;category,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">更新商品分类</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.example.springboot2.repository">
<h2><a href="../../repository/package-summary.html">com.example.springboot2.repository</a>中<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>的使用</h2>
<div class="caption"><span>返回变量类型为<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>的类型的<a href="../../repository/package-summary.html">com.example.springboot2.repository</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsCategoryRepository.</span><code><a href="../../repository/GoodsCategoryRepository.html#findByMerchantAndIsEnabledTrueOrderBySortOrder(com.example.springboot2.entity.Merchant)" class="member-name-link">findByMerchantAndIsEnabledTrueOrderBySortOrder</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</code></div>
<div class="col-last even-row-color">
<div class="block">根据商家查找分类</div>
</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsCategoryRepository.</span><code><a href="../../repository/GoodsCategoryRepository.html#findByMerchantAndParentAndIsEnabledTrueOrderBySortOrder(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.GoodsCategory)" class="member-name-link">findByMerchantAndParentAndIsEnabledTrueOrderBySortOrder</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&nbsp;parent)</code></div>
<div class="col-last odd-row-color">
<div class="block">根据商家和父分类查找子分类</div>
</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsCategoryRepository.</span><code><a href="../../repository/GoodsCategoryRepository.html#findByMerchantAndParentIsNullAndIsEnabledTrueOrderBySortOrder(com.example.springboot2.entity.Merchant)" class="member-name-link">findByMerchantAndParentIsNullAndIsEnabledTrueOrderBySortOrder</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</code></div>
<div class="col-last even-row-color">
<div class="block">根据商家查找顶级分类</div>
</div>
</div>
<div class="caption"><span>参数类型为<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>的<a href="../../repository/package-summary.html">com.example.springboot2.repository</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsCategoryRepository.</span><code><a href="../../repository/GoodsCategoryRepository.html#findByMerchantAndParentAndIsEnabledTrueOrderBySortOrder(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.GoodsCategory)" class="member-name-link">findByMerchantAndParentAndIsEnabledTrueOrderBySortOrder</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&nbsp;parent)</code></div>
<div class="col-last even-row-color">
<div class="block">根据商家和父分类查找子分类</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.example.springboot2.service">
<h2><a href="../../service/package-summary.html">com.example.springboot2.service</a>中<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>的使用</h2>
<div class="caption"><span>返回<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>的<a href="../../service/package-summary.html">com.example.springboot2.service</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsCategoryService.</span><code><a href="../../service/GoodsCategoryService.html#createGoodsCategory(java.lang.Long,com.example.springboot2.entity.GoodsCategory)" class="member-name-link">createGoodsCategory</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;userId,
 <a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&nbsp;category)</code></div>
<div class="col-last even-row-color">
<div class="block">创建商品分类</div>
</div>
<div class="col-first odd-row-color"><code><a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsCategoryService.</span><code><a href="../../service/GoodsCategoryService.html#findById(java.lang.Long)" class="member-name-link">findById</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id)</code></div>
<div class="col-last odd-row-color">
<div class="block">根据ID查找分类</div>
</div>
<div class="col-first even-row-color"><code><a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsCategoryService.</span><code><a href="../../service/GoodsCategoryService.html#getCategoryDetail(java.lang.Long,java.lang.Long)" class="member-name-link">getCategoryDetail</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;categoryId)</code></div>
<div class="col-last even-row-color">
<div class="block">获取分类详情</div>
</div>
<div class="col-first odd-row-color"><code><a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsCategoryService.</span><code><a href="../../service/GoodsCategoryService.html#updateGoodsCategory(java.lang.Long,java.lang.Long,com.example.springboot2.entity.GoodsCategory)" class="member-name-link">updateGoodsCategory</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;categoryId,
 <a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&nbsp;categoryInfo)</code></div>
<div class="col-last odd-row-color">
<div class="block">更新商品分类</div>
</div>
</div>
<div class="caption"><span>返回变量类型为<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>的类型的<a href="../../service/package-summary.html">com.example.springboot2.service</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsCategoryService.</span><code><a href="../../service/GoodsCategoryService.html#getGoodsCategories(java.lang.Long)" class="member-name-link">getGoodsCategories</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;userId)</code></div>
<div class="col-last even-row-color">
<div class="block">获取商家所有分类</div>
</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsCategoryService.</span><code><a href="../../service/GoodsCategoryService.html#getSubCategories(java.lang.Long,java.lang.Long)" class="member-name-link">getSubCategories</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;parentId)</code></div>
<div class="col-last odd-row-color">
<div class="block">获取子分类</div>
</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsCategoryService.</span><code><a href="../../service/GoodsCategoryService.html#getTopCategories(java.lang.Long)" class="member-name-link">getTopCategories</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;userId)</code></div>
<div class="col-last even-row-color">
<div class="block">获取顶级分类</div>
</div>
</div>
<div class="caption"><span>参数类型为<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>的<a href="../../service/package-summary.html">com.example.springboot2.service</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsCategoryService.</span><code><a href="../../service/GoodsCategoryService.html#createGoodsCategory(java.lang.Long,com.example.springboot2.entity.GoodsCategory)" class="member-name-link">createGoodsCategory</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;userId,
 <a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&nbsp;category)</code></div>
<div class="col-last even-row-color">
<div class="block">创建商品分类</div>
</div>
<div class="col-first odd-row-color"><code><a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsCategoryService.</span><code><a href="../../service/GoodsCategoryService.html#updateGoodsCategory(java.lang.Long,java.lang.Long,com.example.springboot2.entity.GoodsCategory)" class="member-name-link">updateGoodsCategory</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;categoryId,
 <a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&nbsp;categoryInfo)</code></div>
<div class="col-last odd-row-color">
<div class="block">更新商品分类</div>
</div>
</div>
</section>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
