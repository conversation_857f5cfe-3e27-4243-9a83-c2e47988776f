<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed Jun 18 22:33:52 CST 2025 -->
<title>类 com.example.springboot2.entity.LaundryOrder的使用 (spring-boot2 0.0.1-SNAPSHOT API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-06-18">
<meta name="description" content="use: package: com.example.springboot2.entity, class: LaundryOrder">
<meta name="generator" content="javadoc/ClassUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-use-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../LaundryOrder.html" title="com.example.springboot2.entity中的类">类</a></li>
<li class="nav-bar-cell1-rev">使用</li>
<li><a href="../package-tree.html">树</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html#use">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="类的使用 com.example.springboot2.entity.LaundryOrder" class="title">类的使用<br>com.example.springboot2.entity.LaundryOrder</h1>
</div>
<div class="caption"><span>使用<a href="../LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>的程序包</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">程序包</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="#com.example.springboot2.controller">com.example.springboot2.controller</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.example.springboot2.repository">com.example.springboot2.repository</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.example.springboot2.service">com.example.springboot2.service</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<section class="class-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.example.springboot2.controller">
<h2><a href="../../controller/package-summary.html">com.example.springboot2.controller</a>中<a href="../LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>的使用</h2>
<div class="caption"><span>返回变量类型为<a href="../LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>的类型的<a href="../../controller/package-summary.html">com.example.springboot2.controller</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="../../common/Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LaundryController.</span><code><a href="../../controller/LaundryController.html#getLaundryOrderDetail(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">getLaundryOrderDetail</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取洗护订单详情</div>
</div>
<div class="col-first odd-row-color"><code><a href="../../common/Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../../common/PageResult.html" title="com.example.springboot2.common中的类">PageResult</a>&lt;<a href="../LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LaundryController.</span><code><a href="../../controller/LaundryController.html#getLaundryOrders(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getLaundryOrders</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>&nbsp;current,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>&nbsp;size,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;orderNo,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;status,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">分页查询洗护订单列表</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.example.springboot2.repository">
<h2><a href="../../repository/package-summary.html">com.example.springboot2.repository</a>中<a href="../LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>的使用</h2>
<div class="caption"><span>返回变量类型为<a href="../LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>的类型的<a href="../../repository/package-summary.html">com.example.springboot2.repository</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>org.springframework.data.domain.Page&lt;<a href="../LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LaundryOrderRepository.</span><code><a href="../../repository/LaundryOrderRepository.html#findByCustomer(com.example.springboot2.entity.User,org.springframework.data.domain.Pageable)" class="member-name-link">findByCustomer</a><wbr>(<a href="../User.html" title="com.example.springboot2.entity中的类">User</a>&nbsp;customer,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last even-row-color">
<div class="block">根据客户查找订单</div>
</div>
<div class="col-first odd-row-color"><code>org.springframework.data.domain.Page&lt;<a href="../LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LaundryOrderRepository.</span><code><a href="../../repository/LaundryOrderRepository.html#findByMerchant(com.example.springboot2.entity.Merchant,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchant</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last odd-row-color">
<div class="block">根据商家查找订单</div>
</div>
<div class="col-first even-row-color"><code>org.springframework.data.domain.Page&lt;<a href="../LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LaundryOrderRepository.</span><code><a href="../../repository/LaundryOrderRepository.html#findByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.LaundryOrder.LaundryOrderStatus,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchantAndStatus</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../LaundryOrder.LaundryOrderStatus.html" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a>&nbsp;status,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last even-row-color">
<div class="block">根据商家和状态查找订单</div>
</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="java.util中的类或接口" class="external-link">Optional</a>&lt;<a href="../LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LaundryOrderRepository.</span><code><a href="../../repository/LaundryOrderRepository.html#findByOrderNo(java.lang.String)" class="member-name-link">findByOrderNo</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;orderNo)</code></div>
<div class="col-last odd-row-color">
<div class="block">根据订单号查找订单</div>
</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LaundryOrderRepository.</span><code><a href="../../repository/LaundryOrderRepository.html#findTodayOrdersByMerchant(com.example.springboot2.entity.Merchant)" class="member-name-link">findTodayOrdersByMerchant</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</code></div>
<div class="col-last even-row-color">
<div class="block">查找商家今日订单</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.example.springboot2.service">
<h2><a href="../../service/package-summary.html">com.example.springboot2.service</a>中<a href="../LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>的使用</h2>
<div class="caption"><span>返回<a href="../LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>的<a href="../../service/package-summary.html">com.example.springboot2.service</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="../LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LaundryBusinessService.</span><code><a href="../../service/LaundryBusinessService.html#getLaundryOrderDetail(java.lang.Long,java.lang.Long)" class="member-name-link">getLaundryOrderDetail</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;orderId)</code></div>
<div class="col-last even-row-color">
<div class="block">获取洗护订单详情</div>
</div>
</div>
<div class="caption"><span>返回变量类型为<a href="../LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>的类型的<a href="../../service/package-summary.html">com.example.springboot2.service</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="../../common/PageResult.html" title="com.example.springboot2.common中的类">PageResult</a>&lt;<a href="../LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LaundryBusinessService.</span><code><a href="../../service/LaundryBusinessService.html#getLaundryOrders(java.lang.Long,java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String)" class="member-name-link">getLaundryOrders</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>&nbsp;current,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>&nbsp;size,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;orderNo,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;status)</code></div>
<div class="col-last even-row-color">
<div class="block">分页查询洗护订单列表</div>
</div>
</div>
</section>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
