<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed Jun 18 22:59:01 CST 2025 -->
<title>类 com.example.springboot2.entity.Merchant的使用 (spring-boot2 0.0.1-SNAPSHOT API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-06-18">
<meta name="description" content="use: package: com.example.springboot2.entity, class: Merchant">
<meta name="generator" content="javadoc/ClassUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-use-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../Merchant.html" title="com.example.springboot2.entity中的类">类</a></li>
<li class="nav-bar-cell1-rev">使用</li>
<li><a href="../package-tree.html">树</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html#use">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="类的使用 com.example.springboot2.entity.Merchant" class="title">类的使用<br>com.example.springboot2.entity.Merchant</h1>
</div>
<div class="caption"><span>使用<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>的程序包</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">程序包</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="#com.example.springboot2.controller">com.example.springboot2.controller</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.example.springboot2.repository">com.example.springboot2.repository</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.example.springboot2.service">com.example.springboot2.service</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<section class="class-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.example.springboot2.controller">
<h2><a href="../../controller/package-summary.html">com.example.springboot2.controller</a>中<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>的使用</h2>
<div class="caption"><span>返回变量类型为<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>的类型的<a href="../../controller/package-summary.html">com.example.springboot2.controller</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="../../common/Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantController.</span><code><a href="../../controller/MerchantController.html#getMerchantInfo(org.springframework.security.core.Authentication)" class="member-name-link">getMerchantInfo</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取商家信息</div>
</div>
<div class="col-first odd-row-color"><code><a href="../../common/Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantController.</span><code><a href="../../controller/MerchantController.html#updateMerchantInfo(com.example.springboot2.entity.Merchant,org.springframework.security.core.Authentication)" class="member-name-link">updateMerchantInfo</a><wbr>(@Valid <a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchantInfo,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color">
<div class="block">更新商家信息</div>
</div>
</div>
<div class="caption"><span>参数类型为<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>的<a href="../../controller/package-summary.html">com.example.springboot2.controller</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="../../common/Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantController.</span><code><a href="../../controller/MerchantController.html#updateMerchantInfo(com.example.springboot2.entity.Merchant,org.springframework.security.core.Authentication)" class="member-name-link">updateMerchantInfo</a><wbr>(@Valid <a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchantInfo,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">更新商家信息</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.example.springboot2.repository">
<h2><a href="../../repository/package-summary.html">com.example.springboot2.repository</a>中<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>的使用</h2>
<div class="caption"><span>返回变量类型为<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>的类型的<a href="../../repository/package-summary.html">com.example.springboot2.repository</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="java.util中的类或接口" class="external-link">Optional</a>&lt;<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantRepository.</span><code><a href="../../repository/MerchantRepository.html#findByShopName(java.lang.String)" class="member-name-link">findByShopName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;shopName)</code></div>
<div class="col-last even-row-color">
<div class="block">根据店铺名称查找商家</div>
</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="java.util中的类或接口" class="external-link">Optional</a>&lt;<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantRepository.</span><code><a href="../../repository/MerchantRepository.html#findByUser(com.example.springboot2.entity.User)" class="member-name-link">findByUser</a><wbr>(<a href="../User.html" title="com.example.springboot2.entity中的类">User</a>&nbsp;user)</code></div>
<div class="col-last odd-row-color">
<div class="block">根据用户查找商家</div>
</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="java.util中的类或接口" class="external-link">Optional</a>&lt;<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantRepository.</span><code><a href="../../repository/MerchantRepository.html#findByUserId(java.lang.Long)" class="member-name-link">findByUserId</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;userId)</code></div>
<div class="col-last even-row-color">
<div class="block">根据用户ID查找商家</div>
</div>
</div>
<div class="caption"><span>参数类型为<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>的<a href="../../repository/package-summary.html">com.example.springboot2.repository</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>long</code></div>
<div class="col-second even-row-color"><span class="type-name-label">CouponRepository.</span><code><a href="../../repository/CouponRepository.html#countByMerchant(com.example.springboot2.entity.Merchant)" class="member-name-link">countByMerchant</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</code></div>
<div class="col-last even-row-color">
<div class="block">统计商家优惠券数量</div>
</div>
<div class="col-first odd-row-color"><code>long</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsRepository.</span><code><a href="../../repository/GoodsRepository.html#countByMerchant(com.example.springboot2.entity.Merchant)" class="member-name-link">countByMerchant</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</code></div>
<div class="col-last odd-row-color">
<div class="block">根据商家查找商品数量</div>
</div>
<div class="col-first even-row-color"><code>long</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LaundryOrderRepository.</span><code><a href="../../repository/LaundryOrderRepository.html#countByMerchant(com.example.springboot2.entity.Merchant)" class="member-name-link">countByMerchant</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</code></div>
<div class="col-last even-row-color">
<div class="block">统计商家订单数量</div>
</div>
<div class="col-first odd-row-color"><code>long</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LaundryServiceRepository.</span><code><a href="../../repository/LaundryServiceRepository.html#countByMerchant(com.example.springboot2.entity.Merchant)" class="member-name-link">countByMerchant</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</code></div>
<div class="col-last odd-row-color">
<div class="block">统计商家服务数量</div>
</div>
<div class="col-first even-row-color"><code>long</code></div>
<div class="col-second even-row-color"><span class="type-name-label">OrderRepository.</span><code><a href="../../repository/OrderRepository.html#countByMerchant(com.example.springboot2.entity.Merchant)" class="member-name-link">countByMerchant</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</code></div>
<div class="col-last even-row-color">
<div class="block">统计商家订单数量</div>
</div>
<div class="col-first odd-row-color"><code>long</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LaundryOrderRepository.</span><code><a href="../../repository/LaundryOrderRepository.html#countByMerchantAndCreatedTimeBetween(com.example.springboot2.entity.Merchant,java.time.LocalDateTime,java.time.LocalDateTime)" class="member-name-link">countByMerchantAndCreatedTimeBetween</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDateTime.html" title="java.time中的类或接口" class="external-link">LocalDateTime</a>&nbsp;startTime,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDateTime.html" title="java.time中的类或接口" class="external-link">LocalDateTime</a>&nbsp;endTime)</code></div>
<div class="col-last odd-row-color">
<div class="block">统计商家指定时间范围内的订单数量</div>
</div>
<div class="col-first even-row-color"><code>long</code></div>
<div class="col-second even-row-color"><span class="type-name-label">OrderRepository.</span><code><a href="../../repository/OrderRepository.html#countByMerchantAndCreatedTimeBetween(com.example.springboot2.entity.Merchant,java.time.LocalDateTime,java.time.LocalDateTime)" class="member-name-link">countByMerchantAndCreatedTimeBetween</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDateTime.html" title="java.time中的类或接口" class="external-link">LocalDateTime</a>&nbsp;startTime,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDateTime.html" title="java.time中的类或接口" class="external-link">LocalDateTime</a>&nbsp;endTime)</code></div>
<div class="col-last even-row-color">
<div class="block">统计商家指定时间范围内的订单数量</div>
</div>
<div class="col-first odd-row-color"><code>long</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LaundryServiceRepository.</span><code><a href="../../repository/LaundryServiceRepository.html#countByMerchantAndIsEnabledTrue(com.example.springboot2.entity.Merchant)" class="member-name-link">countByMerchantAndIsEnabledTrue</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</code></div>
<div class="col-last odd-row-color">
<div class="block">统计商家启用的服务数量</div>
</div>
<div class="col-first even-row-color"><code>long</code></div>
<div class="col-second even-row-color"><span class="type-name-label">CouponRepository.</span><code><a href="../../repository/CouponRepository.html#countByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Coupon.CouponStatus)" class="member-name-link">countByMerchantAndStatus</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../Coupon.CouponStatus.html" title="enum class in com.example.springboot2.entity">Coupon.CouponStatus</a>&nbsp;status)</code></div>
<div class="col-last even-row-color">
<div class="block">统计商家指定状态优惠券数量</div>
</div>
<div class="col-first odd-row-color"><code>long</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsRepository.</span><code><a href="../../repository/GoodsRepository.html#countByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Goods.GoodsStatus)" class="member-name-link">countByMerchantAndStatus</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a>&nbsp;status)</code></div>
<div class="col-last odd-row-color">
<div class="block">根据商家和状态查找商品数量</div>
</div>
<div class="col-first even-row-color"><code>long</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LaundryOrderRepository.</span><code><a href="../../repository/LaundryOrderRepository.html#countByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.LaundryOrder.LaundryOrderStatus)" class="member-name-link">countByMerchantAndStatus</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../LaundryOrder.LaundryOrderStatus.html" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a>&nbsp;status)</code></div>
<div class="col-last even-row-color">
<div class="block">统计商家指定状态订单数量</div>
</div>
<div class="col-first odd-row-color"><code>long</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">OrderRepository.</span><code><a href="../../repository/OrderRepository.html#countByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Order.OrderStatus)" class="member-name-link">countByMerchantAndStatus</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../Order.OrderStatus.html" title="enum class in com.example.springboot2.entity">Order.OrderStatus</a>&nbsp;status)</code></div>
<div class="col-last odd-row-color">
<div class="block">统计商家指定状态订单数量</div>
</div>
<div class="col-first even-row-color"><code>org.springframework.data.domain.Page&lt;<a href="../Coupon.html" title="com.example.springboot2.entity中的类">Coupon</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">CouponRepository.</span><code><a href="../../repository/CouponRepository.html#findByMerchant(com.example.springboot2.entity.Merchant,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchant</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last even-row-color">
<div class="block">根据商家查找优惠券</div>
</div>
<div class="col-first odd-row-color"><code>org.springframework.data.domain.Page&lt;<a href="../Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsRepository.</span><code><a href="../../repository/GoodsRepository.html#findByMerchant(com.example.springboot2.entity.Merchant,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchant</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last odd-row-color">
<div class="block">根据商家查找商品</div>
</div>
<div class="col-first even-row-color"><code>org.springframework.data.domain.Page&lt;<a href="../LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LaundryOrderRepository.</span><code><a href="../../repository/LaundryOrderRepository.html#findByMerchant(com.example.springboot2.entity.Merchant,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchant</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last even-row-color">
<div class="block">根据商家查找订单</div>
</div>
<div class="col-first odd-row-color"><code>org.springframework.data.domain.Page&lt;<a href="../LaundryService.html" title="com.example.springboot2.entity中的类">LaundryService</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LaundryServiceRepository.</span><code><a href="../../repository/LaundryServiceRepository.html#findByMerchant(com.example.springboot2.entity.Merchant,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchant</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last odd-row-color">
<div class="block">根据商家查找服务</div>
</div>
<div class="col-first even-row-color"><code>org.springframework.data.domain.Page&lt;<a href="../Order.html" title="com.example.springboot2.entity中的类">Order</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">OrderRepository.</span><code><a href="../../repository/OrderRepository.html#findByMerchant(com.example.springboot2.entity.Merchant,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchant</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last even-row-color">
<div class="block">根据商家查找订单</div>
</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsCategoryRepository.</span><code><a href="../../repository/GoodsCategoryRepository.html#findByMerchantAndIsEnabledTrueOrderBySortOrder(com.example.springboot2.entity.Merchant)" class="member-name-link">findByMerchantAndIsEnabledTrueOrderBySortOrder</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</code></div>
<div class="col-last odd-row-color">
<div class="block">根据商家查找分类</div>
</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../LaundryService.html" title="com.example.springboot2.entity中的类">LaundryService</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LaundryServiceRepository.</span><code><a href="../../repository/LaundryServiceRepository.html#findByMerchantAndIsEnabledTrueOrderBySortOrder(com.example.springboot2.entity.Merchant)" class="member-name-link">findByMerchantAndIsEnabledTrueOrderBySortOrder</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</code></div>
<div class="col-last even-row-color">
<div class="block">根据商家查找启用的服务</div>
</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsRepository.</span><code><a href="../../repository/GoodsRepository.html#findByMerchantAndIsNewTrueAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Goods.GoodsStatus,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchantAndIsNewTrueAndStatus</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a>&nbsp;status,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last odd-row-color">
<div class="block">查找新品</div>
</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsRepository.</span><code><a href="../../repository/GoodsRepository.html#findByMerchantAndIsRecommendedTrueAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Goods.GoodsStatus,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchantAndIsRecommendedTrueAndStatus</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a>&nbsp;status,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last even-row-color">
<div class="block">查找推荐商品</div>
</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsCategoryRepository.</span><code><a href="../../repository/GoodsCategoryRepository.html#findByMerchantAndParentAndIsEnabledTrueOrderBySortOrder(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.GoodsCategory)" class="member-name-link">findByMerchantAndParentAndIsEnabledTrueOrderBySortOrder</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&nbsp;parent)</code></div>
<div class="col-last odd-row-color">
<div class="block">根据商家和父分类查找子分类</div>
</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsCategoryRepository.</span><code><a href="../../repository/GoodsCategoryRepository.html#findByMerchantAndParentIsNullAndIsEnabledTrueOrderBySortOrder(com.example.springboot2.entity.Merchant)" class="member-name-link">findByMerchantAndParentIsNullAndIsEnabledTrueOrderBySortOrder</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</code></div>
<div class="col-last even-row-color">
<div class="block">根据商家查找顶级分类</div>
</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../LaundryService.html" title="com.example.springboot2.entity中的类">LaundryService</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LaundryServiceRepository.</span><code><a href="../../repository/LaundryServiceRepository.html#findByMerchantAndServiceTypeAndIsEnabledTrue(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.LaundryService.ServiceType)" class="member-name-link">findByMerchantAndServiceTypeAndIsEnabledTrue</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../LaundryService.ServiceType.html" title="enum class in com.example.springboot2.entity">LaundryService.ServiceType</a>&nbsp;serviceType)</code></div>
<div class="col-last odd-row-color">
<div class="block">根据商家和服务类型查找服务</div>
</div>
<div class="col-first even-row-color"><code>org.springframework.data.domain.Page&lt;<a href="../Coupon.html" title="com.example.springboot2.entity中的类">Coupon</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">CouponRepository.</span><code><a href="../../repository/CouponRepository.html#findByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Coupon.CouponStatus,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchantAndStatus</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../Coupon.CouponStatus.html" title="enum class in com.example.springboot2.entity">Coupon.CouponStatus</a>&nbsp;status,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last even-row-color">
<div class="block">根据商家和状态查找优惠券</div>
</div>
<div class="col-first odd-row-color"><code>org.springframework.data.domain.Page&lt;<a href="../Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoodsRepository.</span><code><a href="../../repository/GoodsRepository.html#findByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Goods.GoodsStatus,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchantAndStatus</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a>&nbsp;status,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last odd-row-color">
<div class="block">根据商家和状态查找商品</div>
</div>
<div class="col-first even-row-color"><code>org.springframework.data.domain.Page&lt;<a href="../LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LaundryOrderRepository.</span><code><a href="../../repository/LaundryOrderRepository.html#findByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.LaundryOrder.LaundryOrderStatus,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchantAndStatus</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../LaundryOrder.LaundryOrderStatus.html" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a>&nbsp;status,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last even-row-color">
<div class="block">根据商家和状态查找订单</div>
</div>
<div class="col-first odd-row-color"><code>org.springframework.data.domain.Page&lt;<a href="../Order.html" title="com.example.springboot2.entity中的类">Order</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">OrderRepository.</span><code><a href="../../repository/OrderRepository.html#findByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Order.OrderStatus,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchantAndStatus</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../Order.OrderStatus.html" title="enum class in com.example.springboot2.entity">Order.OrderStatus</a>&nbsp;status,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last odd-row-color">
<div class="block">根据商家和状态查找订单</div>
</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsRepository.</span><code><a href="../../repository/GoodsRepository.html#findHotGoodsByMerchant(com.example.springboot2.entity.Merchant,org.springframework.data.domain.Pageable)" class="member-name-link">findHotGoodsByMerchant</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last even-row-color">
<div class="block">查找热销商品</div>
</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LaundryOrderRepository.</span><code><a href="../../repository/LaundryOrderRepository.html#findTodayOrdersByMerchant(com.example.springboot2.entity.Merchant)" class="member-name-link">findTodayOrdersByMerchant</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</code></div>
<div class="col-last odd-row-color">
<div class="block">查找商家今日订单</div>
</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../Order.html" title="com.example.springboot2.entity中的类">Order</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">OrderRepository.</span><code><a href="../../repository/OrderRepository.html#findTodayOrdersByMerchant(com.example.springboot2.entity.Merchant)" class="member-name-link">findTodayOrdersByMerchant</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</code></div>
<div class="col-last even-row-color">
<div class="block">查找商家今日订单</div>
</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../Coupon.html" title="com.example.springboot2.entity中的类">Coupon</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">CouponRepository.</span><code><a href="../../repository/CouponRepository.html#findValidCouponsByMerchant(com.example.springboot2.entity.Merchant,java.time.LocalDateTime)" class="member-name-link">findValidCouponsByMerchant</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDateTime.html" title="java.time中的类或接口" class="external-link">LocalDateTime</a>&nbsp;now)</code></div>
<div class="col-last odd-row-color">
<div class="block">查找有效的优惠券</div>
</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/math/BigDecimal.html" title="java.math中的类或接口" class="external-link">BigDecimal</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LaundryOrderRepository.</span><code><a href="../../repository/LaundryOrderRepository.html#sumActualAmountByMerchantAndCreatedTimeBetween(com.example.springboot2.entity.Merchant,java.time.LocalDateTime,java.time.LocalDateTime)" class="member-name-link">sumActualAmountByMerchantAndCreatedTimeBetween</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDateTime.html" title="java.time中的类或接口" class="external-link">LocalDateTime</a>&nbsp;startTime,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDateTime.html" title="java.time中的类或接口" class="external-link">LocalDateTime</a>&nbsp;endTime)</code></div>
<div class="col-last even-row-color">
<div class="block">统计商家指定时间范围内的销售额</div>
</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/math/BigDecimal.html" title="java.math中的类或接口" class="external-link">BigDecimal</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">OrderRepository.</span><code><a href="../../repository/OrderRepository.html#sumActualAmountByMerchantAndCreatedTimeBetween(com.example.springboot2.entity.Merchant,java.time.LocalDateTime,java.time.LocalDateTime)" class="member-name-link">sumActualAmountByMerchantAndCreatedTimeBetween</a><wbr>(<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDateTime.html" title="java.time中的类或接口" class="external-link">LocalDateTime</a>&nbsp;startTime,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDateTime.html" title="java.time中的类或接口" class="external-link">LocalDateTime</a>&nbsp;endTime)</code></div>
<div class="col-last odd-row-color">
<div class="block">统计商家指定时间范围内的销售额</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.example.springboot2.service">
<h2><a href="../../service/package-summary.html">com.example.springboot2.service</a>中<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>的使用</h2>
<div class="caption"><span>返回<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>的<a href="../../service/package-summary.html">com.example.springboot2.service</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantService.</span><code><a href="../../service/MerchantService.html#createMerchant(java.lang.Long,com.example.springboot2.entity.Merchant)" class="member-name-link">createMerchant</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;userId,
 <a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</code></div>
<div class="col-last even-row-color">
<div class="block">创建商家</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantService.</span><code><a href="../../service/MerchantService.html#findById(java.lang.Long)" class="member-name-link">findById</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;id)</code></div>
<div class="col-last odd-row-color">
<div class="block">根据ID查找商家</div>
</div>
<div class="col-first even-row-color"><code><a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantService.</span><code><a href="../../service/MerchantService.html#getMerchantInfo(java.lang.Long)" class="member-name-link">getMerchantInfo</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;userId)</code></div>
<div class="col-last even-row-color">
<div class="block">获取商家信息</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantService.</span><code><a href="../../service/MerchantService.html#updateMerchantInfo(java.lang.Long,com.example.springboot2.entity.Merchant)" class="member-name-link">updateMerchantInfo</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;userId,
 <a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchantInfo)</code></div>
<div class="col-last odd-row-color">
<div class="block">更新商家信息</div>
</div>
</div>
<div class="caption"><span>返回变量类型为<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>的类型的<a href="../../service/package-summary.html">com.example.springboot2.service</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="java.util中的类或接口" class="external-link">Optional</a>&lt;<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantService.</span><code><a href="../../service/MerchantService.html#findByUserId(java.lang.Long)" class="member-name-link">findByUserId</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;userId)</code></div>
<div class="col-last even-row-color">
<div class="block">根据用户ID查找商家</div>
</div>
</div>
<div class="caption"><span>参数类型为<a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>的<a href="../../service/package-summary.html">com.example.springboot2.service</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">MerchantService.</span><code><a href="../../service/MerchantService.html#createMerchant(java.lang.Long,com.example.springboot2.entity.Merchant)" class="member-name-link">createMerchant</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;userId,
 <a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</code></div>
<div class="col-last even-row-color">
<div class="block">创建商家</div>
</div>
<div class="col-first odd-row-color"><code><a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">MerchantService.</span><code><a href="../../service/MerchantService.html#updateMerchantInfo(java.lang.Long,com.example.springboot2.entity.Merchant)" class="member-name-link">updateMerchantInfo</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;userId,
 <a href="../Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchantInfo)</code></div>
<div class="col-last odd-row-color">
<div class="block">更新商家信息</div>
</div>
</div>
</section>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
