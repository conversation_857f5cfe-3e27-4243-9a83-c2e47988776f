<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed Jun 18 22:33:52 CST 2025 -->
<title>com.example.springboot2.entity (spring-boot2 0.0.1-SNAPSHOT API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-06-18">
<meta name="description" content="declaration: package: com.example.springboot2.entity">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../index.html">概览</a></li>
<li class="nav-bar-cell1-rev">程序包</li>
<li>类</li>
<li><a href="package-use.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html#package">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="程序包 com.example.springboot2.entity" class="title">程序包 com.example.springboot2.entity</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">com.example.springboot2.entity</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">程序包</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="../package-summary.html">com.example.springboot2</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">类</button><button id="class-summary-tab3" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab3', 2)" class="table-tab">Enum Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="BaseEntity.html" title="com.example.springboot2.entity中的类">BaseEntity</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">基础实体类</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Coupon.html" title="com.example.springboot2.entity中的类">Coupon</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">优惠券实体类</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab3"><a href="Coupon.CouponStatus.html" title="enum class in com.example.springboot2.entity">Coupon.CouponStatus</a></div>
<div class="col-last even-row-color class-summary class-summary-tab3">
<div class="block">优惠券状态枚举</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab3"><a href="Coupon.CouponType.html" title="enum class in com.example.springboot2.entity">Coupon.CouponType</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab3">
<div class="block">优惠券类型枚举</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Goods.html" title="com.example.springboot2.entity中的类">Goods</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">商品实体类</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab3"><a href="Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab3">
<div class="block">商品状态枚举</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab3"><a href="Goods.GoodsType.html" title="enum class in com.example.springboot2.entity">Goods.GoodsType</a></div>
<div class="col-last even-row-color class-summary class-summary-tab3">
<div class="block">商品类型枚举</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">商品分类实体类</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">洗护订单实体类</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab3"><a href="LaundryOrder.LaundryOrderStatus.html" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab3">
<div class="block">洗护订单状态枚举</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="LaundryOrderItem.html" title="com.example.springboot2.entity中的类">LaundryOrderItem</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">洗护订单项实体类</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="LaundryService.html" title="com.example.springboot2.entity中的类">LaundryService</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">洗护服务实体类</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab3"><a href="LaundryService.ServiceType.html" title="enum class in com.example.springboot2.entity">LaundryService.ServiceType</a></div>
<div class="col-last even-row-color class-summary class-summary-tab3">
<div class="block">洗护服务类型枚举</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">商家实体类</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab3"><a href="Merchant.CertificationStatus.html" title="enum class in com.example.springboot2.entity">Merchant.CertificationStatus</a></div>
<div class="col-last even-row-color class-summary class-summary-tab3">
<div class="block">认证状态枚举</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab3"><a href="Merchant.MerchantStatus.html" title="enum class in com.example.springboot2.entity">Merchant.MerchantStatus</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab3">
<div class="block">商家状态枚举</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Order.html" title="com.example.springboot2.entity中的类">Order</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">订单实体类</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab3"><a href="Order.OrderStatus.html" title="enum class in com.example.springboot2.entity">Order.OrderStatus</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab3">
<div class="block">订单状态枚举</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab3"><a href="Order.OrderType.html" title="enum class in com.example.springboot2.entity">Order.OrderType</a></div>
<div class="col-last even-row-color class-summary class-summary-tab3">
<div class="block">订单类型枚举</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="OrderItem.html" title="com.example.springboot2.entity中的类">OrderItem</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">订单项实体类</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="User.html" title="com.example.springboot2.entity中的类">User</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">用户实体类</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab3"><a href="User.UserRole.html" title="enum class in com.example.springboot2.entity">User.UserRole</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab3">
<div class="block">用户角色枚举</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab3"><a href="User.UserStatus.html" title="enum class in com.example.springboot2.entity">User.UserStatus</a></div>
<div class="col-last even-row-color class-summary class-summary-tab3">
<div class="block">用户状态枚举</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
