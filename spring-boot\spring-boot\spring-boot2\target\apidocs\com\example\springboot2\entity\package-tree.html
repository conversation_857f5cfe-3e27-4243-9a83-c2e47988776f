<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed Jun 18 22:59:01 CST 2025 -->
<title>com.example.springboot2.entity 类分层结构 (spring-boot2 0.0.1-SNAPSHOT API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-06-18">
<meta name="description" content="tree: package: com.example.springboot2.entity">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li>使用</li>
<li class="nav-bar-cell1-rev">树</li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html#tree">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">程序包com.example.springboot2.entity的分层结构</h1>
<span class="package-hierarchy-label">程序包分层结构:</span>
<ul class="horizontal">
<li><a href="../../../../overview-tree.html">所有程序包</a></li>
</ul>
</div>
<section class="hierarchy">
<h2 title="类分层结构">类分层结构</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="java.lang中的类或接口">Object</a>
<ul>
<li class="circle">com.example.springboot2.entity.<a href="BaseEntity.html" class="type-name-link" title="com.example.springboot2.entity中的类">BaseEntity</a>
<ul>
<li class="circle">com.example.springboot2.entity.<a href="Coupon.html" class="type-name-link" title="com.example.springboot2.entity中的类">Coupon</a></li>
<li class="circle">com.example.springboot2.entity.<a href="Goods.html" class="type-name-link" title="com.example.springboot2.entity中的类">Goods</a></li>
<li class="circle">com.example.springboot2.entity.<a href="GoodsCategory.html" class="type-name-link" title="com.example.springboot2.entity中的类">GoodsCategory</a></li>
<li class="circle">com.example.springboot2.entity.<a href="LaundryOrder.html" class="type-name-link" title="com.example.springboot2.entity中的类">LaundryOrder</a></li>
<li class="circle">com.example.springboot2.entity.<a href="LaundryOrderItem.html" class="type-name-link" title="com.example.springboot2.entity中的类">LaundryOrderItem</a></li>
<li class="circle">com.example.springboot2.entity.<a href="LaundryService.html" class="type-name-link" title="com.example.springboot2.entity中的类">LaundryService</a></li>
<li class="circle">com.example.springboot2.entity.<a href="Merchant.html" class="type-name-link" title="com.example.springboot2.entity中的类">Merchant</a></li>
<li class="circle">com.example.springboot2.entity.<a href="Order.html" class="type-name-link" title="com.example.springboot2.entity中的类">Order</a></li>
<li class="circle">com.example.springboot2.entity.<a href="OrderItem.html" class="type-name-link" title="com.example.springboot2.entity中的类">OrderItem</a></li>
<li class="circle">com.example.springboot2.entity.<a href="User.html" class="type-name-link" title="com.example.springboot2.entity中的类">User</a> (implements org.springframework.security.core.userdetails.UserDetails)</li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Enum Class Hierarchy">Enum Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="java.lang中的类或接口">Object</a>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html" class="type-name-link external-link" title="java.lang中的类或接口">Enum</a>&lt;E&gt; (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Comparable.html" title="java.lang中的类或接口" class="external-link">Comparable</a>&lt;T&gt;, java.lang.constant.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/constant/Constable.html" title="java.lang.constant中的类或接口" class="external-link">Constable</a>, java.io.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="java.io中的类或接口" class="external-link">Serializable</a>)
<ul>
<li class="circle">com.example.springboot2.entity.<a href="Coupon.CouponStatus.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Coupon.CouponStatus</a></li>
<li class="circle">com.example.springboot2.entity.<a href="Coupon.CouponType.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Coupon.CouponType</a></li>
<li class="circle">com.example.springboot2.entity.<a href="Goods.GoodsStatus.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a></li>
<li class="circle">com.example.springboot2.entity.<a href="Goods.GoodsType.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Goods.GoodsType</a></li>
<li class="circle">com.example.springboot2.entity.<a href="LaundryOrder.LaundryOrderStatus.html" class="type-name-link" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a></li>
<li class="circle">com.example.springboot2.entity.<a href="LaundryService.ServiceType.html" class="type-name-link" title="enum class in com.example.springboot2.entity">LaundryService.ServiceType</a></li>
<li class="circle">com.example.springboot2.entity.<a href="Merchant.CertificationStatus.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Merchant.CertificationStatus</a></li>
<li class="circle">com.example.springboot2.entity.<a href="Merchant.MerchantStatus.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Merchant.MerchantStatus</a></li>
<li class="circle">com.example.springboot2.entity.<a href="Order.OrderStatus.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Order.OrderStatus</a></li>
<li class="circle">com.example.springboot2.entity.<a href="Order.OrderType.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Order.OrderType</a></li>
<li class="circle">com.example.springboot2.entity.<a href="User.UserRole.html" class="type-name-link" title="enum class in com.example.springboot2.entity">User.UserRole</a></li>
<li class="circle">com.example.springboot2.entity.<a href="User.UserStatus.html" class="type-name-link" title="enum class in com.example.springboot2.entity">User.UserStatus</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
