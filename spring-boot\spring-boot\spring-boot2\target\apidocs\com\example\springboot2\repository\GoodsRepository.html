<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed Jun 18 22:33:52 CST 2025 -->
<title>GoodsRepository (spring-boot2 0.0.1-SNAPSHOT API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-06-18">
<meta name="description" content="declaration: package: com.example.springboot2.repository, interface: GoodsRepository">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="class-use/GoodsRepository.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html#class">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.example.springboot2.repository</a></div>
<h1 title="接口 GoodsRepository" class="title">接口 GoodsRepository</h1>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>所有超级接口:</dt>
<dd><code>org.springframework.data.repository.CrudRepository&lt;<a href="../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&gt;</code>, <code>org.springframework.data.jpa.repository.JpaRepository&lt;<a href="../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&gt;</code>, <code>org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;<a href="../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;</code>, <code>org.springframework.data.repository.ListCrudRepository&lt;<a href="../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&gt;</code>, <code>org.springframework.data.repository.ListPagingAndSortingRepository&lt;<a href="../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&gt;</code>, <code>org.springframework.data.repository.PagingAndSortingRepository&lt;<a href="../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&gt;</code>, <code>org.springframework.data.repository.query.QueryByExampleExecutor&lt;<a href="../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;</code>, <code>org.springframework.data.repository.Repository&lt;<a href="../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&gt;</code></dd>
</dl>
<hr>
<div class="type-signature"><span class="annotations">@Repository
</span><span class="modifiers">public interface </span><span class="element-name type-name-label">GoodsRepository</span><span class="extends-implements">
extends org.springframework.data.jpa.repository.JpaRepository&lt;<a href="../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&gt;, org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;<a href="../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;</span></div>
<div class="block">商品Repository</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">抽象方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#countByMerchant(com.example.springboot2.entity.Merchant)" class="member-name-link">countByMerchant</a><wbr>(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">根据商家查找商品数量</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#countByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Goods.GoodsStatus)" class="member-name-link">countByMerchantAndStatus</a><wbr>(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../entity/Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a>&nbsp;status)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">根据商家和状态查找商品数量</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>org.springframework.data.domain.Page&lt;<a href="../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#findByMerchant(com.example.springboot2.entity.Merchant,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchant</a><wbr>(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">根据商家查找商品</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#findByMerchantAndIsNewTrueAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Goods.GoodsStatus,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchantAndIsNewTrueAndStatus</a><wbr>(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../entity/Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a>&nbsp;status,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">查找新品</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#findByMerchantAndIsRecommendedTrueAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Goods.GoodsStatus,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchantAndIsRecommendedTrueAndStatus</a><wbr>(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../entity/Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a>&nbsp;status,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">查找推荐商品</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>org.springframework.data.domain.Page&lt;<a href="../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#findByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Goods.GoodsStatus,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchantAndStatus</a><wbr>(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../entity/Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a>&nbsp;status,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">根据商家和状态查找商品</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#findHotGoodsByMerchant(com.example.springboot2.entity.Merchant,org.springframework.data.domain.Pageable)" class="member-name-link">findHotGoodsByMerchant</a><wbr>(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">查找热销商品</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.springframework.data.repository.CrudRepository">从接口继承的方法&nbsp;org.springframework.data.repository.CrudRepository</h3>
<code>count, delete, deleteAll, deleteAll, deleteAllById, deleteById, existsById, findById, save</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.springframework.data.jpa.repository.JpaRepository">从接口继承的方法&nbsp;org.springframework.data.jpa.repository.JpaRepository</h3>
<code>deleteAllByIdInBatch, deleteAllInBatch, deleteAllInBatch, deleteInBatch, findAll, findAll, flush, getById, getOne, getReferenceById, saveAllAndFlush, saveAndFlush</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.springframework.data.jpa.repository.JpaSpecificationExecutor">从接口继承的方法&nbsp;org.springframework.data.jpa.repository.JpaSpecificationExecutor</h3>
<code>count, delete, exists, findAll, findAll, findAll, findBy, findOne</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.springframework.data.repository.ListCrudRepository">从接口继承的方法&nbsp;org.springframework.data.repository.ListCrudRepository</h3>
<code>findAll, findAllById, saveAll</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.springframework.data.repository.ListPagingAndSortingRepository">从接口继承的方法&nbsp;org.springframework.data.repository.ListPagingAndSortingRepository</h3>
<code>findAll</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.springframework.data.repository.PagingAndSortingRepository">从接口继承的方法&nbsp;org.springframework.data.repository.PagingAndSortingRepository</h3>
<code>findAll</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.springframework.data.repository.query.QueryByExampleExecutor">从接口继承的方法&nbsp;org.springframework.data.repository.query.QueryByExampleExecutor</h3>
<code>count, exists, findAll, findBy, findOne</code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="findByMerchant(com.example.springboot2.entity.Merchant,org.springframework.data.domain.Pageable)">
<h3>findByMerchant</h3>
<div class="member-signature"><span class="return-type">org.springframework.data.domain.Page&lt;<a href="../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;</span>&nbsp;<span class="element-name">findByMerchant</span><wbr><span class="parameters">(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 org.springframework.data.domain.Pageable&nbsp;pageable)</span></div>
<div class="block">根据商家查找商品</div>
</section>
</li>
<li>
<section class="detail" id="findByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Goods.GoodsStatus,org.springframework.data.domain.Pageable)">
<h3>findByMerchantAndStatus</h3>
<div class="member-signature"><span class="return-type">org.springframework.data.domain.Page&lt;<a href="../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;</span>&nbsp;<span class="element-name">findByMerchantAndStatus</span><wbr><span class="parameters">(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../entity/Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a>&nbsp;status,
 org.springframework.data.domain.Pageable&nbsp;pageable)</span></div>
<div class="block">根据商家和状态查找商品</div>
</section>
</li>
<li>
<section class="detail" id="countByMerchant(com.example.springboot2.entity.Merchant)">
<h3>countByMerchant</h3>
<div class="member-signature"><span class="return-type">long</span>&nbsp;<span class="element-name">countByMerchant</span><wbr><span class="parameters">(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</span></div>
<div class="block">根据商家查找商品数量</div>
</section>
</li>
<li>
<section class="detail" id="countByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Goods.GoodsStatus)">
<h3>countByMerchantAndStatus</h3>
<div class="member-signature"><span class="return-type">long</span>&nbsp;<span class="element-name">countByMerchantAndStatus</span><wbr><span class="parameters">(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../entity/Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a>&nbsp;status)</span></div>
<div class="block">根据商家和状态查找商品数量</div>
</section>
</li>
<li>
<section class="detail" id="findHotGoodsByMerchant(com.example.springboot2.entity.Merchant,org.springframework.data.domain.Pageable)">
<h3>findHotGoodsByMerchant</h3>
<div class="member-signature"><span class="annotations">@Query("SELECT g FROM Goods g WHERE g.merchant = :merchant AND g.status = \'ON_SALE\' ORDER BY g.salesCount DESC")
</span><span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;</span>&nbsp;<span class="element-name">findHotGoodsByMerchant</span><wbr><span class="parameters">(@Param("merchant")
 <a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 org.springframework.data.domain.Pageable&nbsp;pageable)</span></div>
<div class="block">查找热销商品</div>
</section>
</li>
<li>
<section class="detail" id="findByMerchantAndIsRecommendedTrueAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Goods.GoodsStatus,org.springframework.data.domain.Pageable)">
<h3>findByMerchantAndIsRecommendedTrueAndStatus</h3>
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;</span>&nbsp;<span class="element-name">findByMerchantAndIsRecommendedTrueAndStatus</span><wbr><span class="parameters">(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../entity/Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a>&nbsp;status,
 org.springframework.data.domain.Pageable&nbsp;pageable)</span></div>
<div class="block">查找推荐商品</div>
</section>
</li>
<li>
<section class="detail" id="findByMerchantAndIsNewTrueAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Goods.GoodsStatus,org.springframework.data.domain.Pageable)">
<h3>findByMerchantAndIsNewTrueAndStatus</h3>
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a>&gt;</span>&nbsp;<span class="element-name">findByMerchantAndIsNewTrueAndStatus</span><wbr><span class="parameters">(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../entity/Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a>&nbsp;status,
 org.springframework.data.domain.Pageable&nbsp;pageable)</span></div>
<div class="block">查找新品</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
