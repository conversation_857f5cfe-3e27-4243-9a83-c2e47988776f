<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed Jun 18 22:33:52 CST 2025 -->
<title>LaundryOrderRepository (spring-boot2 0.0.1-SNAPSHOT API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-06-18">
<meta name="description" content="declaration: package: com.example.springboot2.repository, interface: LaundryOrderRepository">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var evenRowColor = "even-row-color";
var oddRowColor = "odd-row-color";
var tableTab = "table-tab";
var activeTableTab = "active-table-tab";
var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="class-use/LaundryOrderRepository.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html#class">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.example.springboot2.repository</a></div>
<h1 title="接口 LaundryOrderRepository" class="title">接口 LaundryOrderRepository</h1>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>所有超级接口:</dt>
<dd><code>org.springframework.data.repository.CrudRepository&lt;<a href="../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&gt;</code>, <code>org.springframework.data.jpa.repository.JpaRepository&lt;<a href="../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&gt;</code>, <code>org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;<a href="../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</code>, <code>org.springframework.data.repository.ListCrudRepository&lt;<a href="../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&gt;</code>, <code>org.springframework.data.repository.ListPagingAndSortingRepository&lt;<a href="../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&gt;</code>, <code>org.springframework.data.repository.PagingAndSortingRepository&lt;<a href="../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&gt;</code>, <code>org.springframework.data.repository.query.QueryByExampleExecutor&lt;<a href="../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</code>, <code>org.springframework.data.repository.Repository&lt;<a href="../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&gt;</code></dd>
</dl>
<hr>
<div class="type-signature"><span class="annotations">@Repository
</span><span class="modifiers">public interface </span><span class="element-name type-name-label">LaundryOrderRepository</span><span class="extends-implements">
extends org.springframework.data.jpa.repository.JpaRepository&lt;<a href="../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&gt;, org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;<a href="../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</span></div>
<div class="block">洗护订单Repository</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">抽象方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#countByMerchant(com.example.springboot2.entity.Merchant)" class="member-name-link">countByMerchant</a><wbr>(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">统计商家订单数量</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#countByMerchantAndCreatedTimeBetween(com.example.springboot2.entity.Merchant,java.time.LocalDateTime,java.time.LocalDateTime)" class="member-name-link">countByMerchantAndCreatedTimeBetween</a><wbr>(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDateTime.html" title="java.time中的类或接口" class="external-link">LocalDateTime</a>&nbsp;startTime,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDateTime.html" title="java.time中的类或接口" class="external-link">LocalDateTime</a>&nbsp;endTime)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">统计商家指定时间范围内的订单数量</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#countByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.LaundryOrder.LaundryOrderStatus)" class="member-name-link">countByMerchantAndStatus</a><wbr>(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../entity/LaundryOrder.LaundryOrderStatus.html" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a>&nbsp;status)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">统计商家指定状态订单数量</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>org.springframework.data.domain.Page&lt;<a href="../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#findByCustomer(com.example.springboot2.entity.User,org.springframework.data.domain.Pageable)" class="member-name-link">findByCustomer</a><wbr>(<a href="../entity/User.html" title="com.example.springboot2.entity中的类">User</a>&nbsp;customer,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">根据客户查找订单</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>org.springframework.data.domain.Page&lt;<a href="../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#findByMerchant(com.example.springboot2.entity.Merchant,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchant</a><wbr>(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">根据商家查找订单</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>org.springframework.data.domain.Page&lt;<a href="../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#findByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.LaundryOrder.LaundryOrderStatus,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchantAndStatus</a><wbr>(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../entity/LaundryOrder.LaundryOrderStatus.html" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a>&nbsp;status,
 org.springframework.data.domain.Pageable&nbsp;pageable)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">根据商家和状态查找订单</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="java.util中的类或接口" class="external-link">Optional</a>&lt;<a href="../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#findByOrderNo(java.lang.String)" class="member-name-link">findByOrderNo</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;orderNo)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">根据订单号查找订单</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#findTodayOrdersByMerchant(com.example.springboot2.entity.Merchant)" class="member-name-link">findTodayOrdersByMerchant</a><wbr>(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">查找商家今日订单</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/math/BigDecimal.html" title="java.math中的类或接口" class="external-link">BigDecimal</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#sumActualAmountByMerchantAndCreatedTimeBetween(com.example.springboot2.entity.Merchant,java.time.LocalDateTime,java.time.LocalDateTime)" class="member-name-link">sumActualAmountByMerchantAndCreatedTimeBetween</a><wbr>(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDateTime.html" title="java.time中的类或接口" class="external-link">LocalDateTime</a>&nbsp;startTime,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDateTime.html" title="java.time中的类或接口" class="external-link">LocalDateTime</a>&nbsp;endTime)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">统计商家指定时间范围内的销售额</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.springframework.data.repository.CrudRepository">从接口继承的方法&nbsp;org.springframework.data.repository.CrudRepository</h3>
<code>count, delete, deleteAll, deleteAll, deleteAllById, deleteById, existsById, findById, save</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.springframework.data.jpa.repository.JpaRepository">从接口继承的方法&nbsp;org.springframework.data.jpa.repository.JpaRepository</h3>
<code>deleteAllByIdInBatch, deleteAllInBatch, deleteAllInBatch, deleteInBatch, findAll, findAll, flush, getById, getOne, getReferenceById, saveAllAndFlush, saveAndFlush</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.springframework.data.jpa.repository.JpaSpecificationExecutor">从接口继承的方法&nbsp;org.springframework.data.jpa.repository.JpaSpecificationExecutor</h3>
<code>count, delete, exists, findAll, findAll, findAll, findBy, findOne</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.springframework.data.repository.ListCrudRepository">从接口继承的方法&nbsp;org.springframework.data.repository.ListCrudRepository</h3>
<code>findAll, findAllById, saveAll</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.springframework.data.repository.ListPagingAndSortingRepository">从接口继承的方法&nbsp;org.springframework.data.repository.ListPagingAndSortingRepository</h3>
<code>findAll</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.springframework.data.repository.PagingAndSortingRepository">从接口继承的方法&nbsp;org.springframework.data.repository.PagingAndSortingRepository</h3>
<code>findAll</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.springframework.data.repository.query.QueryByExampleExecutor">从接口继承的方法&nbsp;org.springframework.data.repository.query.QueryByExampleExecutor</h3>
<code>count, exists, findAll, findBy, findOne</code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="findByOrderNo(java.lang.String)">
<h3>findByOrderNo</h3>
<div class="member-signature"><span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="java.util中的类或接口" class="external-link">Optional</a>&lt;<a href="../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</span>&nbsp;<span class="element-name">findByOrderNo</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;orderNo)</span></div>
<div class="block">根据订单号查找订单</div>
</section>
</li>
<li>
<section class="detail" id="findByMerchant(com.example.springboot2.entity.Merchant,org.springframework.data.domain.Pageable)">
<h3>findByMerchant</h3>
<div class="member-signature"><span class="return-type">org.springframework.data.domain.Page&lt;<a href="../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</span>&nbsp;<span class="element-name">findByMerchant</span><wbr><span class="parameters">(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 org.springframework.data.domain.Pageable&nbsp;pageable)</span></div>
<div class="block">根据商家查找订单</div>
</section>
</li>
<li>
<section class="detail" id="findByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.LaundryOrder.LaundryOrderStatus,org.springframework.data.domain.Pageable)">
<h3>findByMerchantAndStatus</h3>
<div class="member-signature"><span class="return-type">org.springframework.data.domain.Page&lt;<a href="../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</span>&nbsp;<span class="element-name">findByMerchantAndStatus</span><wbr><span class="parameters">(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../entity/LaundryOrder.LaundryOrderStatus.html" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a>&nbsp;status,
 org.springframework.data.domain.Pageable&nbsp;pageable)</span></div>
<div class="block">根据商家和状态查找订单</div>
</section>
</li>
<li>
<section class="detail" id="findByCustomer(com.example.springboot2.entity.User,org.springframework.data.domain.Pageable)">
<h3>findByCustomer</h3>
<div class="member-signature"><span class="return-type">org.springframework.data.domain.Page&lt;<a href="../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</span>&nbsp;<span class="element-name">findByCustomer</span><wbr><span class="parameters">(<a href="../entity/User.html" title="com.example.springboot2.entity中的类">User</a>&nbsp;customer,
 org.springframework.data.domain.Pageable&nbsp;pageable)</span></div>
<div class="block">根据客户查找订单</div>
</section>
</li>
<li>
<section class="detail" id="countByMerchant(com.example.springboot2.entity.Merchant)">
<h3>countByMerchant</h3>
<div class="member-signature"><span class="return-type">long</span>&nbsp;<span class="element-name">countByMerchant</span><wbr><span class="parameters">(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</span></div>
<div class="block">统计商家订单数量</div>
</section>
</li>
<li>
<section class="detail" id="countByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.LaundryOrder.LaundryOrderStatus)">
<h3>countByMerchantAndStatus</h3>
<div class="member-signature"><span class="return-type">long</span>&nbsp;<span class="element-name">countByMerchantAndStatus</span><wbr><span class="parameters">(<a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 <a href="../entity/LaundryOrder.LaundryOrderStatus.html" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a>&nbsp;status)</span></div>
<div class="block">统计商家指定状态订单数量</div>
</section>
</li>
<li>
<section class="detail" id="countByMerchantAndCreatedTimeBetween(com.example.springboot2.entity.Merchant,java.time.LocalDateTime,java.time.LocalDateTime)">
<h3>countByMerchantAndCreatedTimeBetween</h3>
<div class="member-signature"><span class="annotations">@Query("SELECT COUNT(lo) FROM LaundryOrder lo WHERE lo.merchant = :merchant AND lo.createdTime BETWEEN :startTime AND :endTime")
</span><span class="return-type">long</span>&nbsp;<span class="element-name">countByMerchantAndCreatedTimeBetween</span><wbr><span class="parameters">(@Param("merchant")
 <a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 @Param("startTime")
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDateTime.html" title="java.time中的类或接口" class="external-link">LocalDateTime</a>&nbsp;startTime,
 @Param("endTime")
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDateTime.html" title="java.time中的类或接口" class="external-link">LocalDateTime</a>&nbsp;endTime)</span></div>
<div class="block">统计商家指定时间范围内的订单数量</div>
</section>
</li>
<li>
<section class="detail" id="sumActualAmountByMerchantAndCreatedTimeBetween(com.example.springboot2.entity.Merchant,java.time.LocalDateTime,java.time.LocalDateTime)">
<h3>sumActualAmountByMerchantAndCreatedTimeBetween</h3>
<div class="member-signature"><span class="annotations">@Query("SELECT COALESCE(SUM(lo.actualAmount), 0) FROM LaundryOrder lo WHERE lo.merchant = :merchant AND lo.status NOT IN (\'CANCELLED\', \'REFUNDED\') AND lo.createdTime BETWEEN :startTime AND :endTime")
</span><span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/math/BigDecimal.html" title="java.math中的类或接口" class="external-link">BigDecimal</a></span>&nbsp;<span class="element-name">sumActualAmountByMerchantAndCreatedTimeBetween</span><wbr><span class="parameters">(@Param("merchant")
 <a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant,
 @Param("startTime")
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDateTime.html" title="java.time中的类或接口" class="external-link">LocalDateTime</a>&nbsp;startTime,
 @Param("endTime")
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDateTime.html" title="java.time中的类或接口" class="external-link">LocalDateTime</a>&nbsp;endTime)</span></div>
<div class="block">统计商家指定时间范围内的销售额</div>
</section>
</li>
<li>
<section class="detail" id="findTodayOrdersByMerchant(com.example.springboot2.entity.Merchant)">
<h3>findTodayOrdersByMerchant</h3>
<div class="member-signature"><span class="annotations">@Query("SELECT lo FROM LaundryOrder lo WHERE lo.merchant = :merchant AND DATE(lo.createdTime) = DATE(CURRENT_TIMESTAMP)")
</span><span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="../entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a>&gt;</span>&nbsp;<span class="element-name">findTodayOrdersByMerchant</span><wbr><span class="parameters">(@Param("merchant")
 <a href="../entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a>&nbsp;merchant)</span></div>
<div class="block">查找商家今日订单</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
