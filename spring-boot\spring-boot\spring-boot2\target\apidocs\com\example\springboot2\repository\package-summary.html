<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed Jun 18 22:33:52 CST 2025 -->
<title>com.example.springboot2.repository (spring-boot2 0.0.1-SNAPSHOT API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-06-18">
<meta name="description" content="declaration: package: com.example.springboot2.repository">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../index.html">概览</a></li>
<li class="nav-bar-cell1-rev">程序包</li>
<li>类</li>
<li><a href="package-use.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html#package">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div>
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="程序包 com.example.springboot2.repository" class="title">程序包 com.example.springboot2.repository</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">com.example.springboot2.repository</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">程序包</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="../package-summary.html">com.example.springboot2</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="caption"><span>接口</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="CouponRepository.html" title="com.example.springboot2.repository中的接口">CouponRepository</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">优惠券Repository</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="GoodsCategoryRepository.html" title="com.example.springboot2.repository中的接口">GoodsCategoryRepository</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">商品分类Repository</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="GoodsRepository.html" title="com.example.springboot2.repository中的接口">GoodsRepository</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">商品Repository</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="LaundryOrderRepository.html" title="com.example.springboot2.repository中的接口">LaundryOrderRepository</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">洗护订单Repository</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="LaundryServiceRepository.html" title="com.example.springboot2.repository中的接口">LaundryServiceRepository</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">洗护服务Repository</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="MerchantRepository.html" title="com.example.springboot2.repository中的接口">MerchantRepository</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">商家Repository</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="OrderRepository.html" title="com.example.springboot2.repository中的接口">OrderRepository</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">订单Repository</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="UserRepository.html" title="com.example.springboot2.repository中的接口">UserRepository</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">用户Repository</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
