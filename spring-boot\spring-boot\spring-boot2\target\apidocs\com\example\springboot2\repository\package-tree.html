<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed Jun 18 22:33:52 CST 2025 -->
<title>com.example.springboot2.repository 类分层结构 (spring-boot2 0.0.1-SNAPSHOT API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-06-18">
<meta name="description" content="tree: package: com.example.springboot2.repository">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li>使用</li>
<li class="nav-bar-cell1-rev">树</li>
<li><a href="../../../../index-all.html">索引</a></li>
<li><a href="../../../../help-doc.html#tree">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">程序包com.example.springboot2.repository的分层结构</h1>
<span class="package-hierarchy-label">程序包分层结构:</span>
<ul class="horizontal">
<li><a href="../../../../overview-tree.html">所有程序包</a></li>
</ul>
</div>
<section class="hierarchy">
<h2 title="接口分层结构">接口分层结构</h2>
<ul>
<li class="circle">org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;
<ul>
<li class="circle">com.example.springboot2.repository.<a href="CouponRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">CouponRepository</a> (并 extends org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="GoodsCategoryRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">GoodsCategoryRepository</a> (并 extends org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="GoodsRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">GoodsRepository</a> (并 extends org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="LaundryOrderRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">LaundryOrderRepository</a> (并 extends org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="LaundryServiceRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">LaundryServiceRepository</a> (并 extends org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="MerchantRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">MerchantRepository</a> (并 extends org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="OrderRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">OrderRepository</a> (并 extends org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="UserRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">UserRepository</a> (并 extends org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt;)</li>
</ul>
</li>
<li class="circle">org.springframework.data.repository.query.QueryByExampleExecutor&lt;T&gt;
<ul>
<li class="circle">org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt; (并 extends org.springframework.data.repository.ListCrudRepository&lt;T,<wbr>ID&gt;, org.springframework.data.repository.ListPagingAndSortingRepository&lt;T,<wbr>ID&gt;)
<ul>
<li class="circle">com.example.springboot2.repository.<a href="CouponRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">CouponRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="GoodsCategoryRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">GoodsCategoryRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="GoodsRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">GoodsRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="LaundryOrderRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">LaundryOrderRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="LaundryServiceRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">LaundryServiceRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="MerchantRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">MerchantRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="OrderRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">OrderRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="UserRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">UserRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.springframework.data.repository.Repository&lt;T,<wbr>ID&gt;
<ul>
<li class="circle">org.springframework.data.repository.CrudRepository&lt;T,<wbr>ID&gt;
<ul>
<li class="circle">org.springframework.data.repository.ListCrudRepository&lt;T,<wbr>ID&gt;
<ul>
<li class="circle">org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt; (并 extends org.springframework.data.repository.ListPagingAndSortingRepository&lt;T,<wbr>ID&gt;, org.springframework.data.repository.query.QueryByExampleExecutor&lt;T&gt;)
<ul>
<li class="circle">com.example.springboot2.repository.<a href="CouponRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">CouponRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="GoodsCategoryRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">GoodsCategoryRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="GoodsRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">GoodsRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="LaundryOrderRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">LaundryOrderRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="LaundryServiceRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">LaundryServiceRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="MerchantRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">MerchantRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="OrderRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">OrderRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="UserRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">UserRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.springframework.data.repository.PagingAndSortingRepository&lt;T,<wbr>ID&gt;
<ul>
<li class="circle">org.springframework.data.repository.ListPagingAndSortingRepository&lt;T,<wbr>ID&gt;
<ul>
<li class="circle">org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt; (并 extends org.springframework.data.repository.ListCrudRepository&lt;T,<wbr>ID&gt;, org.springframework.data.repository.query.QueryByExampleExecutor&lt;T&gt;)
<ul>
<li class="circle">com.example.springboot2.repository.<a href="CouponRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">CouponRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="GoodsCategoryRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">GoodsCategoryRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="GoodsRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">GoodsRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="LaundryOrderRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">LaundryOrderRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="LaundryServiceRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">LaundryServiceRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="MerchantRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">MerchantRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="OrderRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">OrderRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="UserRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">UserRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
