<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed Jun 18 22:59:01 CST 2025 -->
<title>类 com.example.springboot2.service.GoodsService.GoodsStats的使用 (spring-boot2 0.0.1-SNAPSHOT API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-06-18">
<meta name="description" content="use: package: com.example.springboot2.service, class: GoodsService, class: GoodsStats">
<meta name="generator" content="javadoc/ClassUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-use-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../GoodsService.GoodsStats.html" title="com.example.springboot2.service中的类">类</a></li>
<li class="nav-bar-cell1-rev">使用</li>
<li><a href="../package-tree.html">树</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html#use">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="类的使用 com.example.springboot2.service.GoodsService.GoodsStats" class="title">类的使用<br>com.example.springboot2.service.GoodsService.GoodsStats</h1>
</div>
<div class="caption"><span>使用<a href="../GoodsService.GoodsStats.html" title="com.example.springboot2.service中的类">GoodsService.GoodsStats</a>的程序包</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">程序包</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="#com.example.springboot2.controller">com.example.springboot2.controller</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.example.springboot2.service">com.example.springboot2.service</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<section class="class-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.example.springboot2.controller">
<h2><a href="../../controller/package-summary.html">com.example.springboot2.controller</a>中<a href="../GoodsService.GoodsStats.html" title="com.example.springboot2.service中的类">GoodsService.GoodsStats</a>的使用</h2>
<div class="caption"><span>返回变量类型为<a href="../GoodsService.GoodsStats.html" title="com.example.springboot2.service中的类">GoodsService.GoodsStats</a>的类型的<a href="../../controller/package-summary.html">com.example.springboot2.controller</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="../../common/Result.html" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="../GoodsService.GoodsStats.html" title="com.example.springboot2.service中的类">GoodsService.GoodsStats</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsController.</span><code><a href="../../controller/GoodsController.html#getGoodsStats(org.springframework.security.core.Authentication)" class="member-name-link">getGoodsStats</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color">
<div class="block">获取商品统计数据</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.example.springboot2.service">
<h2><a href="../package-summary.html">com.example.springboot2.service</a>中<a href="../GoodsService.GoodsStats.html" title="com.example.springboot2.service中的类">GoodsService.GoodsStats</a>的使用</h2>
<div class="caption"><span>返回<a href="../GoodsService.GoodsStats.html" title="com.example.springboot2.service中的类">GoodsService.GoodsStats</a>的<a href="../package-summary.html">com.example.springboot2.service</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="../GoodsService.GoodsStats.html" title="com.example.springboot2.service中的类">GoodsService.GoodsStats</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">GoodsService.</span><code><a href="../GoodsService.html#getGoodsStats(java.lang.Long)" class="member-name-link">getGoodsStats</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a>&nbsp;userId)</code></div>
<div class="col-last even-row-color">
<div class="block">获取商品统计数据</div>
</div>
</div>
</section>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
