<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed Jun 18 22:33:52 CST 2025 -->
<title>索引 (spring-boot2 0.0.1-SNAPSHOT API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-06-18">
<meta name="description" content="index">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="#I:A">A</a>&nbsp;<a href="#I:B">B</a>&nbsp;<a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:E">E</a>&nbsp;<a href="#I:F">F</a>&nbsp;<a href="#I:G">G</a>&nbsp;<a href="#I:H">H</a>&nbsp;<a href="#I:I">I</a>&nbsp;<a href="#I:J">J</a>&nbsp;<a href="#I:L">L</a>&nbsp;<a href="#I:M">M</a>&nbsp;<a href="#I:N">N</a>&nbsp;<a href="#I:O">O</a>&nbsp;<a href="#I:P">P</a>&nbsp;<a href="#I:Q">Q</a>&nbsp;<a href="#I:R">R</a>&nbsp;<a href="#I:S">S</a>&nbsp;<a href="#I:T">T</a>&nbsp;<a href="#I:U">U</a>&nbsp;<a href="#I:V">V</a>&nbsp;<a href="#I:W">W</a>&nbsp;<br><a href="allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="allpackages-index.html">所有程序包</a><span class="vertical-separator">|</span><a href="serialized-form.html">序列化表格</a>
<h2 class="title" id="I:A">A</h2>
<dl class="index">
<dt><a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html#ACCEPTED" class="member-name-link">ACCEPTED</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/LaundryBusinessService.html#acceptLaundryOrder(java.lang.Long,java.lang.Long,java.time.LocalDateTime)" class="member-name-link">acceptLaundryOrder(Long, Long, LocalDateTime)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/LaundryBusinessService.html" title="com.example.springboot2.service中的类">LaundryBusinessService</a></dt>
<dd>
<div class="block">接单</div>
</dd>
<dt><a href="com/example/springboot2/controller/LaundryController.html#acceptLaundryOrder(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">acceptLaundryOrder(Long, Map&lt;String, String&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/LaundryController.html" title="com.example.springboot2.controller中的类">LaundryController</a></dt>
<dd>
<div class="block">接单</div>
</dd>
<dt><a href="com/example/springboot2/entity/Merchant.MerchantStatus.html#ACTIVE" class="member-name-link">ACTIVE</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Merchant.MerchantStatus.html" title="enum class in com.example.springboot2.entity">Merchant.MerchantStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/User.UserStatus.html#ACTIVE" class="member-name-link">ACTIVE</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/User.UserStatus.html" title="enum class in com.example.springboot2.entity">User.UserStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#addBankCard(java.lang.Long,java.util.Map)" class="member-name-link">addBankCard(Long, Map&lt;String, Object&gt;)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantFinanceController.html#addBankCard(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">addBankCard(Map&lt;String, Object&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></dt>
<dd>
<div class="block">添加银行卡</div>
</dd>
<dt><a href="com/example/springboot2/entity/User.UserRole.html#ADMIN" class="member-name-link">ADMIN</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/User.UserRole.html" title="enum class in com.example.springboot2.entity">User.UserRole</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/SimpleAuthController.html#adminLogin(java.util.Map)" class="member-name-link">adminLogin(Map&lt;String, Object&gt;)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/SimpleAuthController.html" title="com.example.springboot2.controller中的类">SimpleAuthController</a></dt>
<dd>
<div class="block">管理员登录</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantService.html#approveCertification(java.lang.Long,boolean,java.lang.String)" class="member-name-link">approveCertification(Long, boolean, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantService.html" title="com.example.springboot2.service中的类">MerchantService</a></dt>
<dd>
<div class="block">审核商家认证</div>
</dd>
<dt><a href="com/example/springboot2/entity/Coupon.CouponStatus.html#APPROVED" class="member-name-link">APPROVED</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Coupon.CouponStatus.html" title="enum class in com.example.springboot2.entity">Coupon.CouponStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Goods.GoodsStatus.html#APPROVED" class="member-name-link">APPROVED</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Merchant.CertificationStatus.html#APPROVED" class="member-name-link">APPROVED</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Merchant.CertificationStatus.html" title="enum class in com.example.springboot2.entity">Merchant.CertificationStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/AuthController.html" class="type-name-link" title="com.example.springboot2.controller中的类">AuthController</a> - <a href="com/example/springboot2/controller/package-summary.html">com.example.springboot2.controller</a>中的类</dt>
<dd>
<div class="block">认证控制器</div>
</dd>
<dt><a href="com/example/springboot2/controller/AuthController.html#%3Cinit%3E()" class="member-name-link">AuthController()</a> - 类的构造器 com.example.springboot2.controller.<a href="com/example/springboot2/controller/AuthController.html" title="com.example.springboot2.controller中的类">AuthController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/config/SecurityConfig.html#authenticationManager(org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration)" class="member-name-link">authenticationManager(AuthenticationConfiguration)</a> - 类中的方法 com.example.springboot2.config.<a href="com/example/springboot2/config/SecurityConfig.html" title="com.example.springboot2.config中的类">SecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/config/SecurityConfig.html#authenticationProvider()" class="member-name-link">authenticationProvider()</a> - 类中的方法 com.example.springboot2.config.<a href="com/example/springboot2/config/SecurityConfig.html" title="com.example.springboot2.config中的类">SecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/AuthService.html" class="type-name-link" title="com.example.springboot2.service中的类">AuthService</a> - <a href="com/example/springboot2/service/package-summary.html">com.example.springboot2.service</a>中的类</dt>
<dd>
<div class="block">认证服务类</div>
</dd>
<dt><a href="com/example/springboot2/service/AuthService.html#%3Cinit%3E()" class="member-name-link">AuthService()</a> - 类的构造器 com.example.springboot2.service.<a href="com/example/springboot2/service/AuthService.html" title="com.example.springboot2.service中的类">AuthService</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:B">B</h2>
<dl class="index">
<dt><a href="com/example/springboot2/common/Result.html#badRequest()" class="member-name-link">badRequest()</a> - 类中的静态方法 com.example.springboot2.common.<a href="com/example/springboot2/common/Result.html" title="com.example.springboot2.common中的类">Result</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/common/Result.html#badRequest(java.lang.String)" class="member-name-link">badRequest(String)</a> - 类中的静态方法 com.example.springboot2.common.<a href="com/example/springboot2/common/Result.html" title="com.example.springboot2.common中的类">Result</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/LaundryService.ServiceType.html#BAG_CLEANING" class="member-name-link">BAG_CLEANING</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryService.ServiceType.html" title="enum class in com.example.springboot2.entity">LaundryService.ServiceType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/BaseEntity.html" class="type-name-link" title="com.example.springboot2.entity中的类">BaseEntity</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的类</dt>
<dd>
<div class="block">基础实体类</div>
</dd>
<dt><a href="com/example/springboot2/entity/BaseEntity.html#%3Cinit%3E()" class="member-name-link">BaseEntity()</a> - 类的构造器 com.example.springboot2.entity.<a href="com/example/springboot2/entity/BaseEntity.html" title="com.example.springboot2.entity中的类">BaseEntity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/CouponService.html#batchDeleteCoupons(java.lang.Long,java.util.List)" class="member-name-link">batchDeleteCoupons(Long, List&lt;Long&gt;)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/CouponService.html" title="com.example.springboot2.service中的类">CouponService</a></dt>
<dd>
<div class="block">批量删除优惠券</div>
</dd>
<dt><a href="com/example/springboot2/controller/CouponController.html#batchDeleteCoupons(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">batchDeleteCoupons(Map&lt;String, List&lt;Long&gt;&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/CouponController.html" title="com.example.springboot2.controller中的类">CouponController</a></dt>
<dd>
<div class="block">批量删除优惠券</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsService.html#batchDeleteGoods(java.lang.Long,java.util.List)" class="member-name-link">batchDeleteGoods(Long, List&lt;Long&gt;)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsService.html" title="com.example.springboot2.service中的类">GoodsService</a></dt>
<dd>
<div class="block">批量删除商品</div>
</dd>
<dt><a href="com/example/springboot2/controller/GoodsController.html#batchDeleteGoods(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">batchDeleteGoods(Map&lt;String, List&lt;Long&gt;&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/GoodsController.html" title="com.example.springboot2.controller中的类">GoodsController</a></dt>
<dd>
<div class="block">批量删除商品</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantServiceManagementService.html#batchOperateServices(java.lang.Long,java.util.Map)" class="member-name-link">batchOperateServices(Long, Map&lt;String, Object&gt;)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantServiceManagementService.html" title="com.example.springboot2.service中的类">MerchantServiceManagementService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantServiceController.html#batchOperateServices(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">batchOperateServices(Map&lt;String, Object&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantServiceController.html" title="com.example.springboot2.controller中的类">MerchantServiceController</a></dt>
<dd>
<div class="block">批量操作服务</div>
</dd>
<dt><a href="com/example/springboot2/service/CouponService.html#batchUpdateCouponStatus(java.lang.Long,java.util.List,com.example.springboot2.entity.Coupon.CouponStatus)" class="member-name-link">batchUpdateCouponStatus(Long, List&lt;Long&gt;, Coupon.CouponStatus)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/CouponService.html" title="com.example.springboot2.service中的类">CouponService</a></dt>
<dd>
<div class="block">批量更新优惠券状态</div>
</dd>
<dt><a href="com/example/springboot2/controller/CouponController.html#batchUpdateCouponStatus(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">batchUpdateCouponStatus(Map&lt;String, Object&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/CouponController.html" title="com.example.springboot2.controller中的类">CouponController</a></dt>
<dd>
<div class="block">批量更新优惠券状态</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsService.html#batchUpdateGoodsStatus(java.lang.Long,java.util.List,com.example.springboot2.entity.Goods.GoodsStatus)" class="member-name-link">batchUpdateGoodsStatus(Long, List&lt;Long&gt;, Goods.GoodsStatus)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsService.html" title="com.example.springboot2.service中的类">GoodsService</a></dt>
<dd>
<div class="block">批量更新商品状态</div>
</dd>
<dt><a href="com/example/springboot2/controller/GoodsController.html#batchUpdateGoodsStatus(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">batchUpdateGoodsStatus(Map&lt;String, Object&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/GoodsController.html" title="com.example.springboot2.controller中的类">GoodsController</a></dt>
<dd>
<div class="block">批量更新商品状态</div>
</dd>
<dt><a href="com/example/springboot2/service/LaundryBusinessService.html#batchUpdateServiceStatus(java.lang.Long,java.util.List,java.lang.Boolean)" class="member-name-link">batchUpdateServiceStatus(Long, List&lt;Long&gt;, Boolean)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/LaundryBusinessService.html" title="com.example.springboot2.service中的类">LaundryBusinessService</a></dt>
<dd>
<div class="block">批量更新服务状态</div>
</dd>
<dt><a href="com/example/springboot2/controller/LaundryController.html#batchUpdateServiceStatus(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">batchUpdateServiceStatus(Map&lt;String, Object&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/LaundryController.html" title="com.example.springboot2.controller中的类">LaundryController</a></dt>
<dd>
<div class="block">批量更新服务状态</div>
</dd>
<dt><a href="com/example/springboot2/exception/BusinessException.html" class="type-name-link" title="com.example.springboot2.exception中的类">BusinessException</a> - <a href="com/example/springboot2/exception/package-summary.html">com.example.springboot2.exception</a>中的异常错误</dt>
<dd>
<div class="block">业务异常类</div>
</dd>
<dt><a href="com/example/springboot2/exception/BusinessException.html#%3Cinit%3E(java.lang.Integer,java.lang.String)" class="member-name-link">BusinessException(Integer, String)</a> - 异常错误的构造器 com.example.springboot2.exception.<a href="com/example/springboot2/exception/BusinessException.html" title="com.example.springboot2.exception中的类">BusinessException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/exception/BusinessException.html#%3Cinit%3E(java.lang.Integer,java.lang.String,java.lang.Throwable)" class="member-name-link">BusinessException(Integer, String, Throwable)</a> - 异常错误的构造器 com.example.springboot2.exception.<a href="com/example/springboot2/exception/BusinessException.html" title="com.example.springboot2.exception中的类">BusinessException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/exception/BusinessException.html#%3Cinit%3E(java.lang.String)" class="member-name-link">BusinessException(String)</a> - 异常错误的构造器 com.example.springboot2.exception.<a href="com/example/springboot2/exception/BusinessException.html" title="com.example.springboot2.exception中的类">BusinessException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/exception/BusinessException.html#%3Cinit%3E(java.lang.String,java.lang.Throwable)" class="member-name-link">BusinessException(String, Throwable)</a> - 异常错误的构造器 com.example.springboot2.exception.<a href="com/example/springboot2/exception/BusinessException.html" title="com.example.springboot2.exception中的类">BusinessException</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:C">C</h2>
<dl class="index">
<dt><a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html#CANCELLED" class="member-name-link">CANCELLED</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Order.OrderStatus.html#CANCELLED" class="member-name-link">CANCELLED</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Order.OrderStatus.html" title="enum class in com.example.springboot2.entity">Order.OrderStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/OrderService.html#cancelOrder(java.lang.Long,java.lang.Long,java.lang.String)" class="member-name-link">cancelOrder(Long, Long, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/OrderService.html" title="com.example.springboot2.service中的类">OrderService</a></dt>
<dd>
<div class="block">取消订单</div>
</dd>
<dt><a href="com/example/springboot2/controller/OrderController.html#cancelOrder(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">cancelOrder(Long, Map&lt;String, String&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/OrderController.html" title="com.example.springboot2.controller中的类">OrderController</a></dt>
<dd>
<div class="block">取消订单</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#cancelWithdrawal(java.lang.Long,java.lang.Long)" class="member-name-link">cancelWithdrawal(Long, Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantFinanceController.html#cancelWithdrawal(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">cancelWithdrawal(Long, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></dt>
<dd>
<div class="block">取消提现申请</div>
</dd>
<dt><a href="com/example/springboot2/entity/LaundryService.ServiceType.html#CARPET_CLEANING" class="member-name-link">CARPET_CLEANING</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryService.ServiceType.html" title="enum class in com.example.springboot2.entity">LaundryService.ServiceType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/AuthService.html#changePassword(java.lang.Long,java.lang.String,java.lang.String)" class="member-name-link">changePassword(Long, String, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/AuthService.html" title="com.example.springboot2.service中的类">AuthService</a></dt>
<dd>
<div class="block">修改密码</div>
</dd>
<dt><a href="com/example/springboot2/service/UserService.html#changePassword(java.lang.Long,java.lang.String,java.lang.String)" class="member-name-link">changePassword(Long, String, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/UserService.html" title="com.example.springboot2.service中的类">UserService</a></dt>
<dd>
<div class="block">修改密码</div>
</dd>
<dt><a href="com/example/springboot2/controller/AuthController.html#changePassword(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">changePassword(Map&lt;String, String&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/AuthController.html" title="com.example.springboot2.controller中的类">AuthController</a></dt>
<dd>
<div class="block">修改密码</div>
</dd>
<dt><a href="com/example/springboot2/package-summary.html">com.example.springboot2</a> - 程序包 com.example.springboot2</dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/common/package-summary.html">com.example.springboot2.common</a> - 程序包 com.example.springboot2.common</dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/config/package-summary.html">com.example.springboot2.config</a> - 程序包 com.example.springboot2.config</dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/package-summary.html">com.example.springboot2.controller</a> - 程序包 com.example.springboot2.controller</dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/dto/package-summary.html">com.example.springboot2.dto</a> - 程序包 com.example.springboot2.dto</dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a> - 程序包 com.example.springboot2.entity</dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/exception/package-summary.html">com.example.springboot2.exception</a> - 程序包 com.example.springboot2.exception</dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/repository/package-summary.html">com.example.springboot2.repository</a> - 程序包 com.example.springboot2.repository</dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/security/package-summary.html">com.example.springboot2.security</a> - 程序包 com.example.springboot2.security</dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/package-summary.html">com.example.springboot2.service</a> - 程序包 com.example.springboot2.service</dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/util/package-summary.html">com.example.springboot2.util</a> - 程序包 com.example.springboot2.util</dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/security/JwtAuthenticationEntryPoint.html#commence(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse,org.springframework.security.core.AuthenticationException)" class="member-name-link">commence(HttpServletRequest, HttpServletResponse, AuthenticationException)</a> - 类中的方法 com.example.springboot2.security.<a href="com/example/springboot2/security/JwtAuthenticationEntryPoint.html" title="com.example.springboot2.security中的类">JwtAuthenticationEntryPoint</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/OrderController.html#confirmOrder(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">confirmOrder(Long, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/OrderController.html" title="com.example.springboot2.controller中的类">OrderController</a></dt>
<dd>
<div class="block">确认收货</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantServiceManagementService.html#copyService(java.lang.Long,java.lang.Long)" class="member-name-link">copyService(Long, Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantServiceManagementService.html" title="com.example.springboot2.service中的类">MerchantServiceManagementService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantServiceController.html#copyService(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">copyService(Long, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantServiceController.html" title="com.example.springboot2.controller中的类">MerchantServiceController</a></dt>
<dd>
<div class="block">复制服务</div>
</dd>
<dt><a href="com/example/springboot2/config/CorsConfig.html" class="type-name-link" title="com.example.springboot2.config中的类">CorsConfig</a> - <a href="com/example/springboot2/config/package-summary.html">com.example.springboot2.config</a>中的类</dt>
<dd>
<div class="block">跨域配置</div>
</dd>
<dt><a href="com/example/springboot2/config/CorsConfig.html#%3Cinit%3E()" class="member-name-link">CorsConfig()</a> - 类的构造器 com.example.springboot2.config.<a href="com/example/springboot2/config/CorsConfig.html" title="com.example.springboot2.config中的类">CorsConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/config/CorsConfig.html#corsConfigurationSource()" class="member-name-link">corsConfigurationSource()</a> - 类中的方法 com.example.springboot2.config.<a href="com/example/springboot2/config/CorsConfig.html" title="com.example.springboot2.config中的类">CorsConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/repository/CouponRepository.html#countByMerchant(com.example.springboot2.entity.Merchant)" class="member-name-link">countByMerchant(Merchant)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/CouponRepository.html" title="com.example.springboot2.repository中的接口">CouponRepository</a></dt>
<dd>
<div class="block">统计商家优惠券数量</div>
</dd>
<dt><a href="com/example/springboot2/repository/GoodsRepository.html#countByMerchant(com.example.springboot2.entity.Merchant)" class="member-name-link">countByMerchant(Merchant)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/GoodsRepository.html" title="com.example.springboot2.repository中的接口">GoodsRepository</a></dt>
<dd>
<div class="block">根据商家查找商品数量</div>
</dd>
<dt><a href="com/example/springboot2/repository/LaundryOrderRepository.html#countByMerchant(com.example.springboot2.entity.Merchant)" class="member-name-link">countByMerchant(Merchant)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryOrderRepository.html" title="com.example.springboot2.repository中的接口">LaundryOrderRepository</a></dt>
<dd>
<div class="block">统计商家订单数量</div>
</dd>
<dt><a href="com/example/springboot2/repository/LaundryServiceRepository.html#countByMerchant(com.example.springboot2.entity.Merchant)" class="member-name-link">countByMerchant(Merchant)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryServiceRepository.html" title="com.example.springboot2.repository中的接口">LaundryServiceRepository</a></dt>
<dd>
<div class="block">统计商家服务数量</div>
</dd>
<dt><a href="com/example/springboot2/repository/OrderRepository.html#countByMerchant(com.example.springboot2.entity.Merchant)" class="member-name-link">countByMerchant(Merchant)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/OrderRepository.html" title="com.example.springboot2.repository中的接口">OrderRepository</a></dt>
<dd>
<div class="block">统计商家订单数量</div>
</dd>
<dt><a href="com/example/springboot2/repository/LaundryOrderRepository.html#countByMerchantAndCreatedTimeBetween(com.example.springboot2.entity.Merchant,java.time.LocalDateTime,java.time.LocalDateTime)" class="member-name-link">countByMerchantAndCreatedTimeBetween(Merchant, LocalDateTime, LocalDateTime)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryOrderRepository.html" title="com.example.springboot2.repository中的接口">LaundryOrderRepository</a></dt>
<dd>
<div class="block">统计商家指定时间范围内的订单数量</div>
</dd>
<dt><a href="com/example/springboot2/repository/OrderRepository.html#countByMerchantAndCreatedTimeBetween(com.example.springboot2.entity.Merchant,java.time.LocalDateTime,java.time.LocalDateTime)" class="member-name-link">countByMerchantAndCreatedTimeBetween(Merchant, LocalDateTime, LocalDateTime)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/OrderRepository.html" title="com.example.springboot2.repository中的接口">OrderRepository</a></dt>
<dd>
<div class="block">统计商家指定时间范围内的订单数量</div>
</dd>
<dt><a href="com/example/springboot2/repository/LaundryServiceRepository.html#countByMerchantAndIsEnabledTrue(com.example.springboot2.entity.Merchant)" class="member-name-link">countByMerchantAndIsEnabledTrue(Merchant)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryServiceRepository.html" title="com.example.springboot2.repository中的接口">LaundryServiceRepository</a></dt>
<dd>
<div class="block">统计商家启用的服务数量</div>
</dd>
<dt><a href="com/example/springboot2/repository/CouponRepository.html#countByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Coupon.CouponStatus)" class="member-name-link">countByMerchantAndStatus(Merchant, Coupon.CouponStatus)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/CouponRepository.html" title="com.example.springboot2.repository中的接口">CouponRepository</a></dt>
<dd>
<div class="block">统计商家指定状态优惠券数量</div>
</dd>
<dt><a href="com/example/springboot2/repository/GoodsRepository.html#countByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Goods.GoodsStatus)" class="member-name-link">countByMerchantAndStatus(Merchant, Goods.GoodsStatus)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/GoodsRepository.html" title="com.example.springboot2.repository中的接口">GoodsRepository</a></dt>
<dd>
<div class="block">根据商家和状态查找商品数量</div>
</dd>
<dt><a href="com/example/springboot2/repository/LaundryOrderRepository.html#countByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.LaundryOrder.LaundryOrderStatus)" class="member-name-link">countByMerchantAndStatus(Merchant, LaundryOrder.LaundryOrderStatus)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryOrderRepository.html" title="com.example.springboot2.repository中的接口">LaundryOrderRepository</a></dt>
<dd>
<div class="block">统计商家指定状态订单数量</div>
</dd>
<dt><a href="com/example/springboot2/repository/OrderRepository.html#countByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Order.OrderStatus)" class="member-name-link">countByMerchantAndStatus(Merchant, Order.OrderStatus)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/OrderRepository.html" title="com.example.springboot2.repository中的接口">OrderRepository</a></dt>
<dd>
<div class="block">统计商家指定状态订单数量</div>
</dd>
<dt><a href="com/example/springboot2/entity/Coupon.html" class="type-name-link" title="com.example.springboot2.entity中的类">Coupon</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的类</dt>
<dd>
<div class="block">优惠券实体类</div>
</dd>
<dt><a href="com/example/springboot2/entity/Coupon.html#%3Cinit%3E()" class="member-name-link">Coupon()</a> - 类的构造器 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Coupon.html" title="com.example.springboot2.entity中的类">Coupon</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Coupon.CouponStatus.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Coupon.CouponStatus</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的Enum Class</dt>
<dd>
<div class="block">优惠券状态枚举</div>
</dd>
<dt><a href="com/example/springboot2/entity/Coupon.CouponType.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Coupon.CouponType</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的Enum Class</dt>
<dd>
<div class="block">优惠券类型枚举</div>
</dd>
<dt><a href="com/example/springboot2/controller/CouponController.html" class="type-name-link" title="com.example.springboot2.controller中的类">CouponController</a> - <a href="com/example/springboot2/controller/package-summary.html">com.example.springboot2.controller</a>中的类</dt>
<dd>
<div class="block">优惠券控制器</div>
</dd>
<dt><a href="com/example/springboot2/controller/CouponController.html#%3Cinit%3E()" class="member-name-link">CouponController()</a> - 类的构造器 com.example.springboot2.controller.<a href="com/example/springboot2/controller/CouponController.html" title="com.example.springboot2.controller中的类">CouponController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/repository/CouponRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">CouponRepository</a> - <a href="com/example/springboot2/repository/package-summary.html">com.example.springboot2.repository</a>中的接口</dt>
<dd>
<div class="block">优惠券Repository</div>
</dd>
<dt><a href="com/example/springboot2/service/CouponService.html" class="type-name-link" title="com.example.springboot2.service中的类">CouponService</a> - <a href="com/example/springboot2/service/package-summary.html">com.example.springboot2.service</a>中的类</dt>
<dd>
<div class="block">优惠券服务类</div>
</dd>
<dt><a href="com/example/springboot2/service/CouponService.html#%3Cinit%3E()" class="member-name-link">CouponService()</a> - 类的构造器 com.example.springboot2.service.<a href="com/example/springboot2/service/CouponService.html" title="com.example.springboot2.service中的类">CouponService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/CouponService.CouponStats.html" class="type-name-link" title="com.example.springboot2.service中的类">CouponService.CouponStats</a> - <a href="com/example/springboot2/service/package-summary.html">com.example.springboot2.service</a>中的类</dt>
<dd>
<div class="block">优惠券统计数据DTO</div>
</dd>
<dt><a href="com/example/springboot2/service/CouponService.CouponStats.html#%3Cinit%3E()" class="member-name-link">CouponStats()</a> - 类的构造器 com.example.springboot2.service.<a href="com/example/springboot2/service/CouponService.CouponStats.html" title="com.example.springboot2.service中的类">CouponService.CouponStats</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/CouponController.html#createCoupon(com.example.springboot2.entity.Coupon,org.springframework.security.core.Authentication)" class="member-name-link">createCoupon(Coupon, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/CouponController.html" title="com.example.springboot2.controller中的类">CouponController</a></dt>
<dd>
<div class="block">创建优惠券</div>
</dd>
<dt><a href="com/example/springboot2/service/CouponService.html#createCoupon(java.lang.Long,com.example.springboot2.entity.Coupon)" class="member-name-link">createCoupon(Long, Coupon)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/CouponService.html" title="com.example.springboot2.service中的类">CouponService</a></dt>
<dd>
<div class="block">创建优惠券</div>
</dd>
<dt><a href="com/example/springboot2/controller/GoodsController.html#createGoods(com.example.springboot2.entity.Goods,org.springframework.security.core.Authentication)" class="member-name-link">createGoods(Goods, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/GoodsController.html" title="com.example.springboot2.controller中的类">GoodsController</a></dt>
<dd>
<div class="block">创建商品</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsService.html#createGoods(java.lang.Long,com.example.springboot2.entity.Goods)" class="member-name-link">createGoods(Long, Goods)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsService.html" title="com.example.springboot2.service中的类">GoodsService</a></dt>
<dd>
<div class="block">创建商品</div>
</dd>
<dt><a href="com/example/springboot2/controller/GoodsCategoryController.html#createGoodsCategory(com.example.springboot2.entity.GoodsCategory,org.springframework.security.core.Authentication)" class="member-name-link">createGoodsCategory(GoodsCategory, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/GoodsCategoryController.html" title="com.example.springboot2.controller中的类">GoodsCategoryController</a></dt>
<dd>
<div class="block">创建商品分类</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsCategoryService.html#createGoodsCategory(java.lang.Long,com.example.springboot2.entity.GoodsCategory)" class="member-name-link">createGoodsCategory(Long, GoodsCategory)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsCategoryService.html" title="com.example.springboot2.service中的类">GoodsCategoryService</a></dt>
<dd>
<div class="block">创建商品分类</div>
</dd>
<dt><a href="com/example/springboot2/controller/LaundryController.html#createLaundryService(com.example.springboot2.entity.LaundryService,org.springframework.security.core.Authentication)" class="member-name-link">createLaundryService(LaundryService, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/LaundryController.html" title="com.example.springboot2.controller中的类">LaundryController</a></dt>
<dd>
<div class="block">创建洗护服务</div>
</dd>
<dt><a href="com/example/springboot2/service/LaundryBusinessService.html#createLaundryService(java.lang.Long,com.example.springboot2.entity.LaundryService)" class="member-name-link">createLaundryService(Long, LaundryService)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/LaundryBusinessService.html" title="com.example.springboot2.service中的类">LaundryBusinessService</a></dt>
<dd>
<div class="block">创建洗护服务</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantService.html#createMerchant(java.lang.Long,com.example.springboot2.entity.Merchant)" class="member-name-link">createMerchant(Long, Merchant)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantService.html" title="com.example.springboot2.service中的类">MerchantService</a></dt>
<dd>
<div class="block">创建商家</div>
</dd>
<dt><a href="com/example/springboot2/service/UserService.html#createUser(com.example.springboot2.entity.User)" class="member-name-link">createUser(User)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/UserService.html" title="com.example.springboot2.service中的类">UserService</a></dt>
<dd>
<div class="block">创建用户</div>
</dd>
<dt><a href="com/example/springboot2/entity/LaundryService.ServiceType.html#CURTAIN_CLEANING" class="member-name-link">CURTAIN_CLEANING</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryService.ServiceType.html" title="enum class in com.example.springboot2.entity">LaundryService.ServiceType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/User.UserRole.html#CUSTOMER" class="member-name-link">CUSTOMER</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/User.UserRole.html" title="enum class in com.example.springboot2.entity">User.UserRole</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:D">D</h2>
<dl class="index">
<dt><a href="com/example/springboot2/controller/DashboardController.html" class="type-name-link" title="com.example.springboot2.controller中的类">DashboardController</a> - <a href="com/example/springboot2/controller/package-summary.html">com.example.springboot2.controller</a>中的类</dt>
<dd>
<div class="block">仪表板控制器</div>
</dd>
<dt><a href="com/example/springboot2/controller/DashboardController.html#%3Cinit%3E()" class="member-name-link">DashboardController()</a> - 类的构造器 com.example.springboot2.controller.<a href="com/example/springboot2/controller/DashboardController.html" title="com.example.springboot2.controller中的类">DashboardController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/DashboardService.html" class="type-name-link" title="com.example.springboot2.service中的类">DashboardService</a> - <a href="com/example/springboot2/service/package-summary.html">com.example.springboot2.service</a>中的类</dt>
<dd>
<div class="block">仪表板服务类</div>
</dd>
<dt><a href="com/example/springboot2/service/DashboardService.html#%3Cinit%3E()" class="member-name-link">DashboardService()</a> - 类的构造器 com.example.springboot2.service.<a href="com/example/springboot2/service/DashboardService.html" title="com.example.springboot2.service中的类">DashboardService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/config/DataInitializer.html" class="type-name-link" title="com.example.springboot2.config中的类">DataInitializer</a> - <a href="com/example/springboot2/config/package-summary.html">com.example.springboot2.config</a>中的类</dt>
<dd>
<div class="block">数据初始化器</div>
</dd>
<dt><a href="com/example/springboot2/config/DataInitializer.html#%3Cinit%3E()" class="member-name-link">DataInitializer()</a> - 类的构造器 com.example.springboot2.config.<a href="com/example/springboot2/config/DataInitializer.html" title="com.example.springboot2.config中的类">DataInitializer</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#deleteBankCard(java.lang.Long,java.lang.Long)" class="member-name-link">deleteBankCard(Long, Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantFinanceController.html#deleteBankCard(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">deleteBankCard(Long, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></dt>
<dd>
<div class="block">删除银行卡</div>
</dd>
<dt><a href="com/example/springboot2/service/CouponService.html#deleteCoupon(java.lang.Long,java.lang.Long)" class="member-name-link">deleteCoupon(Long, Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/CouponService.html" title="com.example.springboot2.service中的类">CouponService</a></dt>
<dd>
<div class="block">删除优惠券</div>
</dd>
<dt><a href="com/example/springboot2/controller/CouponController.html#deleteCoupon(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">deleteCoupon(Long, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/CouponController.html" title="com.example.springboot2.controller中的类">CouponController</a></dt>
<dd>
<div class="block">删除优惠券</div>
</dd>
<dt><a href="com/example/springboot2/entity/Goods.GoodsStatus.html#DELETED" class="member-name-link">DELETED</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/User.UserStatus.html#DELETED" class="member-name-link">DELETED</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/User.UserStatus.html" title="enum class in com.example.springboot2.entity">User.UserStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/GoodsService.html#deleteGoods(java.lang.Long,java.lang.Long)" class="member-name-link">deleteGoods(Long, Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsService.html" title="com.example.springboot2.service中的类">GoodsService</a></dt>
<dd>
<div class="block">删除商品</div>
</dd>
<dt><a href="com/example/springboot2/controller/GoodsController.html#deleteGoods(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">deleteGoods(Long, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/GoodsController.html" title="com.example.springboot2.controller中的类">GoodsController</a></dt>
<dd>
<div class="block">删除商品</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsCategoryService.html#deleteGoodsCategory(java.lang.Long,java.lang.Long)" class="member-name-link">deleteGoodsCategory(Long, Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsCategoryService.html" title="com.example.springboot2.service中的类">GoodsCategoryService</a></dt>
<dd>
<div class="block">删除商品分类</div>
</dd>
<dt><a href="com/example/springboot2/controller/GoodsCategoryController.html#deleteGoodsCategory(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">deleteGoodsCategory(Long, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/GoodsCategoryController.html" title="com.example.springboot2.controller中的类">GoodsCategoryController</a></dt>
<dd>
<div class="block">删除商品分类</div>
</dd>
<dt><a href="com/example/springboot2/service/LaundryBusinessService.html#deleteLaundryService(java.lang.Long,java.lang.Long)" class="member-name-link">deleteLaundryService(Long, Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/LaundryBusinessService.html" title="com.example.springboot2.service中的类">LaundryBusinessService</a></dt>
<dd>
<div class="block">删除洗护服务</div>
</dd>
<dt><a href="com/example/springboot2/controller/LaundryController.html#deleteLaundryService(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">deleteLaundryService(Long, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/LaundryController.html" title="com.example.springboot2.controller中的类">LaundryController</a></dt>
<dd>
<div class="block">删除洗护服务</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantServiceManagementService.html#deleteService(java.lang.Long,java.lang.Long)" class="member-name-link">deleteService(Long, Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantServiceManagementService.html" title="com.example.springboot2.service中的类">MerchantServiceManagementService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantServiceController.html#deleteService(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">deleteService(Long, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantServiceController.html" title="com.example.springboot2.controller中的类">MerchantServiceController</a></dt>
<dd>
<div class="block">删除服务</div>
</dd>
<dt><a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html#DELIVERED" class="member-name-link">DELIVERED</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Order.OrderStatus.html#DELIVERED" class="member-name-link">DELIVERED</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Order.OrderStatus.html" title="enum class in com.example.springboot2.entity">Order.OrderStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Coupon.CouponType.html#DIRECT_REDUCTION" class="member-name-link">DIRECT_REDUCTION</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Coupon.CouponType.html" title="enum class in com.example.springboot2.entity">Coupon.CouponType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Coupon.CouponType.html#DISCOUNT" class="member-name-link">DISCOUNT</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Coupon.CouponType.html" title="enum class in com.example.springboot2.entity">Coupon.CouponType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/security/JwtAuthenticationFilter.html#doFilterInternal(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse,jakarta.servlet.FilterChain)" class="member-name-link">doFilterInternal(HttpServletRequest, HttpServletResponse, FilterChain)</a> - 类中的方法 com.example.springboot2.security.<a href="com/example/springboot2/security/JwtAuthenticationFilter.html" title="com.example.springboot2.security中的类">JwtAuthenticationFilter</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/FileController.html#downloadFile(java.lang.String)" class="member-name-link">downloadFile(String)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/FileController.html" title="com.example.springboot2.controller中的类">FileController</a></dt>
<dd>
<div class="block">文件下载</div>
</dd>
<dt><a href="com/example/springboot2/entity/Coupon.CouponStatus.html#DRAFT" class="member-name-link">DRAFT</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Coupon.CouponStatus.html" title="enum class in com.example.springboot2.entity">Coupon.CouponStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Goods.GoodsStatus.html#DRAFT" class="member-name-link">DRAFT</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/LaundryService.ServiceType.html#DRY_CLEANING" class="member-name-link">DRY_CLEANING</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryService.ServiceType.html" title="enum class in com.example.springboot2.entity">LaundryService.ServiceType</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:E">E</h2>
<dl class="index">
<dt><a href="com/example/springboot2/common/Result.html#error()" class="member-name-link">error()</a> - 类中的静态方法 com.example.springboot2.common.<a href="com/example/springboot2/common/Result.html" title="com.example.springboot2.common中的类">Result</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/common/Result.html#error(java.lang.Integer,java.lang.String)" class="member-name-link">error(Integer, String)</a> - 类中的静态方法 com.example.springboot2.common.<a href="com/example/springboot2/common/Result.html" title="com.example.springboot2.common中的类">Result</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/common/Result.html#error(java.lang.String)" class="member-name-link">error(String)</a> - 类中的静态方法 com.example.springboot2.common.<a href="com/example/springboot2/common/Result.html" title="com.example.springboot2.common中的类">Result</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/repository/UserRepository.html#existsByEmail(java.lang.String)" class="member-name-link">existsByEmail(String)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/UserRepository.html" title="com.example.springboot2.repository中的接口">UserRepository</a></dt>
<dd>
<div class="block">检查邮箱是否存在</div>
</dd>
<dt><a href="com/example/springboot2/service/UserService.html#existsByEmail(java.lang.String)" class="member-name-link">existsByEmail(String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/UserService.html" title="com.example.springboot2.service中的类">UserService</a></dt>
<dd>
<div class="block">检查邮箱是否存在</div>
</dd>
<dt><a href="com/example/springboot2/repository/UserRepository.html#existsByPhone(java.lang.String)" class="member-name-link">existsByPhone(String)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/UserRepository.html" title="com.example.springboot2.repository中的接口">UserRepository</a></dt>
<dd>
<div class="block">检查手机号是否存在</div>
</dd>
<dt><a href="com/example/springboot2/service/UserService.html#existsByPhone(java.lang.String)" class="member-name-link">existsByPhone(String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/UserService.html" title="com.example.springboot2.service中的类">UserService</a></dt>
<dd>
<div class="block">检查手机号是否存在</div>
</dd>
<dt><a href="com/example/springboot2/repository/MerchantRepository.html#existsByShopName(java.lang.String)" class="member-name-link">existsByShopName(String)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/MerchantRepository.html" title="com.example.springboot2.repository中的接口">MerchantRepository</a></dt>
<dd>
<div class="block">检查店铺名称是否存在</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantService.html#existsByShopName(java.lang.String)" class="member-name-link">existsByShopName(String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantService.html" title="com.example.springboot2.service中的类">MerchantService</a></dt>
<dd>
<div class="block">检查店铺名称是否存在</div>
</dd>
<dt><a href="com/example/springboot2/repository/UserRepository.html#existsByUsername(java.lang.String)" class="member-name-link">existsByUsername(String)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/UserRepository.html" title="com.example.springboot2.repository中的接口">UserRepository</a></dt>
<dd>
<div class="block">检查用户名是否存在</div>
</dd>
<dt><a href="com/example/springboot2/service/UserService.html#existsByUsername(java.lang.String)" class="member-name-link">existsByUsername(String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/UserService.html" title="com.example.springboot2.service中的类">UserService</a></dt>
<dd>
<div class="block">检查用户名是否存在</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#exportFinanceData(java.lang.Long,java.util.Map)" class="member-name-link">exportFinanceData(Long, Map&lt;String, Object&gt;)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantFinanceController.html#exportFinanceData(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">exportFinanceData(Map&lt;String, Object&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></dt>
<dd>
<div class="block">导出财务数据</div>
</dd>
</dl>
<h2 class="title" id="I:F">F</h2>
<dl class="index">
<dt><a href="com/example/springboot2/controller/FileController.html" class="type-name-link" title="com.example.springboot2.controller中的类">FileController</a> - <a href="com/example/springboot2/controller/package-summary.html">com.example.springboot2.controller</a>中的类</dt>
<dd>
<div class="block">文件上传控制器</div>
</dd>
<dt><a href="com/example/springboot2/controller/FileController.html#%3Cinit%3E()" class="member-name-link">FileController()</a> - 类的构造器 com.example.springboot2.controller.<a href="com/example/springboot2/controller/FileController.html" title="com.example.springboot2.controller中的类">FileController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/config/SecurityConfig.html#filterChain(org.springframework.security.config.annotation.web.builders.HttpSecurity)" class="member-name-link">filterChain(HttpSecurity)</a> - 类中的方法 com.example.springboot2.config.<a href="com/example/springboot2/config/SecurityConfig.html" title="com.example.springboot2.config中的类">SecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/repository/LaundryOrderRepository.html#findByCustomer(com.example.springboot2.entity.User,org.springframework.data.domain.Pageable)" class="member-name-link">findByCustomer(User, Pageable)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryOrderRepository.html" title="com.example.springboot2.repository中的接口">LaundryOrderRepository</a></dt>
<dd>
<div class="block">根据客户查找订单</div>
</dd>
<dt><a href="com/example/springboot2/repository/OrderRepository.html#findByCustomer(com.example.springboot2.entity.User,org.springframework.data.domain.Pageable)" class="member-name-link">findByCustomer(User, Pageable)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/OrderRepository.html" title="com.example.springboot2.repository中的接口">OrderRepository</a></dt>
<dd>
<div class="block">根据客户查找订单</div>
</dd>
<dt><a href="com/example/springboot2/repository/UserRepository.html#findByEmail(java.lang.String)" class="member-name-link">findByEmail(String)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/UserRepository.html" title="com.example.springboot2.repository中的接口">UserRepository</a></dt>
<dd>
<div class="block">根据邮箱查找用户</div>
</dd>
<dt><a href="com/example/springboot2/service/CouponService.html#findById(java.lang.Long)" class="member-name-link">findById(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/CouponService.html" title="com.example.springboot2.service中的类">CouponService</a></dt>
<dd>
<div class="block">根据ID查找优惠券</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsCategoryService.html#findById(java.lang.Long)" class="member-name-link">findById(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsCategoryService.html" title="com.example.springboot2.service中的类">GoodsCategoryService</a></dt>
<dd>
<div class="block">根据ID查找分类</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsService.html#findById(java.lang.Long)" class="member-name-link">findById(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsService.html" title="com.example.springboot2.service中的类">GoodsService</a></dt>
<dd>
<div class="block">根据ID查找商品</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantService.html#findById(java.lang.Long)" class="member-name-link">findById(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantService.html" title="com.example.springboot2.service中的类">MerchantService</a></dt>
<dd>
<div class="block">根据ID查找商家</div>
</dd>
<dt><a href="com/example/springboot2/service/OrderService.html#findById(java.lang.Long)" class="member-name-link">findById(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/OrderService.html" title="com.example.springboot2.service中的类">OrderService</a></dt>
<dd>
<div class="block">根据ID查找订单</div>
</dd>
<dt><a href="com/example/springboot2/service/UserService.html#findById(java.lang.Long)" class="member-name-link">findById(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/UserService.html" title="com.example.springboot2.service中的类">UserService</a></dt>
<dd>
<div class="block">根据ID查找用户</div>
</dd>
<dt><a href="com/example/springboot2/repository/CouponRepository.html#findByMerchant(com.example.springboot2.entity.Merchant,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchant(Merchant, Pageable)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/CouponRepository.html" title="com.example.springboot2.repository中的接口">CouponRepository</a></dt>
<dd>
<div class="block">根据商家查找优惠券</div>
</dd>
<dt><a href="com/example/springboot2/repository/GoodsRepository.html#findByMerchant(com.example.springboot2.entity.Merchant,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchant(Merchant, Pageable)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/GoodsRepository.html" title="com.example.springboot2.repository中的接口">GoodsRepository</a></dt>
<dd>
<div class="block">根据商家查找商品</div>
</dd>
<dt><a href="com/example/springboot2/repository/LaundryOrderRepository.html#findByMerchant(com.example.springboot2.entity.Merchant,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchant(Merchant, Pageable)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryOrderRepository.html" title="com.example.springboot2.repository中的接口">LaundryOrderRepository</a></dt>
<dd>
<div class="block">根据商家查找订单</div>
</dd>
<dt><a href="com/example/springboot2/repository/LaundryServiceRepository.html#findByMerchant(com.example.springboot2.entity.Merchant,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchant(Merchant, Pageable)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryServiceRepository.html" title="com.example.springboot2.repository中的接口">LaundryServiceRepository</a></dt>
<dd>
<div class="block">根据商家查找服务</div>
</dd>
<dt><a href="com/example/springboot2/repository/OrderRepository.html#findByMerchant(com.example.springboot2.entity.Merchant,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchant(Merchant, Pageable)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/OrderRepository.html" title="com.example.springboot2.repository中的接口">OrderRepository</a></dt>
<dd>
<div class="block">根据商家查找订单</div>
</dd>
<dt><a href="com/example/springboot2/repository/GoodsCategoryRepository.html#findByMerchantAndIsEnabledTrueOrderBySortOrder(com.example.springboot2.entity.Merchant)" class="member-name-link">findByMerchantAndIsEnabledTrueOrderBySortOrder(Merchant)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/GoodsCategoryRepository.html" title="com.example.springboot2.repository中的接口">GoodsCategoryRepository</a></dt>
<dd>
<div class="block">根据商家查找分类</div>
</dd>
<dt><a href="com/example/springboot2/repository/LaundryServiceRepository.html#findByMerchantAndIsEnabledTrueOrderBySortOrder(com.example.springboot2.entity.Merchant)" class="member-name-link">findByMerchantAndIsEnabledTrueOrderBySortOrder(Merchant)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryServiceRepository.html" title="com.example.springboot2.repository中的接口">LaundryServiceRepository</a></dt>
<dd>
<div class="block">根据商家查找启用的服务</div>
</dd>
<dt><a href="com/example/springboot2/repository/GoodsRepository.html#findByMerchantAndIsNewTrueAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Goods.GoodsStatus,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchantAndIsNewTrueAndStatus(Merchant, Goods.GoodsStatus, Pageable)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/GoodsRepository.html" title="com.example.springboot2.repository中的接口">GoodsRepository</a></dt>
<dd>
<div class="block">查找新品</div>
</dd>
<dt><a href="com/example/springboot2/repository/GoodsRepository.html#findByMerchantAndIsRecommendedTrueAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Goods.GoodsStatus,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchantAndIsRecommendedTrueAndStatus(Merchant, Goods.GoodsStatus, Pageable)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/GoodsRepository.html" title="com.example.springboot2.repository中的接口">GoodsRepository</a></dt>
<dd>
<div class="block">查找推荐商品</div>
</dd>
<dt><a href="com/example/springboot2/repository/GoodsCategoryRepository.html#findByMerchantAndParentAndIsEnabledTrueOrderBySortOrder(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.GoodsCategory)" class="member-name-link">findByMerchantAndParentAndIsEnabledTrueOrderBySortOrder(Merchant, GoodsCategory)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/GoodsCategoryRepository.html" title="com.example.springboot2.repository中的接口">GoodsCategoryRepository</a></dt>
<dd>
<div class="block">根据商家和父分类查找子分类</div>
</dd>
<dt><a href="com/example/springboot2/repository/GoodsCategoryRepository.html#findByMerchantAndParentIsNullAndIsEnabledTrueOrderBySortOrder(com.example.springboot2.entity.Merchant)" class="member-name-link">findByMerchantAndParentIsNullAndIsEnabledTrueOrderBySortOrder(Merchant)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/GoodsCategoryRepository.html" title="com.example.springboot2.repository中的接口">GoodsCategoryRepository</a></dt>
<dd>
<div class="block">根据商家查找顶级分类</div>
</dd>
<dt><a href="com/example/springboot2/repository/LaundryServiceRepository.html#findByMerchantAndServiceTypeAndIsEnabledTrue(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.LaundryService.ServiceType)" class="member-name-link">findByMerchantAndServiceTypeAndIsEnabledTrue(Merchant, LaundryService.ServiceType)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryServiceRepository.html" title="com.example.springboot2.repository中的接口">LaundryServiceRepository</a></dt>
<dd>
<div class="block">根据商家和服务类型查找服务</div>
</dd>
<dt><a href="com/example/springboot2/repository/CouponRepository.html#findByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Coupon.CouponStatus,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchantAndStatus(Merchant, Coupon.CouponStatus, Pageable)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/CouponRepository.html" title="com.example.springboot2.repository中的接口">CouponRepository</a></dt>
<dd>
<div class="block">根据商家和状态查找优惠券</div>
</dd>
<dt><a href="com/example/springboot2/repository/GoodsRepository.html#findByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Goods.GoodsStatus,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchantAndStatus(Merchant, Goods.GoodsStatus, Pageable)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/GoodsRepository.html" title="com.example.springboot2.repository中的接口">GoodsRepository</a></dt>
<dd>
<div class="block">根据商家和状态查找商品</div>
</dd>
<dt><a href="com/example/springboot2/repository/LaundryOrderRepository.html#findByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.LaundryOrder.LaundryOrderStatus,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchantAndStatus(Merchant, LaundryOrder.LaundryOrderStatus, Pageable)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryOrderRepository.html" title="com.example.springboot2.repository中的接口">LaundryOrderRepository</a></dt>
<dd>
<div class="block">根据商家和状态查找订单</div>
</dd>
<dt><a href="com/example/springboot2/repository/OrderRepository.html#findByMerchantAndStatus(com.example.springboot2.entity.Merchant,com.example.springboot2.entity.Order.OrderStatus,org.springframework.data.domain.Pageable)" class="member-name-link">findByMerchantAndStatus(Merchant, Order.OrderStatus, Pageable)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/OrderRepository.html" title="com.example.springboot2.repository中的接口">OrderRepository</a></dt>
<dd>
<div class="block">根据商家和状态查找订单</div>
</dd>
<dt><a href="com/example/springboot2/repository/LaundryOrderRepository.html#findByOrderNo(java.lang.String)" class="member-name-link">findByOrderNo(String)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryOrderRepository.html" title="com.example.springboot2.repository中的接口">LaundryOrderRepository</a></dt>
<dd>
<div class="block">根据订单号查找订单</div>
</dd>
<dt><a href="com/example/springboot2/repository/OrderRepository.html#findByOrderNo(java.lang.String)" class="member-name-link">findByOrderNo(String)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/OrderRepository.html" title="com.example.springboot2.repository中的接口">OrderRepository</a></dt>
<dd>
<div class="block">根据订单号查找订单</div>
</dd>
<dt><a href="com/example/springboot2/repository/UserRepository.html#findByPhone(java.lang.String)" class="member-name-link">findByPhone(String)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/UserRepository.html" title="com.example.springboot2.repository中的接口">UserRepository</a></dt>
<dd>
<div class="block">根据手机号查找用户</div>
</dd>
<dt><a href="com/example/springboot2/repository/MerchantRepository.html#findByShopName(java.lang.String)" class="member-name-link">findByShopName(String)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/MerchantRepository.html" title="com.example.springboot2.repository中的接口">MerchantRepository</a></dt>
<dd>
<div class="block">根据店铺名称查找商家</div>
</dd>
<dt><a href="com/example/springboot2/repository/MerchantRepository.html#findByUser(com.example.springboot2.entity.User)" class="member-name-link">findByUser(User)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/MerchantRepository.html" title="com.example.springboot2.repository中的接口">MerchantRepository</a></dt>
<dd>
<div class="block">根据用户查找商家</div>
</dd>
<dt><a href="com/example/springboot2/repository/MerchantRepository.html#findByUserId(java.lang.Long)" class="member-name-link">findByUserId(Long)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/MerchantRepository.html" title="com.example.springboot2.repository中的接口">MerchantRepository</a></dt>
<dd>
<div class="block">根据用户ID查找商家</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantService.html#findByUserId(java.lang.Long)" class="member-name-link">findByUserId(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantService.html" title="com.example.springboot2.service中的类">MerchantService</a></dt>
<dd>
<div class="block">根据用户ID查找商家</div>
</dd>
<dt><a href="com/example/springboot2/repository/UserRepository.html#findByUsername(java.lang.String)" class="member-name-link">findByUsername(String)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/UserRepository.html" title="com.example.springboot2.repository中的接口">UserRepository</a></dt>
<dd>
<div class="block">根据用户名查找用户</div>
</dd>
<dt><a href="com/example/springboot2/service/UserService.html#findByUsername(java.lang.String)" class="member-name-link">findByUsername(String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/UserService.html" title="com.example.springboot2.service中的类">UserService</a></dt>
<dd>
<div class="block">根据用户名查找用户</div>
</dd>
<dt><a href="com/example/springboot2/repository/GoodsRepository.html#findHotGoodsByMerchant(com.example.springboot2.entity.Merchant,org.springframework.data.domain.Pageable)" class="member-name-link">findHotGoodsByMerchant(Merchant, Pageable)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/GoodsRepository.html" title="com.example.springboot2.repository中的接口">GoodsRepository</a></dt>
<dd>
<div class="block">查找热销商品</div>
</dd>
<dt><a href="com/example/springboot2/repository/LaundryOrderRepository.html#findTodayOrdersByMerchant(com.example.springboot2.entity.Merchant)" class="member-name-link">findTodayOrdersByMerchant(Merchant)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryOrderRepository.html" title="com.example.springboot2.repository中的接口">LaundryOrderRepository</a></dt>
<dd>
<div class="block">查找商家今日订单</div>
</dd>
<dt><a href="com/example/springboot2/repository/OrderRepository.html#findTodayOrdersByMerchant(com.example.springboot2.entity.Merchant)" class="member-name-link">findTodayOrdersByMerchant(Merchant)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/OrderRepository.html" title="com.example.springboot2.repository中的接口">OrderRepository</a></dt>
<dd>
<div class="block">查找商家今日订单</div>
</dd>
<dt><a href="com/example/springboot2/repository/CouponRepository.html#findValidCouponsByMerchant(com.example.springboot2.entity.Merchant,java.time.LocalDateTime)" class="member-name-link">findValidCouponsByMerchant(Merchant, LocalDateTime)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/CouponRepository.html" title="com.example.springboot2.repository中的接口">CouponRepository</a></dt>
<dd>
<div class="block">查找有效的优惠券</div>
</dd>
<dt><a href="com/example/springboot2/entity/Coupon.CouponStatus.html#FINISHED" class="member-name-link">FINISHED</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Coupon.CouponStatus.html" title="enum class in com.example.springboot2.entity">Coupon.CouponStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Order.OrderStatus.html#FINISHED" class="member-name-link">FINISHED</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Order.OrderStatus.html" title="enum class in com.example.springboot2.entity">Order.OrderStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/common/Result.html#forbidden()" class="member-name-link">forbidden()</a> - 类中的静态方法 com.example.springboot2.common.<a href="com/example/springboot2/common/Result.html" title="com.example.springboot2.common中的类">Result</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/common/Result.html#forbidden(java.lang.String)" class="member-name-link">forbidden(String)</a> - 类中的静态方法 com.example.springboot2.common.<a href="com/example/springboot2/common/Result.html" title="com.example.springboot2.common中的类">Result</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Coupon.CouponType.html#FREE_SHIPPING" class="member-name-link">FREE_SHIPPING</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Coupon.CouponType.html" title="enum class in com.example.springboot2.entity">Coupon.CouponType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Coupon.CouponType.html#FULL_REDUCTION" class="member-name-link">FULL_REDUCTION</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Coupon.CouponType.html" title="enum class in com.example.springboot2.entity">Coupon.CouponType</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:G">G</h2>
<dl class="index">
<dt><a href="com/example/springboot2/util/JwtUtil.html#generateToken(java.lang.String,java.lang.Long,java.lang.String)" class="member-name-link">generateToken(String, Long, String)</a> - 类中的方法 com.example.springboot2.util.<a href="com/example/springboot2/util/JwtUtil.html" title="com.example.springboot2.util中的类">JwtUtil</a></dt>
<dd>
<div class="block">生成JWT token</div>
</dd>
<dt><a href="com/example/springboot2/entity/User.html#getAuthorities()" class="member-name-link">getAuthorities()</a> - 类中的方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/User.html" title="com.example.springboot2.entity中的类">User</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#getAutoWithdrawSettings(java.lang.Long)" class="member-name-link">getAutoWithdrawSettings(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantFinanceController.html#getAutoWithdrawSettings(org.springframework.security.core.Authentication)" class="member-name-link">getAutoWithdrawSettings(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></dt>
<dd>
<div class="block">获取自动提现设置</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#getBalance(java.lang.Long)" class="member-name-link">getBalance(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#getBankAccounts(java.lang.Long)" class="member-name-link">getBankAccounts(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#getBankCards(java.lang.Long)" class="member-name-link">getBankCards(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantFinanceController.html#getBankCards(org.springframework.security.core.Authentication)" class="member-name-link">getBankCards(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></dt>
<dd>
<div class="block">获取银行卡信息</div>
</dd>
<dt><a href="com/example/springboot2/controller/GoodsCategoryController.html#getCategoriesTree(org.springframework.security.core.Authentication)" class="member-name-link">getCategoriesTree(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/GoodsCategoryController.html" title="com.example.springboot2.controller中的类">GoodsCategoryController</a></dt>
<dd>
<div class="block">获取所有分类（树形结构）</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsCategoryService.html#getCategoryDetail(java.lang.Long,java.lang.Long)" class="member-name-link">getCategoryDetail(Long, Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsCategoryService.html" title="com.example.springboot2.service中的类">GoodsCategoryService</a></dt>
<dd>
<div class="block">获取分类详情</div>
</dd>
<dt><a href="com/example/springboot2/controller/GoodsCategoryController.html#getCategoryDetail(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">getCategoryDetail(Long, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/GoodsCategoryController.html" title="com.example.springboot2.controller中的类">GoodsCategoryController</a></dt>
<dd>
<div class="block">获取分类详情</div>
</dd>
<dt><a href="com/example/springboot2/controller/MerchantController.html#getCertificationInfo(org.springframework.security.core.Authentication)" class="member-name-link">getCertificationInfo(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantController.html" title="com.example.springboot2.controller中的类">MerchantController</a></dt>
<dd>
<div class="block">获取认证信息</div>
</dd>
<dt><a href="com/example/springboot2/util/JwtUtil.html#getClaimFromToken(java.lang.String,com.example.springboot2.util.JwtUtil.ClaimsResolver)" class="member-name-link">getClaimFromToken(String, JwtUtil.ClaimsResolver&lt;T&gt;)</a> - 类中的方法 com.example.springboot2.util.<a href="com/example/springboot2/util/JwtUtil.html" title="com.example.springboot2.util中的类">JwtUtil</a></dt>
<dd>
<div class="block">从token中获取指定声明</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#getCommissionDetails(java.lang.Long,java.lang.String,java.lang.String,org.springframework.data.domain.Pageable)" class="member-name-link">getCommissionDetails(Long, String, String, Pageable)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantFinanceController.html#getCommissionDetails(java.lang.String,java.lang.String,int,int,org.springframework.security.core.Authentication)" class="member-name-link">getCommissionDetails(String, String, int, int, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></dt>
<dd>
<div class="block">获取佣金明细</div>
</dd>
<dt><a href="com/example/springboot2/service/CouponService.html#getCouponDetail(java.lang.Long,java.lang.Long)" class="member-name-link">getCouponDetail(Long, Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/CouponService.html" title="com.example.springboot2.service中的类">CouponService</a></dt>
<dd>
<div class="block">获取优惠券详情</div>
</dd>
<dt><a href="com/example/springboot2/controller/CouponController.html#getCouponDetail(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">getCouponDetail(Long, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/CouponController.html" title="com.example.springboot2.controller中的类">CouponController</a></dt>
<dd>
<div class="block">获取优惠券详情</div>
</dd>
<dt><a href="com/example/springboot2/controller/CouponController.html#getCouponList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getCouponList(Integer, Integer, String, String, String, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/CouponController.html" title="com.example.springboot2.controller中的类">CouponController</a></dt>
<dd>
<div class="block">分页查询优惠券列表</div>
</dd>
<dt><a href="com/example/springboot2/service/CouponService.html#getCouponList(java.lang.Long,java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">getCouponList(Long, Integer, Integer, String, String, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/CouponService.html" title="com.example.springboot2.service中的类">CouponService</a></dt>
<dd>
<div class="block">分页查询优惠券列表</div>
</dd>
<dt><a href="com/example/springboot2/service/CouponService.html#getCouponStats(java.lang.Long)" class="member-name-link">getCouponStats(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/CouponService.html" title="com.example.springboot2.service中的类">CouponService</a></dt>
<dd>
<div class="block">获取优惠券统计数据</div>
</dd>
<dt><a href="com/example/springboot2/controller/CouponController.html#getCouponStats(org.springframework.security.core.Authentication)" class="member-name-link">getCouponStats(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/CouponController.html" title="com.example.springboot2.controller中的类">CouponController</a></dt>
<dd>
<div class="block">获取优惠券统计数据</div>
</dd>
<dt><a href="com/example/springboot2/controller/DashboardController.html#getCustomerStats(java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getCustomerStats(String, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/DashboardController.html" title="com.example.springboot2.controller中的类">DashboardController</a></dt>
<dd>
<div class="block">获取客户统计</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#getDepositInfo(java.lang.Long)" class="member-name-link">getDepositInfo(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantFinanceController.html#getDepositInfo(org.springframework.security.core.Authentication)" class="member-name-link">getDepositInfo(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></dt>
<dd>
<div class="block">获取保证金信息</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#getEarningsStatistics(java.lang.Long,java.lang.String)" class="member-name-link">getEarningsStatistics(Long, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/util/JwtUtil.html#getExpirationDateFromToken(java.lang.String)" class="member-name-link">getExpirationDateFromToken(String)</a> - 类中的方法 com.example.springboot2.util.<a href="com/example/springboot2/util/JwtUtil.html" title="com.example.springboot2.util中的类">JwtUtil</a></dt>
<dd>
<div class="block">从token中获取过期时间</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#getFinanceOverview(java.lang.Long)" class="member-name-link">getFinanceOverview(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantFinanceController.html#getFinanceOverview(org.springframework.security.core.Authentication)" class="member-name-link">getFinanceOverview(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></dt>
<dd>
<div class="block">获取财务概览</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#getFinanceReports(java.lang.Long,java.lang.String,java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">getFinanceReports(Long, String, String, String, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantFinanceController.html#getFinanceReports(java.lang.String,java.lang.String,java.lang.String,java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getFinanceReports(String, String, String, String, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></dt>
<dd>
<div class="block">获取财务报表</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsCategoryService.html#getGoodsCategories(java.lang.Long)" class="member-name-link">getGoodsCategories(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsCategoryService.html" title="com.example.springboot2.service中的类">GoodsCategoryService</a></dt>
<dd>
<div class="block">获取商家所有分类</div>
</dd>
<dt><a href="com/example/springboot2/controller/GoodsCategoryController.html#getGoodsCategories(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">getGoodsCategories(Long, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/GoodsCategoryController.html" title="com.example.springboot2.controller中的类">GoodsCategoryController</a></dt>
<dd>
<div class="block">获取商品分类列表</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsService.html#getGoodsDetail(java.lang.Long,java.lang.Long)" class="member-name-link">getGoodsDetail(Long, Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsService.html" title="com.example.springboot2.service中的类">GoodsService</a></dt>
<dd>
<div class="block">获取商品详情</div>
</dd>
<dt><a href="com/example/springboot2/controller/GoodsController.html#getGoodsDetail(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">getGoodsDetail(Long, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/GoodsController.html" title="com.example.springboot2.controller中的类">GoodsController</a></dt>
<dd>
<div class="block">获取商品详情</div>
</dd>
<dt><a href="com/example/springboot2/controller/GoodsController.html#getGoodsList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">getGoodsList(Integer, Integer, String, String, Long, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/GoodsController.html" title="com.example.springboot2.controller中的类">GoodsController</a></dt>
<dd>
<div class="block">分页查询商品列表</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsService.html#getGoodsList(java.lang.Long,java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.Long)" class="member-name-link">getGoodsList(Long, Integer, Integer, String, String, Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsService.html" title="com.example.springboot2.service中的类">GoodsService</a></dt>
<dd>
<div class="block">分页查询商品列表</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsService.html#getGoodsStats(java.lang.Long)" class="member-name-link">getGoodsStats(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsService.html" title="com.example.springboot2.service中的类">GoodsService</a></dt>
<dd>
<div class="block">获取商品统计数据</div>
</dd>
<dt><a href="com/example/springboot2/controller/GoodsController.html#getGoodsStats(org.springframework.security.core.Authentication)" class="member-name-link">getGoodsStats(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/GoodsController.html" title="com.example.springboot2.controller中的类">GoodsController</a></dt>
<dd>
<div class="block">获取商品统计数据</div>
</dd>
<dt><a href="com/example/springboot2/controller/GoodsController.html#getHotGoods(java.lang.Integer,org.springframework.security.core.Authentication)" class="member-name-link">getHotGoods(Integer, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/GoodsController.html" title="com.example.springboot2.controller中的类">GoodsController</a></dt>
<dd>
<div class="block">获取热销商品</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsService.html#getHotGoods(java.lang.Long,java.lang.Integer)" class="member-name-link">getHotGoods(Long, Integer)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsService.html" title="com.example.springboot2.service中的类">GoodsService</a></dt>
<dd>
<div class="block">获取热销商品</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#getIncomeDetails(java.lang.Long,java.lang.String,java.lang.String,java.lang.String,org.springframework.data.domain.Pageable)" class="member-name-link">getIncomeDetails(Long, String, String, String, Pageable)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantFinanceController.html#getIncomeDetails(java.lang.String,java.lang.String,java.lang.String,int,int,org.springframework.security.core.Authentication)" class="member-name-link">getIncomeDetails(String, String, String, int, int, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></dt>
<dd>
<div class="block">获取收入明细</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#getIncomeStatistics(java.lang.Long,java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">getIncomeStatistics(Long, String, String, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantFinanceController.html#getIncomeStatistics(java.lang.String,java.lang.String,java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getIncomeStatistics(String, String, String, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></dt>
<dd>
<div class="block">获取收入统计</div>
</dd>
<dt><a href="com/example/springboot2/service/LaundryBusinessService.html#getLaundryOrderDetail(java.lang.Long,java.lang.Long)" class="member-name-link">getLaundryOrderDetail(Long, Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/LaundryBusinessService.html" title="com.example.springboot2.service中的类">LaundryBusinessService</a></dt>
<dd>
<div class="block">获取洗护订单详情</div>
</dd>
<dt><a href="com/example/springboot2/controller/LaundryController.html#getLaundryOrderDetail(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">getLaundryOrderDetail(Long, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/LaundryController.html" title="com.example.springboot2.controller中的类">LaundryController</a></dt>
<dd>
<div class="block">获取洗护订单详情</div>
</dd>
<dt><a href="com/example/springboot2/controller/LaundryController.html#getLaundryOrders(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getLaundryOrders(Integer, Integer, String, String, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/LaundryController.html" title="com.example.springboot2.controller中的类">LaundryController</a></dt>
<dd>
<div class="block">分页查询洗护订单列表</div>
</dd>
<dt><a href="com/example/springboot2/service/LaundryBusinessService.html#getLaundryOrders(java.lang.Long,java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String)" class="member-name-link">getLaundryOrders(Long, Integer, Integer, String, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/LaundryBusinessService.html" title="com.example.springboot2.service中的类">LaundryBusinessService</a></dt>
<dd>
<div class="block">分页查询洗护订单列表</div>
</dd>
<dt><a href="com/example/springboot2/service/LaundryBusinessService.html#getLaundryServiceDetail(java.lang.Long,java.lang.Long)" class="member-name-link">getLaundryServiceDetail(Long, Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/LaundryBusinessService.html" title="com.example.springboot2.service中的类">LaundryBusinessService</a></dt>
<dd>
<div class="block">获取洗护服务详情</div>
</dd>
<dt><a href="com/example/springboot2/controller/LaundryController.html#getLaundryServiceDetail(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">getLaundryServiceDetail(Long, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/LaundryController.html" title="com.example.springboot2.controller中的类">LaundryController</a></dt>
<dd>
<div class="block">获取洗护服务详情</div>
</dd>
<dt><a href="com/example/springboot2/controller/LaundryController.html#getLaundryServices(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getLaundryServices(Integer, Integer, String, String, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/LaundryController.html" title="com.example.springboot2.controller中的类">LaundryController</a></dt>
<dd>
<div class="block">分页查询洗护服务列表</div>
</dd>
<dt><a href="com/example/springboot2/service/LaundryBusinessService.html#getLaundryServices(java.lang.Long,java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String)" class="member-name-link">getLaundryServices(Long, Integer, Integer, String, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/LaundryBusinessService.html" title="com.example.springboot2.service中的类">LaundryBusinessService</a></dt>
<dd>
<div class="block">分页查询洗护服务列表</div>
</dd>
<dt><a href="com/example/springboot2/controller/DashboardController.html#getMerchantAfterSales(org.springframework.security.core.Authentication)" class="member-name-link">getMerchantAfterSales(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/DashboardController.html" title="com.example.springboot2.controller中的类">DashboardController</a></dt>
<dd>
<div class="block">获取商家首页售后数据</div>
</dd>
<dt><a href="com/example/springboot2/controller/DashboardController.html#getMerchantCategorySales(org.springframework.security.core.Authentication)" class="member-name-link">getMerchantCategorySales(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/DashboardController.html" title="com.example.springboot2.controller中的类">DashboardController</a></dt>
<dd>
<div class="block">获取商品分类销售数据</div>
</dd>
<dt><a href="com/example/springboot2/service/DashboardService.html#getMerchantGoods(java.lang.Long)" class="member-name-link">getMerchantGoods(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/DashboardService.html" title="com.example.springboot2.service中的类">DashboardService</a></dt>
<dd>
<div class="block">获取商家首页商品数据</div>
</dd>
<dt><a href="com/example/springboot2/controller/DashboardController.html#getMerchantGoods(org.springframework.security.core.Authentication)" class="member-name-link">getMerchantGoods(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/DashboardController.html" title="com.example.springboot2.controller中的类">DashboardController</a></dt>
<dd>
<div class="block">获取商家首页商品数据</div>
</dd>
<dt><a href="com/example/springboot2/controller/DashboardController.html#getMerchantHotGoods(java.lang.Integer,org.springframework.security.core.Authentication)" class="member-name-link">getMerchantHotGoods(Integer, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/DashboardController.html" title="com.example.springboot2.controller中的类">DashboardController</a></dt>
<dd>
<div class="block">获取热销商品数据</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantService.html#getMerchantInfo(java.lang.Long)" class="member-name-link">getMerchantInfo(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantService.html" title="com.example.springboot2.service中的类">MerchantService</a></dt>
<dd>
<div class="block">获取商家信息</div>
</dd>
<dt><a href="com/example/springboot2/controller/MerchantController.html#getMerchantInfo(org.springframework.security.core.Authentication)" class="member-name-link">getMerchantInfo(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantController.html" title="com.example.springboot2.controller中的类">MerchantController</a></dt>
<dd>
<div class="block">获取商家信息</div>
</dd>
<dt><a href="com/example/springboot2/service/DashboardService.html#getMerchantOrders(java.lang.Long)" class="member-name-link">getMerchantOrders(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/DashboardService.html" title="com.example.springboot2.service中的类">DashboardService</a></dt>
<dd>
<div class="block">获取商家首页订单数据</div>
</dd>
<dt><a href="com/example/springboot2/controller/DashboardController.html#getMerchantOrders(org.springframework.security.core.Authentication)" class="member-name-link">getMerchantOrders(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/DashboardController.html" title="com.example.springboot2.controller中的类">DashboardController</a></dt>
<dd>
<div class="block">获取商家首页订单数据</div>
</dd>
<dt><a href="com/example/springboot2/service/DashboardService.html#getMerchantOverview(java.lang.Long)" class="member-name-link">getMerchantOverview(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/DashboardService.html" title="com.example.springboot2.service中的类">DashboardService</a></dt>
<dd>
<div class="block">获取商家首页概览数据</div>
</dd>
<dt><a href="com/example/springboot2/controller/DashboardController.html#getMerchantOverview(org.springframework.security.core.Authentication)" class="member-name-link">getMerchantOverview(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/DashboardController.html" title="com.example.springboot2.controller中的类">DashboardController</a></dt>
<dd>
<div class="block">获取商家首页概览数据</div>
</dd>
<dt><a href="com/example/springboot2/service/DashboardService.html#getMerchantPendingTasks(java.lang.Long)" class="member-name-link">getMerchantPendingTasks(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/DashboardService.html" title="com.example.springboot2.service中的类">DashboardService</a></dt>
<dd>
<div class="block">获取商家首页待处理事项</div>
</dd>
<dt><a href="com/example/springboot2/controller/DashboardController.html#getMerchantPendingTasks(org.springframework.security.core.Authentication)" class="member-name-link">getMerchantPendingTasks(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/DashboardController.html" title="com.example.springboot2.controller中的类">DashboardController</a></dt>
<dd>
<div class="block">获取商家首页待处理事项</div>
</dd>
<dt><a href="com/example/springboot2/service/DashboardService.html#getMerchantSalesTrend(java.lang.Long,java.lang.String)" class="member-name-link">getMerchantSalesTrend(Long, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/DashboardService.html" title="com.example.springboot2.service中的类">DashboardService</a></dt>
<dd>
<div class="block">获取商家首页销售趋势数据</div>
</dd>
<dt><a href="com/example/springboot2/controller/DashboardController.html#getMerchantSalesTrend(java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getMerchantSalesTrend(String, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/DashboardController.html" title="com.example.springboot2.controller中的类">DashboardController</a></dt>
<dd>
<div class="block">获取商家首页销售趋势数据</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantServiceManagementService.html#getMerchantServices(java.lang.Long,java.lang.String,java.lang.String,org.springframework.data.domain.Pageable)" class="member-name-link">getMerchantServices(Long, String, String, Pageable)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantServiceManagementService.html" title="com.example.springboot2.service中的类">MerchantServiceManagementService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantServiceController.html#getMerchantServices(java.lang.String,java.lang.String,int,int,org.springframework.security.core.Authentication)" class="member-name-link">getMerchantServices(String, String, int, int, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantServiceController.html" title="com.example.springboot2.controller中的类">MerchantServiceController</a></dt>
<dd>
<div class="block">获取商家的服务列表</div>
</dd>
<dt><a href="com/example/springboot2/service/DashboardService.html#getMerchantTransaction(java.lang.Long)" class="member-name-link">getMerchantTransaction(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/DashboardService.html" title="com.example.springboot2.service中的类">DashboardService</a></dt>
<dd>
<div class="block">获取商家首页交易数据</div>
</dd>
<dt><a href="com/example/springboot2/controller/DashboardController.html#getMerchantTransaction(org.springframework.security.core.Authentication)" class="member-name-link">getMerchantTransaction(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/DashboardController.html" title="com.example.springboot2.controller中的类">DashboardController</a></dt>
<dd>
<div class="block">获取商家首页交易数据</div>
</dd>
<dt><a href="com/example/springboot2/controller/DashboardController.html#getMerchantViolations(org.springframework.security.core.Authentication)" class="member-name-link">getMerchantViolations(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/DashboardController.html" title="com.example.springboot2.controller中的类">DashboardController</a></dt>
<dd>
<div class="block">获取商家首页违规数据</div>
</dd>
<dt><a href="com/example/springboot2/service/OrderService.html#getOrderDetail(java.lang.Long,java.lang.Long)" class="member-name-link">getOrderDetail(Long, Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/OrderService.html" title="com.example.springboot2.service中的类">OrderService</a></dt>
<dd>
<div class="block">获取订单详情</div>
</dd>
<dt><a href="com/example/springboot2/controller/OrderController.html#getOrderDetail(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">getOrderDetail(Long, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/OrderController.html" title="com.example.springboot2.controller中的类">OrderController</a></dt>
<dd>
<div class="block">获取订单详情</div>
</dd>
<dt><a href="com/example/springboot2/controller/OrderController.html#getOrdersList(java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getOrdersList(Integer, Integer, String, String, String, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/OrderController.html" title="com.example.springboot2.controller中的类">OrderController</a></dt>
<dd>
<div class="block">分页查询订单列表</div>
</dd>
<dt><a href="com/example/springboot2/service/OrderService.html#getOrdersList(java.lang.Long,java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">getOrdersList(Long, Integer, Integer, String, String, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/OrderService.html" title="com.example.springboot2.service中的类">OrderService</a></dt>
<dd>
<div class="block">分页查询订单列表</div>
</dd>
<dt><a href="com/example/springboot2/service/OrderService.html#getOrderStats(java.lang.Long)" class="member-name-link">getOrderStats(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/OrderService.html" title="com.example.springboot2.service中的类">OrderService</a></dt>
<dd>
<div class="block">获取订单统计数据</div>
</dd>
<dt><a href="com/example/springboot2/controller/OrderController.html#getOrderStats(org.springframework.security.core.Authentication)" class="member-name-link">getOrderStats(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/OrderController.html" title="com.example.springboot2.controller中的类">OrderController</a></dt>
<dd>
<div class="block">获取订单统计数据</div>
</dd>
<dt><a href="com/example/springboot2/controller/DashboardController.html#getRealTimeStats(org.springframework.security.core.Authentication)" class="member-name-link">getRealTimeStats(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/DashboardController.html" title="com.example.springboot2.controller中的类">DashboardController</a></dt>
<dd>
<div class="block">获取实时统计数据</div>
</dd>
<dt><a href="com/example/springboot2/controller/RegionController.html#getRegionData()" class="member-name-link">getRegionData()</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/RegionController.html" title="com.example.springboot2.controller中的类">RegionController</a></dt>
<dd>
<div class="block">获取地区数据</div>
</dd>
<dt><a href="com/example/springboot2/controller/DashboardController.html#getRevenueStats(java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getRevenueStats(String, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/DashboardController.html" title="com.example.springboot2.controller中的类">DashboardController</a></dt>
<dd>
<div class="block">获取收入统计</div>
</dd>
<dt><a href="com/example/springboot2/util/JwtUtil.html#getRoleFromToken(java.lang.String)" class="member-name-link">getRoleFromToken(String)</a> - 类中的方法 com.example.springboot2.util.<a href="com/example/springboot2/util/JwtUtil.html" title="com.example.springboot2.util中的类">JwtUtil</a></dt>
<dd>
<div class="block">从token中获取角色</div>
</dd>
<dt><a href="com/example/springboot2/controller/MerchantServiceController.html#getServiceCategories()" class="member-name-link">getServiceCategories()</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantServiceController.html" title="com.example.springboot2.controller中的类">MerchantServiceController</a></dt>
<dd>
<div class="block">获取服务分类列表</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantServiceManagementService.html#getServiceCategories()" class="member-name-link">getServiceCategories()</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantServiceManagementService.html" title="com.example.springboot2.service中的类">MerchantServiceManagementService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/MerchantServiceManagementService.html#getServiceDetail(java.lang.Long,java.lang.Long)" class="member-name-link">getServiceDetail(Long, Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantServiceManagementService.html" title="com.example.springboot2.service中的类">MerchantServiceManagementService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantServiceController.html#getServiceDetail(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">getServiceDetail(Long, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantServiceController.html" title="com.example.springboot2.controller中的类">MerchantServiceController</a></dt>
<dd>
<div class="block">获取服务详情</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantServiceManagementService.html#getServiceOrderStatistics(java.lang.Long,java.lang.Long,java.lang.String)" class="member-name-link">getServiceOrderStatistics(Long, Long, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantServiceManagementService.html" title="com.example.springboot2.service中的类">MerchantServiceManagementService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantServiceController.html#getServiceOrderStatistics(java.lang.Long,java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getServiceOrderStatistics(Long, String, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantServiceController.html" title="com.example.springboot2.controller中的类">MerchantServiceController</a></dt>
<dd>
<div class="block">获取服务订单统计</div>
</dd>
<dt><a href="com/example/springboot2/controller/MerchantServiceController.html#getServiceReviews(java.lang.Long,java.lang.Integer,int,int,org.springframework.security.core.Authentication)" class="member-name-link">getServiceReviews(Long, Integer, int, int, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantServiceController.html" title="com.example.springboot2.controller中的类">MerchantServiceController</a></dt>
<dd>
<div class="block">获取服务评价</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantServiceManagementService.html#getServiceReviews(java.lang.Long,java.lang.Long,java.lang.Integer,org.springframework.data.domain.Pageable)" class="member-name-link">getServiceReviews(Long, Long, Integer, Pageable)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantServiceManagementService.html" title="com.example.springboot2.service中的类">MerchantServiceManagementService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/MerchantServiceManagementService.html#getServiceStatistics(java.lang.Long)" class="member-name-link">getServiceStatistics(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantServiceManagementService.html" title="com.example.springboot2.service中的类">MerchantServiceManagementService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantServiceController.html#getServiceStatistics(org.springframework.security.core.Authentication)" class="member-name-link">getServiceStatistics(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantServiceController.html" title="com.example.springboot2.controller中的类">MerchantServiceController</a></dt>
<dd>
<div class="block">获取服务统计</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsCategoryService.html#getSubCategories(java.lang.Long,java.lang.Long)" class="member-name-link">getSubCategories(Long, Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsCategoryService.html" title="com.example.springboot2.service中的类">GoodsCategoryService</a></dt>
<dd>
<div class="block">获取子分类</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#getTaxInfo(java.lang.Long,java.lang.String)" class="member-name-link">getTaxInfo(Long, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantFinanceController.html#getTaxInfo(java.lang.String,org.springframework.security.core.Authentication)" class="member-name-link">getTaxInfo(String, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></dt>
<dd>
<div class="block">获取税务信息</div>
</dd>
<dt><a href="com/example/springboot2/service/OrderService.html#getTodayOrders(java.lang.Long)" class="member-name-link">getTodayOrders(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/OrderService.html" title="com.example.springboot2.service中的类">OrderService</a></dt>
<dd>
<div class="block">获取今日订单</div>
</dd>
<dt><a href="com/example/springboot2/controller/OrderController.html#getTodayOrders(org.springframework.security.core.Authentication)" class="member-name-link">getTodayOrders(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/OrderController.html" title="com.example.springboot2.controller中的类">OrderController</a></dt>
<dd>
<div class="block">获取今日订单</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsCategoryService.html#getTopCategories(java.lang.Long)" class="member-name-link">getTopCategories(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsCategoryService.html" title="com.example.springboot2.service中的类">GoodsCategoryService</a></dt>
<dd>
<div class="block">获取顶级分类</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#getTransactionHistory(java.lang.Long,int,int)" class="member-name-link">getTransactionHistory(Long, int, int)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/util/JwtUtil.html#getUserIdFromToken(java.lang.String)" class="member-name-link">getUserIdFromToken(String)</a> - 类中的方法 com.example.springboot2.util.<a href="com/example/springboot2/util/JwtUtil.html" title="com.example.springboot2.util中的类">JwtUtil</a></dt>
<dd>
<div class="block">从token中获取用户ID</div>
</dd>
<dt><a href="com/example/springboot2/controller/SimpleAuthController.html#getUserInfo(java.lang.String)" class="member-name-link">getUserInfo(String)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/SimpleAuthController.html" title="com.example.springboot2.controller中的类">SimpleAuthController</a></dt>
<dd>
<div class="block">获取用户信息</div>
</dd>
<dt><a href="com/example/springboot2/controller/AuthController.html#getUserInfo(org.springframework.security.core.Authentication)" class="member-name-link">getUserInfo(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/AuthController.html" title="com.example.springboot2.controller中的类">AuthController</a></dt>
<dd>
<div class="block">获取用户信息</div>
</dd>
<dt><a href="com/example/springboot2/util/JwtUtil.html#getUsernameFromToken(java.lang.String)" class="member-name-link">getUsernameFromToken(String)</a> - 类中的方法 com.example.springboot2.util.<a href="com/example/springboot2/util/JwtUtil.html" title="com.example.springboot2.util中的类">JwtUtil</a></dt>
<dd>
<div class="block">从token中获取用户名</div>
</dd>
<dt><a href="com/example/springboot2/service/CouponService.html#getValidCoupons(java.lang.Long)" class="member-name-link">getValidCoupons(Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/CouponService.html" title="com.example.springboot2.service中的类">CouponService</a></dt>
<dd>
<div class="block">获取有效优惠券</div>
</dd>
<dt><a href="com/example/springboot2/controller/CouponController.html#getValidCoupons(org.springframework.security.core.Authentication)" class="member-name-link">getValidCoupons(Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/CouponController.html" title="com.example.springboot2.controller中的类">CouponController</a></dt>
<dd>
<div class="block">获取有效优惠券</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#getWithdrawalHistory(java.lang.Long,int,int)" class="member-name-link">getWithdrawalHistory(Long, int, int)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#getWithdrawalRecords(java.lang.Long,java.lang.String,org.springframework.data.domain.Pageable)" class="member-name-link">getWithdrawalRecords(Long, String, Pageable)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantFinanceController.html#getWithdrawalRecords(java.lang.String,int,int,org.springframework.security.core.Authentication)" class="member-name-link">getWithdrawalRecords(String, int, int, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></dt>
<dd>
<div class="block">获取提现记录</div>
</dd>
<dt><a href="com/example/springboot2/exception/GlobalExceptionHandler.html" class="type-name-link" title="com.example.springboot2.exception中的类">GlobalExceptionHandler</a> - <a href="com/example/springboot2/exception/package-summary.html">com.example.springboot2.exception</a>中的类</dt>
<dd>
<div class="block">全局异常处理器</div>
</dd>
<dt><a href="com/example/springboot2/exception/GlobalExceptionHandler.html#%3Cinit%3E()" class="member-name-link">GlobalExceptionHandler()</a> - 类的构造器 com.example.springboot2.exception.<a href="com/example/springboot2/exception/GlobalExceptionHandler.html" title="com.example.springboot2.exception中的类">GlobalExceptionHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Goods.html" class="type-name-link" title="com.example.springboot2.entity中的类">Goods</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的类</dt>
<dd>
<div class="block">商品实体类</div>
</dd>
<dt><a href="com/example/springboot2/entity/Goods.html#%3Cinit%3E()" class="member-name-link">Goods()</a> - 类的构造器 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Goods.html" title="com.example.springboot2.entity中的类">Goods</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Order.OrderType.html#GOODS" class="member-name-link">GOODS</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Order.OrderType.html" title="enum class in com.example.springboot2.entity">Order.OrderType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Goods.GoodsStatus.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的Enum Class</dt>
<dd>
<div class="block">商品状态枚举</div>
</dd>
<dt><a href="com/example/springboot2/entity/Goods.GoodsType.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Goods.GoodsType</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的Enum Class</dt>
<dd>
<div class="block">商品类型枚举</div>
</dd>
<dt><a href="com/example/springboot2/entity/GoodsCategory.html" class="type-name-link" title="com.example.springboot2.entity中的类">GoodsCategory</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的类</dt>
<dd>
<div class="block">商品分类实体类</div>
</dd>
<dt><a href="com/example/springboot2/entity/GoodsCategory.html#%3Cinit%3E()" class="member-name-link">GoodsCategory()</a> - 类的构造器 com.example.springboot2.entity.<a href="com/example/springboot2/entity/GoodsCategory.html" title="com.example.springboot2.entity中的类">GoodsCategory</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/GoodsCategoryController.html" class="type-name-link" title="com.example.springboot2.controller中的类">GoodsCategoryController</a> - <a href="com/example/springboot2/controller/package-summary.html">com.example.springboot2.controller</a>中的类</dt>
<dd>
<div class="block">商品分类控制器</div>
</dd>
<dt><a href="com/example/springboot2/controller/GoodsCategoryController.html#%3Cinit%3E()" class="member-name-link">GoodsCategoryController()</a> - 类的构造器 com.example.springboot2.controller.<a href="com/example/springboot2/controller/GoodsCategoryController.html" title="com.example.springboot2.controller中的类">GoodsCategoryController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/repository/GoodsCategoryRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">GoodsCategoryRepository</a> - <a href="com/example/springboot2/repository/package-summary.html">com.example.springboot2.repository</a>中的接口</dt>
<dd>
<div class="block">商品分类Repository</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsCategoryService.html" class="type-name-link" title="com.example.springboot2.service中的类">GoodsCategoryService</a> - <a href="com/example/springboot2/service/package-summary.html">com.example.springboot2.service</a>中的类</dt>
<dd>
<div class="block">商品分类服务类</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsCategoryService.html#%3Cinit%3E()" class="member-name-link">GoodsCategoryService()</a> - 类的构造器 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsCategoryService.html" title="com.example.springboot2.service中的类">GoodsCategoryService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/GoodsController.html" class="type-name-link" title="com.example.springboot2.controller中的类">GoodsController</a> - <a href="com/example/springboot2/controller/package-summary.html">com.example.springboot2.controller</a>中的类</dt>
<dd>
<div class="block">商品控制器</div>
</dd>
<dt><a href="com/example/springboot2/controller/GoodsController.html#%3Cinit%3E()" class="member-name-link">GoodsController()</a> - 类的构造器 com.example.springboot2.controller.<a href="com/example/springboot2/controller/GoodsController.html" title="com.example.springboot2.controller中的类">GoodsController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/repository/GoodsRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">GoodsRepository</a> - <a href="com/example/springboot2/repository/package-summary.html">com.example.springboot2.repository</a>中的接口</dt>
<dd>
<div class="block">商品Repository</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsService.html" class="type-name-link" title="com.example.springboot2.service中的类">GoodsService</a> - <a href="com/example/springboot2/service/package-summary.html">com.example.springboot2.service</a>中的类</dt>
<dd>
<div class="block">商品服务类</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsService.html#%3Cinit%3E()" class="member-name-link">GoodsService()</a> - 类的构造器 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsService.html" title="com.example.springboot2.service中的类">GoodsService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/GoodsService.GoodsStats.html" class="type-name-link" title="com.example.springboot2.service中的类">GoodsService.GoodsStats</a> - <a href="com/example/springboot2/service/package-summary.html">com.example.springboot2.service</a>中的类</dt>
<dd>
<div class="block">商品统计数据DTO</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsService.GoodsStats.html#%3Cinit%3E()" class="member-name-link">GoodsStats()</a> - 类的构造器 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsService.GoodsStats.html" title="com.example.springboot2.service中的类">GoodsService.GoodsStats</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:H">H</h2>
<dl class="index">
<dt><a href="com/example/springboot2/exception/GlobalExceptionHandler.html#handleAccessDeniedException(org.springframework.security.access.AccessDeniedException)" class="member-name-link">handleAccessDeniedException(AccessDeniedException)</a> - 类中的方法 com.example.springboot2.exception.<a href="com/example/springboot2/exception/GlobalExceptionHandler.html" title="com.example.springboot2.exception中的类">GlobalExceptionHandler</a></dt>
<dd>
<div class="block">处理授权异常</div>
</dd>
<dt><a href="com/example/springboot2/exception/GlobalExceptionHandler.html#handleAuthenticationException(org.springframework.security.core.AuthenticationException)" class="member-name-link">handleAuthenticationException(AuthenticationException)</a> - 类中的方法 com.example.springboot2.exception.<a href="com/example/springboot2/exception/GlobalExceptionHandler.html" title="com.example.springboot2.exception中的类">GlobalExceptionHandler</a></dt>
<dd>
<div class="block">处理认证异常</div>
</dd>
<dt><a href="com/example/springboot2/exception/GlobalExceptionHandler.html#handleBindException(org.springframework.validation.BindException)" class="member-name-link">handleBindException(BindException)</a> - 类中的方法 com.example.springboot2.exception.<a href="com/example/springboot2/exception/GlobalExceptionHandler.html" title="com.example.springboot2.exception中的类">GlobalExceptionHandler</a></dt>
<dd>
<div class="block">处理绑定异常</div>
</dd>
<dt><a href="com/example/springboot2/exception/GlobalExceptionHandler.html#handleBusinessException(com.example.springboot2.exception.BusinessException)" class="member-name-link">handleBusinessException(BusinessException)</a> - 类中的方法 com.example.springboot2.exception.<a href="com/example/springboot2/exception/GlobalExceptionHandler.html" title="com.example.springboot2.exception中的类">GlobalExceptionHandler</a></dt>
<dd>
<div class="block">处理业务异常</div>
</dd>
<dt><a href="com/example/springboot2/exception/GlobalExceptionHandler.html#handleConstraintViolationException(jakarta.validation.ConstraintViolationException)" class="member-name-link">handleConstraintViolationException(ConstraintViolationException)</a> - 类中的方法 com.example.springboot2.exception.<a href="com/example/springboot2/exception/GlobalExceptionHandler.html" title="com.example.springboot2.exception中的类">GlobalExceptionHandler</a></dt>
<dd>
<div class="block">处理约束违反异常</div>
</dd>
<dt><a href="com/example/springboot2/exception/GlobalExceptionHandler.html#handleException(java.lang.Exception)" class="member-name-link">handleException(Exception)</a> - 类中的方法 com.example.springboot2.exception.<a href="com/example/springboot2/exception/GlobalExceptionHandler.html" title="com.example.springboot2.exception中的类">GlobalExceptionHandler</a></dt>
<dd>
<div class="block">处理其他异常</div>
</dd>
<dt><a href="com/example/springboot2/exception/GlobalExceptionHandler.html#handleMethodArgumentNotValidException(org.springframework.web.bind.MethodArgumentNotValidException)" class="member-name-link">handleMethodArgumentNotValidException(MethodArgumentNotValidException)</a> - 类中的方法 com.example.springboot2.exception.<a href="com/example/springboot2/exception/GlobalExceptionHandler.html" title="com.example.springboot2.exception中的类">GlobalExceptionHandler</a></dt>
<dd>
<div class="block">处理参数校验异常</div>
</dd>
<dt><a href="com/example/springboot2/controller/SimpleAuthController.html#health()" class="member-name-link">health()</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/SimpleAuthController.html" title="com.example.springboot2.controller中的类">SimpleAuthController</a></dt>
<dd>
<div class="block">健康检查接口</div>
</dd>
</dl>
<h2 class="title" id="I:I">I</h2>
<dl class="index">
<dt><a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html#IN_PROCESS" class="member-name-link">IN_PROCESS</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Merchant.MerchantStatus.html#INACTIVE" class="member-name-link">INACTIVE</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Merchant.MerchantStatus.html" title="enum class in com.example.springboot2.entity">Merchant.MerchantStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/User.UserStatus.html#INACTIVE" class="member-name-link">INACTIVE</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/User.UserStatus.html" title="enum class in com.example.springboot2.entity">User.UserStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Coupon.CouponStatus.html#INVALID" class="member-name-link">INVALID</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Coupon.CouponStatus.html" title="enum class in com.example.springboot2.entity">Coupon.CouponStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/LaundryService.ServiceType.html#IRONING" class="member-name-link">IRONING</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryService.ServiceType.html" title="enum class in com.example.springboot2.entity">LaundryService.ServiceType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/User.html#isAccountNonExpired()" class="member-name-link">isAccountNonExpired()</a> - 类中的方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/User.html" title="com.example.springboot2.entity中的类">User</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/User.html#isAccountNonLocked()" class="member-name-link">isAccountNonLocked()</a> - 类中的方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/User.html" title="com.example.springboot2.entity中的类">User</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/User.html#isCredentialsNonExpired()" class="member-name-link">isCredentialsNonExpired()</a> - 类中的方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/User.html" title="com.example.springboot2.entity中的类">User</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/User.html#isEnabled()" class="member-name-link">isEnabled()</a> - 类中的方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/User.html" title="com.example.springboot2.entity中的类">User</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/util/JwtUtil.html#isTokenExpired(java.lang.String)" class="member-name-link">isTokenExpired(String)</a> - 类中的方法 com.example.springboot2.util.<a href="com/example/springboot2/util/JwtUtil.html" title="com.example.springboot2.util中的类">JwtUtil</a></dt>
<dd>
<div class="block">检查token是否过期</div>
</dd>
</dl>
<h2 class="title" id="I:J">J</h2>
<dl class="index">
<dt><a href="com/example/springboot2/config/JpaConfig.html" class="type-name-link" title="com.example.springboot2.config中的类">JpaConfig</a> - <a href="com/example/springboot2/config/package-summary.html">com.example.springboot2.config</a>中的类</dt>
<dd>
<div class="block">JPA配置</div>
</dd>
<dt><a href="com/example/springboot2/config/JpaConfig.html#%3Cinit%3E()" class="member-name-link">JpaConfig()</a> - 类的构造器 com.example.springboot2.config.<a href="com/example/springboot2/config/JpaConfig.html" title="com.example.springboot2.config中的类">JpaConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/security/JwtAuthenticationEntryPoint.html" class="type-name-link" title="com.example.springboot2.security中的类">JwtAuthenticationEntryPoint</a> - <a href="com/example/springboot2/security/package-summary.html">com.example.springboot2.security</a>中的类</dt>
<dd>
<div class="block">JWT认证入口点</div>
</dd>
<dt><a href="com/example/springboot2/security/JwtAuthenticationEntryPoint.html#%3Cinit%3E()" class="member-name-link">JwtAuthenticationEntryPoint()</a> - 类的构造器 com.example.springboot2.security.<a href="com/example/springboot2/security/JwtAuthenticationEntryPoint.html" title="com.example.springboot2.security中的类">JwtAuthenticationEntryPoint</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/security/JwtAuthenticationFilter.html" class="type-name-link" title="com.example.springboot2.security中的类">JwtAuthenticationFilter</a> - <a href="com/example/springboot2/security/package-summary.html">com.example.springboot2.security</a>中的类</dt>
<dd>
<div class="block">JWT认证过滤器</div>
</dd>
<dt><a href="com/example/springboot2/security/JwtAuthenticationFilter.html#%3Cinit%3E()" class="member-name-link">JwtAuthenticationFilter()</a> - 类的构造器 com.example.springboot2.security.<a href="com/example/springboot2/security/JwtAuthenticationFilter.html" title="com.example.springboot2.security中的类">JwtAuthenticationFilter</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/util/JwtUtil.html" class="type-name-link" title="com.example.springboot2.util中的类">JwtUtil</a> - <a href="com/example/springboot2/util/package-summary.html">com.example.springboot2.util</a>中的类</dt>
<dd>
<div class="block">JWT工具类</div>
</dd>
<dt><a href="com/example/springboot2/util/JwtUtil.html#%3Cinit%3E()" class="member-name-link">JwtUtil()</a> - 类的构造器 com.example.springboot2.util.<a href="com/example/springboot2/util/JwtUtil.html" title="com.example.springboot2.util中的类">JwtUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/util/JwtUtil.ClaimsResolver.html" class="type-name-link" title="com.example.springboot2.util中的接口">JwtUtil.ClaimsResolver</a>&lt;<a href="com/example/springboot2/util/JwtUtil.ClaimsResolver.html" title="JwtUtil.ClaimsResolver中的类型参数">T</a>&gt; - <a href="com/example/springboot2/util/package-summary.html">com.example.springboot2.util</a>中的接口</dt>
<dd>
<div class="block">声明解析器接口</div>
</dd>
</dl>
<h2 class="title" id="I:L">L</h2>
<dl class="index">
<dt><a href="com/example/springboot2/entity/Order.OrderType.html#LAUNDRY" class="member-name-link">LAUNDRY</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Order.OrderType.html" title="enum class in com.example.springboot2.entity">Order.OrderType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/LaundryBusinessService.html" class="type-name-link" title="com.example.springboot2.service中的类">LaundryBusinessService</a> - <a href="com/example/springboot2/service/package-summary.html">com.example.springboot2.service</a>中的类</dt>
<dd>
<div class="block">洗护业务服务类</div>
</dd>
<dt><a href="com/example/springboot2/service/LaundryBusinessService.html#%3Cinit%3E()" class="member-name-link">LaundryBusinessService()</a> - 类的构造器 com.example.springboot2.service.<a href="com/example/springboot2/service/LaundryBusinessService.html" title="com.example.springboot2.service中的类">LaundryBusinessService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/LaundryController.html" class="type-name-link" title="com.example.springboot2.controller中的类">LaundryController</a> - <a href="com/example/springboot2/controller/package-summary.html">com.example.springboot2.controller</a>中的类</dt>
<dd>
<div class="block">洗护业务控制器</div>
</dd>
<dt><a href="com/example/springboot2/controller/LaundryController.html#%3Cinit%3E()" class="member-name-link">LaundryController()</a> - 类的构造器 com.example.springboot2.controller.<a href="com/example/springboot2/controller/LaundryController.html" title="com.example.springboot2.controller中的类">LaundryController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/LaundryOrder.html" class="type-name-link" title="com.example.springboot2.entity中的类">LaundryOrder</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的类</dt>
<dd>
<div class="block">洗护订单实体类</div>
</dd>
<dt><a href="com/example/springboot2/entity/LaundryOrder.html#%3Cinit%3E()" class="member-name-link">LaundryOrder()</a> - 类的构造器 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryOrder.html" title="com.example.springboot2.entity中的类">LaundryOrder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html" class="type-name-link" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的Enum Class</dt>
<dd>
<div class="block">洗护订单状态枚举</div>
</dd>
<dt><a href="com/example/springboot2/entity/LaundryOrderItem.html" class="type-name-link" title="com.example.springboot2.entity中的类">LaundryOrderItem</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的类</dt>
<dd>
<div class="block">洗护订单项实体类</div>
</dd>
<dt><a href="com/example/springboot2/entity/LaundryOrderItem.html#%3Cinit%3E()" class="member-name-link">LaundryOrderItem()</a> - 类的构造器 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryOrderItem.html" title="com.example.springboot2.entity中的类">LaundryOrderItem</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/repository/LaundryOrderRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">LaundryOrderRepository</a> - <a href="com/example/springboot2/repository/package-summary.html">com.example.springboot2.repository</a>中的接口</dt>
<dd>
<div class="block">洗护订单Repository</div>
</dd>
<dt><a href="com/example/springboot2/entity/LaundryService.html" class="type-name-link" title="com.example.springboot2.entity中的类">LaundryService</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的类</dt>
<dd>
<div class="block">洗护服务实体类</div>
</dd>
<dt><a href="com/example/springboot2/entity/LaundryService.html#%3Cinit%3E()" class="member-name-link">LaundryService()</a> - 类的构造器 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryService.html" title="com.example.springboot2.entity中的类">LaundryService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/LaundryService.ServiceType.html" class="type-name-link" title="enum class in com.example.springboot2.entity">LaundryService.ServiceType</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的Enum Class</dt>
<dd>
<div class="block">洗护服务类型枚举</div>
</dd>
<dt><a href="com/example/springboot2/repository/LaundryServiceRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">LaundryServiceRepository</a> - <a href="com/example/springboot2/repository/package-summary.html">com.example.springboot2.repository</a>中的接口</dt>
<dd>
<div class="block">洗护服务Repository</div>
</dd>
<dt><a href="com/example/springboot2/service/UserService.html#loadUserByUsername(java.lang.String)" class="member-name-link">loadUserByUsername(String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/UserService.html" title="com.example.springboot2.service中的类">UserService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/User.UserStatus.html#LOCKED" class="member-name-link">LOCKED</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/User.UserStatus.html" title="enum class in com.example.springboot2.entity">User.UserStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/AuthController.html#login(com.example.springboot2.dto.LoginRequest,jakarta.servlet.http.HttpServletRequest)" class="member-name-link">login(LoginRequest, HttpServletRequest)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/AuthController.html" title="com.example.springboot2.controller中的类">AuthController</a></dt>
<dd>
<div class="block">用户登录</div>
</dd>
<dt><a href="com/example/springboot2/service/AuthService.html#login(com.example.springboot2.dto.LoginRequest)" class="member-name-link">login(LoginRequest)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/AuthService.html" title="com.example.springboot2.service中的类">AuthService</a></dt>
<dd>
<div class="block">用户登录</div>
</dd>
<dt><a href="com/example/springboot2/dto/LoginRequest.html" class="type-name-link" title="com.example.springboot2.dto中的类">LoginRequest</a> - <a href="com/example/springboot2/dto/package-summary.html">com.example.springboot2.dto</a>中的类</dt>
<dd>
<div class="block">登录请求DTO</div>
</dd>
<dt><a href="com/example/springboot2/dto/LoginRequest.html#%3Cinit%3E()" class="member-name-link">LoginRequest()</a> - 类的构造器 com.example.springboot2.dto.<a href="com/example/springboot2/dto/LoginRequest.html" title="com.example.springboot2.dto中的类">LoginRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/dto/LoginResponse.html" class="type-name-link" title="com.example.springboot2.dto中的类">LoginResponse</a> - <a href="com/example/springboot2/dto/package-summary.html">com.example.springboot2.dto</a>中的类</dt>
<dd>
<div class="block">登录响应DTO</div>
</dd>
<dt><a href="com/example/springboot2/dto/LoginResponse.html#%3Cinit%3E()" class="member-name-link">LoginResponse()</a> - 类的构造器 com.example.springboot2.dto.<a href="com/example/springboot2/dto/LoginResponse.html" title="com.example.springboot2.dto中的类">LoginResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/dto/LoginResponse.UserInfo.html" class="type-name-link" title="com.example.springboot2.dto中的类">LoginResponse.UserInfo</a> - <a href="com/example/springboot2/dto/package-summary.html">com.example.springboot2.dto</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/AuthController.html#logout()" class="member-name-link">logout()</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/AuthController.html" title="com.example.springboot2.controller中的类">AuthController</a></dt>
<dd>
<div class="block">退出登录</div>
</dd>
</dl>
<h2 class="title" id="I:M">M</h2>
<dl class="index">
<dt><a href="com/example/springboot2/SpringBoot2Application.html#main(java.lang.String%5B%5D)" class="member-name-link">main(String[])</a> - 类中的静态方法 com.example.springboot2.<a href="com/example/springboot2/SpringBoot2Application.html" title="com.example.springboot2中的类">SpringBoot2Application</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Merchant.html" class="type-name-link" title="com.example.springboot2.entity中的类">Merchant</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的类</dt>
<dd>
<div class="block">商家实体类</div>
</dd>
<dt><a href="com/example/springboot2/entity/Merchant.html#%3Cinit%3E()" class="member-name-link">Merchant()</a> - 类的构造器 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Merchant.html" title="com.example.springboot2.entity中的类">Merchant</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/User.UserRole.html#MERCHANT" class="member-name-link">MERCHANT</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/User.UserRole.html" title="enum class in com.example.springboot2.entity">User.UserRole</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Merchant.CertificationStatus.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Merchant.CertificationStatus</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的Enum Class</dt>
<dd>
<div class="block">认证状态枚举</div>
</dd>
<dt><a href="com/example/springboot2/entity/Merchant.MerchantStatus.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Merchant.MerchantStatus</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的Enum Class</dt>
<dd>
<div class="block">商家状态枚举</div>
</dd>
<dt><a href="com/example/springboot2/controller/MerchantController.html" class="type-name-link" title="com.example.springboot2.controller中的类">MerchantController</a> - <a href="com/example/springboot2/controller/package-summary.html">com.example.springboot2.controller</a>中的类</dt>
<dd>
<div class="block">商家控制器</div>
</dd>
<dt><a href="com/example/springboot2/controller/MerchantController.html#%3Cinit%3E()" class="member-name-link">MerchantController()</a> - 类的构造器 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantController.html" title="com.example.springboot2.controller中的类">MerchantController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantFinanceController.html" class="type-name-link" title="com.example.springboot2.controller中的类">MerchantFinanceController</a> - <a href="com/example/springboot2/controller/package-summary.html">com.example.springboot2.controller</a>中的类</dt>
<dd>
<div class="block">商家财务管理控制器</div>
</dd>
<dt><a href="com/example/springboot2/controller/MerchantFinanceController.html#%3Cinit%3E()" class="member-name-link">MerchantFinanceController()</a> - 类的构造器 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html" class="type-name-link" title="com.example.springboot2.service中的类">MerchantFinanceService</a> - <a href="com/example/springboot2/service/package-summary.html">com.example.springboot2.service</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#%3Cinit%3E()" class="member-name-link">MerchantFinanceService()</a> - 类的构造器 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/SimpleAuthController.html#merchantLogin(java.util.Map)" class="member-name-link">merchantLogin(Map&lt;String, Object&gt;)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/SimpleAuthController.html" title="com.example.springboot2.controller中的类">SimpleAuthController</a></dt>
<dd>
<div class="block">商家登录</div>
</dd>
<dt><a href="com/example/springboot2/repository/MerchantRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">MerchantRepository</a> - <a href="com/example/springboot2/repository/package-summary.html">com.example.springboot2.repository</a>中的接口</dt>
<dd>
<div class="block">商家Repository</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantService.html" class="type-name-link" title="com.example.springboot2.service中的类">MerchantService</a> - <a href="com/example/springboot2/service/package-summary.html">com.example.springboot2.service</a>中的类</dt>
<dd>
<div class="block">商家服务类</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantService.html#%3Cinit%3E()" class="member-name-link">MerchantService()</a> - 类的构造器 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantService.html" title="com.example.springboot2.service中的类">MerchantService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantServiceController.html" class="type-name-link" title="com.example.springboot2.controller中的类">MerchantServiceController</a> - <a href="com/example/springboot2/controller/package-summary.html">com.example.springboot2.controller</a>中的类</dt>
<dd>
<div class="block">商家服务管理控制器</div>
</dd>
<dt><a href="com/example/springboot2/controller/MerchantServiceController.html#%3Cinit%3E()" class="member-name-link">MerchantServiceController()</a> - 类的构造器 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantServiceController.html" title="com.example.springboot2.controller中的类">MerchantServiceController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/MerchantServiceManagementService.html" class="type-name-link" title="com.example.springboot2.service中的类">MerchantServiceManagementService</a> - <a href="com/example/springboot2/service/package-summary.html">com.example.springboot2.service</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/MerchantServiceManagementService.html#%3Cinit%3E()" class="member-name-link">MerchantServiceManagementService()</a> - 类的构造器 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantServiceManagementService.html" title="com.example.springboot2.service中的类">MerchantServiceManagementService</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:N">N</h2>
<dl class="index">
<dt><a href="com/example/springboot2/entity/Coupon.CouponStatus.html#NOT_STARTED" class="member-name-link">NOT_STARTED</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Coupon.CouponStatus.html" title="enum class in com.example.springboot2.entity">Coupon.CouponStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/common/Result.html#notFound()" class="member-name-link">notFound()</a> - 类中的静态方法 com.example.springboot2.common.<a href="com/example/springboot2/common/Result.html" title="com.example.springboot2.common中的类">Result</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/common/Result.html#notFound(java.lang.String)" class="member-name-link">notFound(String)</a> - 类中的静态方法 com.example.springboot2.common.<a href="com/example/springboot2/common/Result.html" title="com.example.springboot2.common中的类">Result</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:O">O</h2>
<dl class="index">
<dt><a href="com/example/springboot2/common/PageResult.html#of(java.util.List,java.lang.Long,java.lang.Integer,java.lang.Integer)" class="member-name-link">of(List&lt;T&gt;, Long, Integer, Integer)</a> - 类中的静态方法 com.example.springboot2.common.<a href="com/example/springboot2/common/PageResult.html" title="com.example.springboot2.common中的类">PageResult</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/common/PageResult.html#of(org.springframework.data.domain.Page)" class="member-name-link">of(Page&lt;T&gt;)</a> - 类中的静态方法 com.example.springboot2.common.<a href="com/example/springboot2/common/PageResult.html" title="com.example.springboot2.common中的类">PageResult</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Goods.GoodsStatus.html#OFF_SALE" class="member-name-link">OFF_SALE</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Goods.GoodsStatus.html#ON_SALE" class="member-name-link">ON_SALE</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/BaseEntity.html#onCreate()" class="member-name-link">onCreate()</a> - 类中的方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/BaseEntity.html" title="com.example.springboot2.entity中的类">BaseEntity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Coupon.CouponStatus.html#ONGOING" class="member-name-link">ONGOING</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Coupon.CouponStatus.html" title="enum class in com.example.springboot2.entity">Coupon.CouponStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/BaseEntity.html#onUpdate()" class="member-name-link">onUpdate()</a> - 类中的方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/BaseEntity.html" title="com.example.springboot2.entity中的类">BaseEntity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Order.html" class="type-name-link" title="com.example.springboot2.entity中的类">Order</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的类</dt>
<dd>
<div class="block">订单实体类</div>
</dd>
<dt><a href="com/example/springboot2/entity/Order.html#%3Cinit%3E()" class="member-name-link">Order()</a> - 类的构造器 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Order.html" title="com.example.springboot2.entity中的类">Order</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Order.OrderStatus.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Order.OrderStatus</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的Enum Class</dt>
<dd>
<div class="block">订单状态枚举</div>
</dd>
<dt><a href="com/example/springboot2/entity/Order.OrderType.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Order.OrderType</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的Enum Class</dt>
<dd>
<div class="block">订单类型枚举</div>
</dd>
<dt><a href="com/example/springboot2/controller/OrderController.html" class="type-name-link" title="com.example.springboot2.controller中的类">OrderController</a> - <a href="com/example/springboot2/controller/package-summary.html">com.example.springboot2.controller</a>中的类</dt>
<dd>
<div class="block">订单控制器</div>
</dd>
<dt><a href="com/example/springboot2/controller/OrderController.html#%3Cinit%3E()" class="member-name-link">OrderController()</a> - 类的构造器 com.example.springboot2.controller.<a href="com/example/springboot2/controller/OrderController.html" title="com.example.springboot2.controller中的类">OrderController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/OrderItem.html" class="type-name-link" title="com.example.springboot2.entity中的类">OrderItem</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的类</dt>
<dd>
<div class="block">订单项实体类</div>
</dd>
<dt><a href="com/example/springboot2/entity/OrderItem.html#%3Cinit%3E()" class="member-name-link">OrderItem()</a> - 类的构造器 com.example.springboot2.entity.<a href="com/example/springboot2/entity/OrderItem.html" title="com.example.springboot2.entity中的类">OrderItem</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/repository/OrderRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">OrderRepository</a> - <a href="com/example/springboot2/repository/package-summary.html">com.example.springboot2.repository</a>中的接口</dt>
<dd>
<div class="block">订单Repository</div>
</dd>
<dt><a href="com/example/springboot2/service/OrderService.html" class="type-name-link" title="com.example.springboot2.service中的类">OrderService</a> - <a href="com/example/springboot2/service/package-summary.html">com.example.springboot2.service</a>中的类</dt>
<dd>
<div class="block">订单服务类</div>
</dd>
<dt><a href="com/example/springboot2/service/OrderService.html#%3Cinit%3E()" class="member-name-link">OrderService()</a> - 类的构造器 com.example.springboot2.service.<a href="com/example/springboot2/service/OrderService.html" title="com.example.springboot2.service中的类">OrderService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/OrderService.OrderStats.html" class="type-name-link" title="com.example.springboot2.service中的类">OrderService.OrderStats</a> - <a href="com/example/springboot2/service/package-summary.html">com.example.springboot2.service</a>中的类</dt>
<dd>
<div class="block">订单统计数据DTO</div>
</dd>
<dt><a href="com/example/springboot2/service/OrderService.OrderStats.html#%3Cinit%3E()" class="member-name-link">OrderStats()</a> - 类的构造器 com.example.springboot2.service.<a href="com/example/springboot2/service/OrderService.OrderStats.html" title="com.example.springboot2.service中的类">OrderService.OrderStats</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:P">P</h2>
<dl class="index">
<dt><a href="com/example/springboot2/common/PageResult.html" class="type-name-link" title="com.example.springboot2.common中的类">PageResult</a>&lt;<a href="com/example/springboot2/common/PageResult.html" title="PageResult中的类型参数">T</a>&gt; - <a href="com/example/springboot2/common/package-summary.html">com.example.springboot2.common</a>中的类</dt>
<dd>
<div class="block">分页响应结果类</div>
</dd>
<dt><a href="com/example/springboot2/common/PageResult.html#%3Cinit%3E()" class="member-name-link">PageResult()</a> - 类的构造器 com.example.springboot2.common.<a href="com/example/springboot2/common/PageResult.html" title="com.example.springboot2.common中的类">PageResult</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Order.OrderStatus.html#PAID" class="member-name-link">PAID</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Order.OrderStatus.html" title="enum class in com.example.springboot2.entity">Order.OrderStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/config/PasswordConfig.html" class="type-name-link" title="com.example.springboot2.config中的类">PasswordConfig</a> - <a href="com/example/springboot2/config/package-summary.html">com.example.springboot2.config</a>中的类</dt>
<dd>
<div class="block">密码编码器配置 - 解决循环依赖问题</div>
</dd>
<dt><a href="com/example/springboot2/config/PasswordConfig.html#%3Cinit%3E()" class="member-name-link">PasswordConfig()</a> - 类的构造器 com.example.springboot2.config.<a href="com/example/springboot2/config/PasswordConfig.html" title="com.example.springboot2.config中的类">PasswordConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/config/PasswordConfig.html#passwordEncoder()" class="member-name-link">passwordEncoder()</a> - 类中的方法 com.example.springboot2.config.<a href="com/example/springboot2/config/PasswordConfig.html" title="com.example.springboot2.config中的类">PasswordConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#payDeposit(java.lang.Long,java.util.Map)" class="member-name-link">payDeposit(Long, Map&lt;String, Object&gt;)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantFinanceController.html#payDeposit(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">payDeposit(Map&lt;String, Object&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></dt>
<dd>
<div class="block">缴纳保证金</div>
</dd>
<dt><a href="com/example/springboot2/entity/Coupon.CouponStatus.html#PENDING" class="member-name-link">PENDING</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Coupon.CouponStatus.html" title="enum class in com.example.springboot2.entity">Coupon.CouponStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Goods.GoodsStatus.html#PENDING" class="member-name-link">PENDING</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html#PENDING" class="member-name-link">PENDING</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Merchant.CertificationStatus.html#PENDING" class="member-name-link">PENDING</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Merchant.CertificationStatus.html" title="enum class in com.example.springboot2.entity">Merchant.CertificationStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Order.OrderStatus.html#PENDING" class="member-name-link">PENDING</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Order.OrderStatus.html" title="enum class in com.example.springboot2.entity">Order.OrderStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Goods.GoodsType.html#PHYSICAL" class="member-name-link">PHYSICAL</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Goods.GoodsType.html" title="enum class in com.example.springboot2.entity">Goods.GoodsType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html#PICKED_UP" class="member-name-link">PICKED_UP</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/MerchantServiceManagementService.html#publishService(java.lang.Long,java.util.Map)" class="member-name-link">publishService(Long, Map&lt;String, Object&gt;)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantServiceManagementService.html" title="com.example.springboot2.service中的类">MerchantServiceManagementService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantServiceController.html#publishService(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">publishService(Map&lt;String, Object&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantServiceController.html" title="com.example.springboot2.controller中的类">MerchantServiceController</a></dt>
<dd>
<div class="block">发布新服务</div>
</dd>
</dl>
<h2 class="title" id="I:Q">Q</h2>
<dl class="index">
<dt><a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html#QUALITY_CHECK" class="member-name-link">QUALITY_CHECK</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:R">R</h2>
<dl class="index">
<dt><a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html#READY" class="member-name-link">READY</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/AuthController.html#refreshToken(jakarta.servlet.http.HttpServletRequest)" class="member-name-link">refreshToken(HttpServletRequest)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/AuthController.html" title="com.example.springboot2.controller中的类">AuthController</a></dt>
<dd>
<div class="block">刷新token</div>
</dd>
<dt><a href="com/example/springboot2/service/AuthService.html#refreshToken(java.lang.String)" class="member-name-link">refreshToken(String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/AuthService.html" title="com.example.springboot2.service中的类">AuthService</a></dt>
<dd>
<div class="block">刷新token</div>
</dd>
<dt><a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html#REFUNDED" class="member-name-link">REFUNDED</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Order.OrderStatus.html#REFUNDED" class="member-name-link">REFUNDED</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Order.OrderStatus.html" title="enum class in com.example.springboot2.entity">Order.OrderStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/OrderController.html#refundOrder(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">refundOrder(Long, Map&lt;String, String&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/OrderController.html" title="com.example.springboot2.controller中的类">OrderController</a></dt>
<dd>
<div class="block">申请退款</div>
</dd>
<dt><a href="com/example/springboot2/controller/RegionController.html" class="type-name-link" title="com.example.springboot2.controller中的类">RegionController</a> - <a href="com/example/springboot2/controller/package-summary.html">com.example.springboot2.controller</a>中的类</dt>
<dd>
<div class="block">地区数据控制器</div>
</dd>
<dt><a href="com/example/springboot2/controller/RegionController.html#%3Cinit%3E()" class="member-name-link">RegionController()</a> - 类的构造器 com.example.springboot2.controller.<a href="com/example/springboot2/controller/RegionController.html" title="com.example.springboot2.controller中的类">RegionController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/AuthController.html#register(com.example.springboot2.dto.RegisterRequest)" class="member-name-link">register(RegisterRequest)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/AuthController.html" title="com.example.springboot2.controller中的类">AuthController</a></dt>
<dd>
<div class="block">用户注册</div>
</dd>
<dt><a href="com/example/springboot2/service/AuthService.html#register(com.example.springboot2.dto.RegisterRequest)" class="member-name-link">register(RegisterRequest)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/AuthService.html" title="com.example.springboot2.service中的类">AuthService</a></dt>
<dd>
<div class="block">用户注册</div>
</dd>
<dt><a href="com/example/springboot2/dto/RegisterRequest.html" class="type-name-link" title="com.example.springboot2.dto中的类">RegisterRequest</a> - <a href="com/example/springboot2/dto/package-summary.html">com.example.springboot2.dto</a>中的类</dt>
<dd>
<div class="block">注册请求DTO</div>
</dd>
<dt><a href="com/example/springboot2/dto/RegisterRequest.html#%3Cinit%3E()" class="member-name-link">RegisterRequest()</a> - 类的构造器 com.example.springboot2.dto.<a href="com/example/springboot2/dto/RegisterRequest.html" title="com.example.springboot2.dto中的类">RegisterRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Coupon.CouponStatus.html#REJECTED" class="member-name-link">REJECTED</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Coupon.CouponStatus.html" title="enum class in com.example.springboot2.entity">Coupon.CouponStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Goods.GoodsStatus.html#REJECTED" class="member-name-link">REJECTED</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Merchant.CertificationStatus.html#REJECTED" class="member-name-link">REJECTED</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Merchant.CertificationStatus.html" title="enum class in com.example.springboot2.entity">Merchant.CertificationStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/LaundryBusinessService.html#rejectLaundryOrder(java.lang.Long,java.lang.Long,java.lang.String)" class="member-name-link">rejectLaundryOrder(Long, Long, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/LaundryBusinessService.html" title="com.example.springboot2.service中的类">LaundryBusinessService</a></dt>
<dd>
<div class="block">拒绝订单</div>
</dd>
<dt><a href="com/example/springboot2/controller/LaundryController.html#rejectLaundryOrder(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">rejectLaundryOrder(Long, Map&lt;String, String&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/LaundryController.html" title="com.example.springboot2.controller中的类">LaundryController</a></dt>
<dd>
<div class="block">拒绝订单</div>
</dd>
<dt><a href="com/example/springboot2/entity/LaundryService.ServiceType.html#REPAIR" class="member-name-link">REPAIR</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryService.ServiceType.html" title="enum class in com.example.springboot2.entity">LaundryService.ServiceType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/MerchantServiceManagementService.html#replyToReview(java.lang.Long,java.lang.Long,java.lang.String)" class="member-name-link">replyToReview(Long, Long, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantServiceManagementService.html" title="com.example.springboot2.service中的类">MerchantServiceManagementService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantServiceController.html#replyToReview(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">replyToReview(Long, Map&lt;String, String&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantServiceController.html" title="com.example.springboot2.controller中的类">MerchantServiceController</a></dt>
<dd>
<div class="block">回复评价</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#requestDepositRefund(java.lang.Long,java.lang.String)" class="member-name-link">requestDepositRefund(Long, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantFinanceController.html#requestDepositRefund(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">requestDepositRefund(Map&lt;String, String&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></dt>
<dd>
<div class="block">申请保证金退还</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#requestWithdrawal(java.lang.Long,java.util.Map)" class="member-name-link">requestWithdrawal(Long, Map&lt;String, Object&gt;)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantFinanceController.html#requestWithdrawal(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">requestWithdrawal(Map&lt;String, Object&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></dt>
<dd>
<div class="block">申请提现</div>
</dd>
<dt><a href="com/example/springboot2/service/UserService.html#resetPassword(java.lang.String,java.lang.String)" class="member-name-link">resetPassword(String, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/UserService.html" title="com.example.springboot2.service中的类">UserService</a></dt>
<dd>
<div class="block">重置密码</div>
</dd>
<dt><a href="com/example/springboot2/service/AuthService.html#resetPassword(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">resetPassword(String, String, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/AuthService.html" title="com.example.springboot2.service中的类">AuthService</a></dt>
<dd>
<div class="block">重置密码</div>
</dd>
<dt><a href="com/example/springboot2/controller/AuthController.html#resetPassword(java.util.Map)" class="member-name-link">resetPassword(Map&lt;String, String&gt;)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/AuthController.html" title="com.example.springboot2.controller中的类">AuthController</a></dt>
<dd>
<div class="block">重置密码</div>
</dd>
<dt><a href="com/example/springboot2/util/JwtUtil.ClaimsResolver.html#resolve(io.jsonwebtoken.Claims)" class="member-name-link">resolve(Claims)</a> - 接口中的方法 com.example.springboot2.util.<a href="com/example/springboot2/util/JwtUtil.ClaimsResolver.html" title="com.example.springboot2.util中的接口">JwtUtil.ClaimsResolver</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/common/Result.html" class="type-name-link" title="com.example.springboot2.common中的类">Result</a>&lt;<a href="com/example/springboot2/common/Result.html" title="Result中的类型参数">T</a>&gt; - <a href="com/example/springboot2/common/package-summary.html">com.example.springboot2.common</a>中的类</dt>
<dd>
<div class="block">统一响应结果类</div>
</dd>
<dt><a href="com/example/springboot2/common/Result.html#%3Cinit%3E()" class="member-name-link">Result()</a> - 类的构造器 com.example.springboot2.common.<a href="com/example/springboot2/common/Result.html" title="com.example.springboot2.common中的类">Result</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/config/DataInitializer.html#run(java.lang.String...)" class="member-name-link">run(String...)</a> - 类中的方法 com.example.springboot2.config.<a href="com/example/springboot2/config/DataInitializer.html" title="com.example.springboot2.config中的类">DataInitializer</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:S">S</h2>
<dl class="index">
<dt><a href="com/example/springboot2/config/SecurityConfig.html" class="type-name-link" title="com.example.springboot2.config中的类">SecurityConfig</a> - <a href="com/example/springboot2/config/package-summary.html">com.example.springboot2.config</a>中的类</dt>
<dd>
<div class="block">Spring Security配置</div>
</dd>
<dt><a href="com/example/springboot2/config/SecurityConfig.html#%3Cinit%3E()" class="member-name-link">SecurityConfig()</a> - 类的构造器 com.example.springboot2.config.<a href="com/example/springboot2/config/SecurityConfig.html" title="com.example.springboot2.config中的类">SecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/AuthService.html#sendVerifyCode(java.lang.String)" class="member-name-link">sendVerifyCode(String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/AuthService.html" title="com.example.springboot2.service中的类">AuthService</a></dt>
<dd>
<div class="block">发送验证码</div>
</dd>
<dt><a href="com/example/springboot2/controller/AuthController.html#sendVerifyCode(java.util.Map)" class="member-name-link">sendVerifyCode(Map&lt;String, String&gt;)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/AuthController.html" title="com.example.springboot2.controller中的类">AuthController</a></dt>
<dd>
<div class="block">发送验证码</div>
</dd>
<dt><a href="com/example/springboot2/entity/Goods.GoodsType.html#SERVICE" class="member-name-link">SERVICE</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Goods.GoodsType.html" title="enum class in com.example.springboot2.entity">Goods.GoodsType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#setAutoWithdrawSettings(java.lang.Long,java.util.Map)" class="member-name-link">setAutoWithdrawSettings(Long, Map&lt;String, Object&gt;)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantFinanceController.html#setAutoWithdrawSettings(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">setAutoWithdrawSettings(Map&lt;String, Object&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></dt>
<dd>
<div class="block">设置自动提现</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantFinanceService.html#setDefaultBankCard(java.lang.Long,java.lang.Long)" class="member-name-link">setDefaultBankCard(Long, Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" title="com.example.springboot2.service中的类">MerchantFinanceService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantFinanceController.html#setDefaultBankCard(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">setDefaultBankCard(Long, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantFinanceController.html" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></dt>
<dd>
<div class="block">设置默认银行卡</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantServiceManagementService.html#setServicePricing(java.lang.Long,java.lang.Long,java.util.Map)" class="member-name-link">setServicePricing(Long, Long, Map&lt;String, Object&gt;)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantServiceManagementService.html" title="com.example.springboot2.service中的类">MerchantServiceManagementService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantServiceController.html#setServicePricing(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">setServicePricing(Long, Map&lt;String, Object&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantServiceController.html" title="com.example.springboot2.controller中的类">MerchantServiceController</a></dt>
<dd>
<div class="block">设置服务价格</div>
</dd>
<dt><a href="com/example/springboot2/controller/OrderController.html#shipOrder(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">shipOrder(Long, Map&lt;String, String&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/OrderController.html" title="com.example.springboot2.controller中的类">OrderController</a></dt>
<dd>
<div class="block">发货</div>
</dd>
<dt><a href="com/example/springboot2/entity/Order.OrderStatus.html#SHIPPED" class="member-name-link">SHIPPED</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Order.OrderStatus.html" title="enum class in com.example.springboot2.entity">Order.OrderStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/LaundryService.ServiceType.html#SHOE_CLEANING" class="member-name-link">SHOE_CLEANING</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryService.ServiceType.html" title="enum class in com.example.springboot2.entity">LaundryService.ServiceType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/SimpleAuthController.html" class="type-name-link" title="com.example.springboot2.controller中的类">SimpleAuthController</a> - <a href="com/example/springboot2/controller/package-summary.html">com.example.springboot2.controller</a>中的类</dt>
<dd>
<div class="block">简单认证控制器 - 解决前端登录500错误
 提供基础的登录功能，支持超级管理员</div>
</dd>
<dt><a href="com/example/springboot2/controller/SimpleAuthController.html#%3Cinit%3E()" class="member-name-link">SimpleAuthController()</a> - 类的构造器 com.example.springboot2.controller.<a href="com/example/springboot2/controller/SimpleAuthController.html" title="com.example.springboot2.controller中的类">SimpleAuthController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/Goods.GoodsStatus.html#SOLD_OUT" class="member-name-link">SOLD_OUT</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/SpringBoot2Application.html" class="type-name-link" title="com.example.springboot2中的类">SpringBoot2Application</a> - <a href="com/example/springboot2/package-summary.html">com.example.springboot2</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/SpringBoot2Application.html#%3Cinit%3E()" class="member-name-link">SpringBoot2Application()</a> - 类的构造器 com.example.springboot2.<a href="com/example/springboot2/SpringBoot2Application.html" title="com.example.springboot2中的类">SpringBoot2Application</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/LaundryService.ServiceType.html#STAIN_REMOVAL" class="member-name-link">STAIN_REMOVAL</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryService.ServiceType.html" title="enum class in com.example.springboot2.entity">LaundryService.ServiceType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/MerchantService.html#submitCertification(java.lang.Long,java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">submitCertification(Long, String, String, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantService.html" title="com.example.springboot2.service中的类">MerchantService</a></dt>
<dd>
<div class="block">提交商家认证</div>
</dd>
<dt><a href="com/example/springboot2/controller/MerchantController.html#submitCertification(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">submitCertification(Map&lt;String, String&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantController.html" title="com.example.springboot2.controller中的类">MerchantController</a></dt>
<dd>
<div class="block">提交商家认证</div>
</dd>
<dt><a href="com/example/springboot2/common/Result.html#success()" class="member-name-link">success()</a> - 类中的静态方法 com.example.springboot2.common.<a href="com/example/springboot2/common/Result.html" title="com.example.springboot2.common中的类">Result</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/common/Result.html#success(java.lang.String,T)" class="member-name-link">success(String, T)</a> - 类中的静态方法 com.example.springboot2.common.<a href="com/example/springboot2/common/Result.html" title="com.example.springboot2.common中的类">Result</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/common/Result.html#success(T)" class="member-name-link">success(T)</a> - 类中的静态方法 com.example.springboot2.common.<a href="com/example/springboot2/common/Result.html" title="com.example.springboot2.common中的类">Result</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/repository/LaundryOrderRepository.html#sumActualAmountByMerchantAndCreatedTimeBetween(com.example.springboot2.entity.Merchant,java.time.LocalDateTime,java.time.LocalDateTime)" class="member-name-link">sumActualAmountByMerchantAndCreatedTimeBetween(Merchant, LocalDateTime, LocalDateTime)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryOrderRepository.html" title="com.example.springboot2.repository中的接口">LaundryOrderRepository</a></dt>
<dd>
<div class="block">统计商家指定时间范围内的销售额</div>
</dd>
<dt><a href="com/example/springboot2/repository/OrderRepository.html#sumActualAmountByMerchantAndCreatedTimeBetween(com.example.springboot2.entity.Merchant,java.time.LocalDateTime,java.time.LocalDateTime)" class="member-name-link">sumActualAmountByMerchantAndCreatedTimeBetween(Merchant, LocalDateTime, LocalDateTime)</a> - 接口中的方法 com.example.springboot2.repository.<a href="com/example/springboot2/repository/OrderRepository.html" title="com.example.springboot2.repository中的接口">OrderRepository</a></dt>
<dd>
<div class="block">统计商家指定时间范围内的销售额</div>
</dd>
<dt><a href="com/example/springboot2/entity/Merchant.MerchantStatus.html#SUSPENDED" class="member-name-link">SUSPENDED</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Merchant.MerchantStatus.html" title="enum class in com.example.springboot2.entity">Merchant.MerchantStatus</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:T">T</h2>
<dl class="index">
<dt><a href="com/example/springboot2/service/MerchantServiceManagementService.html#toggleServiceStatus(java.lang.Long,java.lang.Long)" class="member-name-link">toggleServiceStatus(Long, Long)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantServiceManagementService.html" title="com.example.springboot2.service中的类">MerchantServiceManagementService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantServiceController.html#toggleServiceStatus(java.lang.Long,org.springframework.security.core.Authentication)" class="member-name-link">toggleServiceStatus(Long, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantServiceController.html" title="com.example.springboot2.controller中的类">MerchantServiceController</a></dt>
<dd>
<div class="block">上架/下架服务</div>
</dd>
</dl>
<h2 class="title" id="I:U">U</h2>
<dl class="index">
<dt><a href="com/example/springboot2/common/Result.html#unauthorized()" class="member-name-link">unauthorized()</a> - 类中的静态方法 com.example.springboot2.common.<a href="com/example/springboot2/common/Result.html" title="com.example.springboot2.common中的类">Result</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/common/Result.html#unauthorized(java.lang.String)" class="member-name-link">unauthorized(String)</a> - 类中的静态方法 com.example.springboot2.common.<a href="com/example/springboot2/common/Result.html" title="com.example.springboot2.common中的类">Result</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/service/GoodsCategoryService.html#updateCategoryGoodsCount(java.lang.Long,int)" class="member-name-link">updateCategoryGoodsCount(Long, int)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsCategoryService.html" title="com.example.springboot2.service中的类">GoodsCategoryService</a></dt>
<dd>
<div class="block">更新分类商品数量</div>
</dd>
<dt><a href="com/example/springboot2/controller/CouponController.html#updateCoupon(java.lang.Long,com.example.springboot2.entity.Coupon,org.springframework.security.core.Authentication)" class="member-name-link">updateCoupon(Long, Coupon, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/CouponController.html" title="com.example.springboot2.controller中的类">CouponController</a></dt>
<dd>
<div class="block">更新优惠券</div>
</dd>
<dt><a href="com/example/springboot2/service/CouponService.html#updateCoupon(java.lang.Long,java.lang.Long,com.example.springboot2.entity.Coupon)" class="member-name-link">updateCoupon(Long, Long, Coupon)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/CouponService.html" title="com.example.springboot2.service中的类">CouponService</a></dt>
<dd>
<div class="block">更新优惠券</div>
</dd>
<dt><a href="com/example/springboot2/service/CouponService.html#updateCouponStatus(java.lang.Long,java.lang.Long,com.example.springboot2.entity.Coupon.CouponStatus)" class="member-name-link">updateCouponStatus(Long, Long, Coupon.CouponStatus)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/CouponService.html" title="com.example.springboot2.service中的类">CouponService</a></dt>
<dd>
<div class="block">更新优惠券状态</div>
</dd>
<dt><a href="com/example/springboot2/controller/CouponController.html#updateCouponStatus(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">updateCouponStatus(Long, Map&lt;String, String&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/CouponController.html" title="com.example.springboot2.controller中的类">CouponController</a></dt>
<dd>
<div class="block">更新优惠券状态</div>
</dd>
<dt><a href="com/example/springboot2/controller/MerchantController.html#updateEmail(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">updateEmail(Map&lt;String, String&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantController.html" title="com.example.springboot2.controller中的类">MerchantController</a></dt>
<dd>
<div class="block">修改邮箱</div>
</dd>
<dt><a href="com/example/springboot2/controller/GoodsController.html#updateGoods(java.lang.Long,com.example.springboot2.entity.Goods,org.springframework.security.core.Authentication)" class="member-name-link">updateGoods(Long, Goods, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/GoodsController.html" title="com.example.springboot2.controller中的类">GoodsController</a></dt>
<dd>
<div class="block">更新商品</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsService.html#updateGoods(java.lang.Long,java.lang.Long,com.example.springboot2.entity.Goods)" class="member-name-link">updateGoods(Long, Long, Goods)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsService.html" title="com.example.springboot2.service中的类">GoodsService</a></dt>
<dd>
<div class="block">更新商品</div>
</dd>
<dt><a href="com/example/springboot2/controller/GoodsCategoryController.html#updateGoodsCategory(java.lang.Long,com.example.springboot2.entity.GoodsCategory,org.springframework.security.core.Authentication)" class="member-name-link">updateGoodsCategory(Long, GoodsCategory, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/GoodsCategoryController.html" title="com.example.springboot2.controller中的类">GoodsCategoryController</a></dt>
<dd>
<div class="block">更新商品分类</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsCategoryService.html#updateGoodsCategory(java.lang.Long,java.lang.Long,com.example.springboot2.entity.GoodsCategory)" class="member-name-link">updateGoodsCategory(Long, Long, GoodsCategory)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsCategoryService.html" title="com.example.springboot2.service中的类">GoodsCategoryService</a></dt>
<dd>
<div class="block">更新商品分类</div>
</dd>
<dt><a href="com/example/springboot2/service/GoodsService.html#updateGoodsStatus(java.lang.Long,java.lang.Long,com.example.springboot2.entity.Goods.GoodsStatus)" class="member-name-link">updateGoodsStatus(Long, Long, Goods.GoodsStatus)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsService.html" title="com.example.springboot2.service中的类">GoodsService</a></dt>
<dd>
<div class="block">更新商品状态</div>
</dd>
<dt><a href="com/example/springboot2/controller/GoodsController.html#updateGoodsStatus(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">updateGoodsStatus(Long, Map&lt;String, String&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/GoodsController.html" title="com.example.springboot2.controller中的类">GoodsController</a></dt>
<dd>
<div class="block">更新商品状态</div>
</dd>
<dt><a href="com/example/springboot2/service/LaundryBusinessService.html#updateLaundryOrderStatus(java.lang.Long,java.lang.Long,com.example.springboot2.entity.LaundryOrder.LaundryOrderStatus,java.lang.String)" class="member-name-link">updateLaundryOrderStatus(Long, Long, LaundryOrder.LaundryOrderStatus, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/LaundryBusinessService.html" title="com.example.springboot2.service中的类">LaundryBusinessService</a></dt>
<dd>
<div class="block">更新洗护订单状态</div>
</dd>
<dt><a href="com/example/springboot2/controller/LaundryController.html#updateLaundryOrderStatus(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">updateLaundryOrderStatus(Long, Map&lt;String, String&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/LaundryController.html" title="com.example.springboot2.controller中的类">LaundryController</a></dt>
<dd>
<div class="block">更新洗护订单状态</div>
</dd>
<dt><a href="com/example/springboot2/controller/LaundryController.html#updateLaundryService(java.lang.Long,com.example.springboot2.entity.LaundryService,org.springframework.security.core.Authentication)" class="member-name-link">updateLaundryService(Long, LaundryService, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/LaundryController.html" title="com.example.springboot2.controller中的类">LaundryController</a></dt>
<dd>
<div class="block">更新洗护服务</div>
</dd>
<dt><a href="com/example/springboot2/service/LaundryBusinessService.html#updateLaundryService(java.lang.Long,java.lang.Long,com.example.springboot2.entity.LaundryService)" class="member-name-link">updateLaundryService(Long, Long, LaundryService)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/LaundryBusinessService.html" title="com.example.springboot2.service中的类">LaundryBusinessService</a></dt>
<dd>
<div class="block">更新洗护服务</div>
</dd>
<dt><a href="com/example/springboot2/service/UserService.html#updateLoginInfo(java.lang.Long,java.lang.String)" class="member-name-link">updateLoginInfo(Long, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/UserService.html" title="com.example.springboot2.service中的类">UserService</a></dt>
<dd>
<div class="block">更新登录信息</div>
</dd>
<dt><a href="com/example/springboot2/controller/MerchantController.html#updateMerchantInfo(com.example.springboot2.entity.Merchant,org.springframework.security.core.Authentication)" class="member-name-link">updateMerchantInfo(Merchant, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantController.html" title="com.example.springboot2.controller中的类">MerchantController</a></dt>
<dd>
<div class="block">更新商家信息</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantService.html#updateMerchantInfo(java.lang.Long,com.example.springboot2.entity.Merchant)" class="member-name-link">updateMerchantInfo(Long, Merchant)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantService.html" title="com.example.springboot2.service中的类">MerchantService</a></dt>
<dd>
<div class="block">更新商家信息</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantService.html#updateMerchantStatus(java.lang.Long,com.example.springboot2.entity.Merchant.MerchantStatus)" class="member-name-link">updateMerchantStatus(Long, Merchant.MerchantStatus)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantService.html" title="com.example.springboot2.service中的类">MerchantService</a></dt>
<dd>
<div class="block">更新商家状态</div>
</dd>
<dt><a href="com/example/springboot2/service/OrderService.html#updateOrderStatus(java.lang.Long,java.lang.Long,com.example.springboot2.entity.Order.OrderStatus,java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">updateOrderStatus(Long, Long, Order.OrderStatus, String, String, String)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/OrderService.html" title="com.example.springboot2.service中的类">OrderService</a></dt>
<dd>
<div class="block">更新订单状态</div>
</dd>
<dt><a href="com/example/springboot2/controller/OrderController.html#updateOrderStatus(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">updateOrderStatus(Long, Map&lt;String, String&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/OrderController.html" title="com.example.springboot2.controller中的类">OrderController</a></dt>
<dd>
<div class="block">更新订单状态</div>
</dd>
<dt><a href="com/example/springboot2/controller/MerchantController.html#updatePhone(java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">updatePhone(Map&lt;String, String&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantController.html" title="com.example.springboot2.controller中的类">MerchantController</a></dt>
<dd>
<div class="block">修改手机号</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantServiceManagementService.html#updateService(java.lang.Long,java.lang.Long,java.util.Map)" class="member-name-link">updateService(Long, Long, Map&lt;String, Object&gt;)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantServiceManagementService.html" title="com.example.springboot2.service中的类">MerchantServiceManagementService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantServiceController.html#updateService(java.lang.Long,java.util.Map,org.springframework.security.core.Authentication)" class="member-name-link">updateService(Long, Map&lt;String, Object&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantServiceController.html" title="com.example.springboot2.controller中的类">MerchantServiceController</a></dt>
<dd>
<div class="block">更新服务信息</div>
</dd>
<dt><a href="com/example/springboot2/service/UserService.html#updateUser(com.example.springboot2.entity.User)" class="member-name-link">updateUser(User)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/UserService.html" title="com.example.springboot2.service中的类">UserService</a></dt>
<dd>
<div class="block">更新用户信息</div>
</dd>
<dt><a href="com/example/springboot2/controller/FileController.html#uploadFile(org.springframework.web.multipart.MultipartFile)" class="member-name-link">uploadFile(MultipartFile)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/FileController.html" title="com.example.springboot2.controller中的类">FileController</a></dt>
<dd>
<div class="block">文件上传</div>
</dd>
<dt><a href="com/example/springboot2/controller/FileController.html#uploadFiles(org.springframework.web.multipart.MultipartFile%5B%5D)" class="member-name-link">uploadFiles(MultipartFile[])</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/FileController.html" title="com.example.springboot2.controller中的类">FileController</a></dt>
<dd>
<div class="block">批量文件上传</div>
</dd>
<dt><a href="com/example/springboot2/service/MerchantServiceManagementService.html#uploadServiceImages(java.lang.Long,java.util.List)" class="member-name-link">uploadServiceImages(Long, List&lt;MultipartFile&gt;)</a> - 类中的方法 com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantServiceManagementService.html" title="com.example.springboot2.service中的类">MerchantServiceManagementService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/MerchantServiceController.html#uploadServiceImages(java.util.List,org.springframework.security.core.Authentication)" class="member-name-link">uploadServiceImages(List&lt;MultipartFile&gt;, Authentication)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantServiceController.html" title="com.example.springboot2.controller中的类">MerchantServiceController</a></dt>
<dd>
<div class="block">上传服务图片</div>
</dd>
<dt><a href="com/example/springboot2/entity/User.html" class="type-name-link" title="com.example.springboot2.entity中的类">User</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的类</dt>
<dd>
<div class="block">用户实体类</div>
</dd>
<dt><a href="com/example/springboot2/entity/User.html#%3Cinit%3E()" class="member-name-link">User()</a> - 类的构造器 com.example.springboot2.entity.<a href="com/example/springboot2/entity/User.html" title="com.example.springboot2.entity中的类">User</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/User.UserRole.html" class="type-name-link" title="enum class in com.example.springboot2.entity">User.UserRole</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的Enum Class</dt>
<dd>
<div class="block">用户角色枚举</div>
</dd>
<dt><a href="com/example/springboot2/entity/User.UserStatus.html" class="type-name-link" title="enum class in com.example.springboot2.entity">User.UserStatus</a> - <a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a>中的Enum Class</dt>
<dd>
<div class="block">用户状态枚举</div>
</dd>
<dt><a href="com/example/springboot2/dto/LoginResponse.UserInfo.html#%3Cinit%3E()" class="member-name-link">UserInfo()</a> - 类的构造器 com.example.springboot2.dto.<a href="com/example/springboot2/dto/LoginResponse.UserInfo.html" title="com.example.springboot2.dto中的类">LoginResponse.UserInfo</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/controller/SimpleAuthController.html#userLogin(java.util.Map)" class="member-name-link">userLogin(Map&lt;String, Object&gt;)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/SimpleAuthController.html" title="com.example.springboot2.controller中的类">SimpleAuthController</a></dt>
<dd>
<div class="block">用户登录 - 支持密码登录和短信登录</div>
</dd>
<dt><a href="com/example/springboot2/controller/SimpleAuthController.html#userRegister(java.util.Map)" class="member-name-link">userRegister(Map&lt;String, Object&gt;)</a> - 类中的方法 com.example.springboot2.controller.<a href="com/example/springboot2/controller/SimpleAuthController.html" title="com.example.springboot2.controller中的类">SimpleAuthController</a></dt>
<dd>
<div class="block">用户注册</div>
</dd>
<dt><a href="com/example/springboot2/repository/UserRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">UserRepository</a> - <a href="com/example/springboot2/repository/package-summary.html">com.example.springboot2.repository</a>中的接口</dt>
<dd>
<div class="block">用户Repository</div>
</dd>
<dt><a href="com/example/springboot2/service/UserService.html" class="type-name-link" title="com.example.springboot2.service中的类">UserService</a> - <a href="com/example/springboot2/service/package-summary.html">com.example.springboot2.service</a>中的类</dt>
<dd>
<div class="block">用户服务类</div>
</dd>
<dt><a href="com/example/springboot2/service/UserService.html#%3Cinit%3E()" class="member-name-link">UserService()</a> - 类的构造器 com.example.springboot2.service.<a href="com/example/springboot2/service/UserService.html" title="com.example.springboot2.service中的类">UserService</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:V">V</h2>
<dl class="index">
<dt><a href="com/example/springboot2/util/JwtUtil.html#validateToken(java.lang.String,java.lang.String)" class="member-name-link">validateToken(String, String)</a> - 类中的方法 com.example.springboot2.util.<a href="com/example/springboot2/util/JwtUtil.html" title="com.example.springboot2.util中的类">JwtUtil</a></dt>
<dd>
<div class="block">验证token</div>
</dd>
<dt><a href="com/example/springboot2/entity/Coupon.CouponStatus.html#valueOf(java.lang.String)" class="member-name-link">valueOf(String)</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Coupon.CouponStatus.html" title="enum class in com.example.springboot2.entity">Coupon.CouponStatus</a></dt>
<dd>
<div class="block">Returns the enum constant of this class with the specified name.</div>
</dd>
<dt><a href="com/example/springboot2/entity/Coupon.CouponType.html#valueOf(java.lang.String)" class="member-name-link">valueOf(String)</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Coupon.CouponType.html" title="enum class in com.example.springboot2.entity">Coupon.CouponType</a></dt>
<dd>
<div class="block">Returns the enum constant of this class with the specified name.</div>
</dd>
<dt><a href="com/example/springboot2/entity/Goods.GoodsStatus.html#valueOf(java.lang.String)" class="member-name-link">valueOf(String)</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a></dt>
<dd>
<div class="block">Returns the enum constant of this class with the specified name.</div>
</dd>
<dt><a href="com/example/springboot2/entity/Goods.GoodsType.html#valueOf(java.lang.String)" class="member-name-link">valueOf(String)</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Goods.GoodsType.html" title="enum class in com.example.springboot2.entity">Goods.GoodsType</a></dt>
<dd>
<div class="block">Returns the enum constant of this class with the specified name.</div>
</dd>
<dt><a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html#valueOf(java.lang.String)" class="member-name-link">valueOf(String)</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a></dt>
<dd>
<div class="block">Returns the enum constant of this class with the specified name.</div>
</dd>
<dt><a href="com/example/springboot2/entity/LaundryService.ServiceType.html#valueOf(java.lang.String)" class="member-name-link">valueOf(String)</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryService.ServiceType.html" title="enum class in com.example.springboot2.entity">LaundryService.ServiceType</a></dt>
<dd>
<div class="block">Returns the enum constant of this class with the specified name.</div>
</dd>
<dt><a href="com/example/springboot2/entity/Merchant.CertificationStatus.html#valueOf(java.lang.String)" class="member-name-link">valueOf(String)</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Merchant.CertificationStatus.html" title="enum class in com.example.springboot2.entity">Merchant.CertificationStatus</a></dt>
<dd>
<div class="block">Returns the enum constant of this class with the specified name.</div>
</dd>
<dt><a href="com/example/springboot2/entity/Merchant.MerchantStatus.html#valueOf(java.lang.String)" class="member-name-link">valueOf(String)</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Merchant.MerchantStatus.html" title="enum class in com.example.springboot2.entity">Merchant.MerchantStatus</a></dt>
<dd>
<div class="block">Returns the enum constant of this class with the specified name.</div>
</dd>
<dt><a href="com/example/springboot2/entity/Order.OrderStatus.html#valueOf(java.lang.String)" class="member-name-link">valueOf(String)</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Order.OrderStatus.html" title="enum class in com.example.springboot2.entity">Order.OrderStatus</a></dt>
<dd>
<div class="block">Returns the enum constant of this class with the specified name.</div>
</dd>
<dt><a href="com/example/springboot2/entity/Order.OrderType.html#valueOf(java.lang.String)" class="member-name-link">valueOf(String)</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Order.OrderType.html" title="enum class in com.example.springboot2.entity">Order.OrderType</a></dt>
<dd>
<div class="block">Returns the enum constant of this class with the specified name.</div>
</dd>
<dt><a href="com/example/springboot2/entity/User.UserRole.html#valueOf(java.lang.String)" class="member-name-link">valueOf(String)</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/User.UserRole.html" title="enum class in com.example.springboot2.entity">User.UserRole</a></dt>
<dd>
<div class="block">Returns the enum constant of this class with the specified name.</div>
</dd>
<dt><a href="com/example/springboot2/entity/User.UserStatus.html#valueOf(java.lang.String)" class="member-name-link">valueOf(String)</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/User.UserStatus.html" title="enum class in com.example.springboot2.entity">User.UserStatus</a></dt>
<dd>
<div class="block">Returns the enum constant of this class with the specified name.</div>
</dd>
<dt><a href="com/example/springboot2/entity/Coupon.CouponStatus.html#values()" class="member-name-link">values()</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Coupon.CouponStatus.html" title="enum class in com.example.springboot2.entity">Coupon.CouponStatus</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</dd>
<dt><a href="com/example/springboot2/entity/Coupon.CouponType.html#values()" class="member-name-link">values()</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Coupon.CouponType.html" title="enum class in com.example.springboot2.entity">Coupon.CouponType</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</dd>
<dt><a href="com/example/springboot2/entity/Goods.GoodsStatus.html#values()" class="member-name-link">values()</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Goods.GoodsStatus.html" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</dd>
<dt><a href="com/example/springboot2/entity/Goods.GoodsType.html#values()" class="member-name-link">values()</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Goods.GoodsType.html" title="enum class in com.example.springboot2.entity">Goods.GoodsType</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</dd>
<dt><a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html#values()" class="member-name-link">values()</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</dd>
<dt><a href="com/example/springboot2/entity/LaundryService.ServiceType.html#values()" class="member-name-link">values()</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryService.ServiceType.html" title="enum class in com.example.springboot2.entity">LaundryService.ServiceType</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</dd>
<dt><a href="com/example/springboot2/entity/Merchant.CertificationStatus.html#values()" class="member-name-link">values()</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Merchant.CertificationStatus.html" title="enum class in com.example.springboot2.entity">Merchant.CertificationStatus</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</dd>
<dt><a href="com/example/springboot2/entity/Merchant.MerchantStatus.html#values()" class="member-name-link">values()</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Merchant.MerchantStatus.html" title="enum class in com.example.springboot2.entity">Merchant.MerchantStatus</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</dd>
<dt><a href="com/example/springboot2/entity/Order.OrderStatus.html#values()" class="member-name-link">values()</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Order.OrderStatus.html" title="enum class in com.example.springboot2.entity">Order.OrderStatus</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</dd>
<dt><a href="com/example/springboot2/entity/Order.OrderType.html#values()" class="member-name-link">values()</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Order.OrderType.html" title="enum class in com.example.springboot2.entity">Order.OrderType</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</dd>
<dt><a href="com/example/springboot2/entity/User.UserRole.html#values()" class="member-name-link">values()</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/User.UserRole.html" title="enum class in com.example.springboot2.entity">User.UserRole</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</dd>
<dt><a href="com/example/springboot2/entity/User.UserStatus.html#values()" class="member-name-link">values()</a> - enum class中的静态方法 com.example.springboot2.entity.<a href="com/example/springboot2/entity/User.UserStatus.html" title="enum class in com.example.springboot2.entity">User.UserStatus</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</dd>
<dt><a href="com/example/springboot2/entity/Goods.GoodsType.html#VIRTUAL" class="member-name-link">VIRTUAL</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/Goods.GoodsType.html" title="enum class in com.example.springboot2.entity">Goods.GoodsType</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:W">W</h2>
<dl class="index">
<dt><a href="com/example/springboot2/entity/LaundryService.ServiceType.html#WASHING" class="member-name-link">WASHING</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryService.ServiceType.html" title="enum class in com.example.springboot2.entity">LaundryService.ServiceType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/example/springboot2/entity/LaundryService.ServiceType.html#WATERPROOF" class="member-name-link">WATERPROOF</a> - enum class 中的枚举常量 com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryService.ServiceType.html" title="enum class in com.example.springboot2.entity">LaundryService.ServiceType</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="#I:A">A</a>&nbsp;<a href="#I:B">B</a>&nbsp;<a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:E">E</a>&nbsp;<a href="#I:F">F</a>&nbsp;<a href="#I:G">G</a>&nbsp;<a href="#I:H">H</a>&nbsp;<a href="#I:I">I</a>&nbsp;<a href="#I:J">J</a>&nbsp;<a href="#I:L">L</a>&nbsp;<a href="#I:M">M</a>&nbsp;<a href="#I:N">N</a>&nbsp;<a href="#I:O">O</a>&nbsp;<a href="#I:P">P</a>&nbsp;<a href="#I:Q">Q</a>&nbsp;<a href="#I:R">R</a>&nbsp;<a href="#I:S">S</a>&nbsp;<a href="#I:T">T</a>&nbsp;<a href="#I:U">U</a>&nbsp;<a href="#I:V">V</a>&nbsp;<a href="#I:W">W</a>&nbsp;<br><a href="allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="allpackages-index.html">所有程序包</a><span class="vertical-separator">|</span><a href="serialized-form.html">序列化表格</a></main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
