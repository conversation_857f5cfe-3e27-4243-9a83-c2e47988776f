<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed Jun 18 22:33:52 CST 2025 -->
<title>类分层结构 (spring-boot2 0.0.1-SNAPSHOT API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-06-18">
<meta name="description" content="class tree">
<meta name="generator" content="javadoc/TreeWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="tree-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li class="nav-bar-cell1-rev">树</li>
<li><a href="index-all.html">索引</a></li>
<li><a href="help-doc.html#tree">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">所有程序包的分层结构</h1>
<span class="package-hierarchy-label">程序包分层结构:</span>
<ul class="horizontal">
<li><a href="com/example/springboot2/package-tree.html">com.example.springboot2</a>, </li>
<li><a href="com/example/springboot2/common/package-tree.html">com.example.springboot2.common</a>, </li>
<li><a href="com/example/springboot2/config/package-tree.html">com.example.springboot2.config</a>, </li>
<li><a href="com/example/springboot2/controller/package-tree.html">com.example.springboot2.controller</a>, </li>
<li><a href="com/example/springboot2/dto/package-tree.html">com.example.springboot2.dto</a>, </li>
<li><a href="com/example/springboot2/entity/package-tree.html">com.example.springboot2.entity</a>, </li>
<li><a href="com/example/springboot2/exception/package-tree.html">com.example.springboot2.exception</a>, </li>
<li><a href="com/example/springboot2/repository/package-tree.html">com.example.springboot2.repository</a>, </li>
<li><a href="com/example/springboot2/security/package-tree.html">com.example.springboot2.security</a>, </li>
<li><a href="com/example/springboot2/service/package-tree.html">com.example.springboot2.service</a>, </li>
<li><a href="com/example/springboot2/util/package-tree.html">com.example.springboot2.util</a></li>
</ul>
</div>
<section class="hierarchy">
<h2 title="类分层结构">类分层结构</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="java.lang中的类或接口">Object</a>
<ul>
<li class="circle">com.example.springboot2.controller.<a href="com/example/springboot2/controller/AuthController.html" class="type-name-link" title="com.example.springboot2.controller中的类">AuthController</a></li>
<li class="circle">com.example.springboot2.service.<a href="com/example/springboot2/service/AuthService.html" class="type-name-link" title="com.example.springboot2.service中的类">AuthService</a></li>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/BaseEntity.html" class="type-name-link" title="com.example.springboot2.entity中的类">BaseEntity</a>
<ul>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/Coupon.html" class="type-name-link" title="com.example.springboot2.entity中的类">Coupon</a></li>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/Goods.html" class="type-name-link" title="com.example.springboot2.entity中的类">Goods</a></li>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/GoodsCategory.html" class="type-name-link" title="com.example.springboot2.entity中的类">GoodsCategory</a></li>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryOrder.html" class="type-name-link" title="com.example.springboot2.entity中的类">LaundryOrder</a></li>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryOrderItem.html" class="type-name-link" title="com.example.springboot2.entity中的类">LaundryOrderItem</a></li>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryService.html" class="type-name-link" title="com.example.springboot2.entity中的类">LaundryService</a></li>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/Merchant.html" class="type-name-link" title="com.example.springboot2.entity中的类">Merchant</a></li>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/Order.html" class="type-name-link" title="com.example.springboot2.entity中的类">Order</a></li>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/OrderItem.html" class="type-name-link" title="com.example.springboot2.entity中的类">OrderItem</a></li>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/User.html" class="type-name-link" title="com.example.springboot2.entity中的类">User</a> (implements org.springframework.security.core.userdetails.UserDetails)</li>
</ul>
</li>
<li class="circle">com.example.springboot2.config.<a href="com/example/springboot2/config/CorsConfig.html" class="type-name-link" title="com.example.springboot2.config中的类">CorsConfig</a></li>
<li class="circle">com.example.springboot2.controller.<a href="com/example/springboot2/controller/CouponController.html" class="type-name-link" title="com.example.springboot2.controller中的类">CouponController</a></li>
<li class="circle">com.example.springboot2.service.<a href="com/example/springboot2/service/CouponService.html" class="type-name-link" title="com.example.springboot2.service中的类">CouponService</a></li>
<li class="circle">com.example.springboot2.service.<a href="com/example/springboot2/service/CouponService.CouponStats.html" class="type-name-link" title="com.example.springboot2.service中的类">CouponService.CouponStats</a></li>
<li class="circle">com.example.springboot2.controller.<a href="com/example/springboot2/controller/DashboardController.html" class="type-name-link" title="com.example.springboot2.controller中的类">DashboardController</a></li>
<li class="circle">com.example.springboot2.service.<a href="com/example/springboot2/service/DashboardService.html" class="type-name-link" title="com.example.springboot2.service中的类">DashboardService</a></li>
<li class="circle">com.example.springboot2.config.<a href="com/example/springboot2/config/DataInitializer.html" class="type-name-link" title="com.example.springboot2.config中的类">DataInitializer</a> (implements org.springframework.boot.CommandLineRunner)</li>
<li class="circle">com.example.springboot2.controller.<a href="com/example/springboot2/controller/FileController.html" class="type-name-link" title="com.example.springboot2.controller中的类">FileController</a></li>
<li class="circle">org.springframework.web.filter.GenericFilterBean (implements org.springframework.beans.factory.BeanNameAware, org.springframework.beans.factory.DisposableBean, org.springframework.context.EnvironmentAware, org.springframework.core.env.EnvironmentCapable, jakarta.servlet.Filter, org.springframework.beans.factory.InitializingBean, org.springframework.web.context.ServletContextAware)
<ul>
<li class="circle">org.springframework.web.filter.OncePerRequestFilter
<ul>
<li class="circle">com.example.springboot2.security.<a href="com/example/springboot2/security/JwtAuthenticationFilter.html" class="type-name-link" title="com.example.springboot2.security中的类">JwtAuthenticationFilter</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">com.example.springboot2.exception.<a href="com/example/springboot2/exception/GlobalExceptionHandler.html" class="type-name-link" title="com.example.springboot2.exception中的类">GlobalExceptionHandler</a></li>
<li class="circle">com.example.springboot2.controller.<a href="com/example/springboot2/controller/GoodsCategoryController.html" class="type-name-link" title="com.example.springboot2.controller中的类">GoodsCategoryController</a></li>
<li class="circle">com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsCategoryService.html" class="type-name-link" title="com.example.springboot2.service中的类">GoodsCategoryService</a></li>
<li class="circle">com.example.springboot2.controller.<a href="com/example/springboot2/controller/GoodsController.html" class="type-name-link" title="com.example.springboot2.controller中的类">GoodsController</a></li>
<li class="circle">com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsService.html" class="type-name-link" title="com.example.springboot2.service中的类">GoodsService</a></li>
<li class="circle">com.example.springboot2.service.<a href="com/example/springboot2/service/GoodsService.GoodsStats.html" class="type-name-link" title="com.example.springboot2.service中的类">GoodsService.GoodsStats</a></li>
<li class="circle">com.example.springboot2.config.<a href="com/example/springboot2/config/JpaConfig.html" class="type-name-link" title="com.example.springboot2.config中的类">JpaConfig</a></li>
<li class="circle">com.example.springboot2.security.<a href="com/example/springboot2/security/JwtAuthenticationEntryPoint.html" class="type-name-link" title="com.example.springboot2.security中的类">JwtAuthenticationEntryPoint</a> (implements org.springframework.security.web.AuthenticationEntryPoint)</li>
<li class="circle">com.example.springboot2.util.<a href="com/example/springboot2/util/JwtUtil.html" class="type-name-link" title="com.example.springboot2.util中的类">JwtUtil</a></li>
<li class="circle">com.example.springboot2.service.<a href="com/example/springboot2/service/LaundryBusinessService.html" class="type-name-link" title="com.example.springboot2.service中的类">LaundryBusinessService</a></li>
<li class="circle">com.example.springboot2.controller.<a href="com/example/springboot2/controller/LaundryController.html" class="type-name-link" title="com.example.springboot2.controller中的类">LaundryController</a></li>
<li class="circle">com.example.springboot2.dto.<a href="com/example/springboot2/dto/LoginRequest.html" class="type-name-link" title="com.example.springboot2.dto中的类">LoginRequest</a></li>
<li class="circle">com.example.springboot2.dto.<a href="com/example/springboot2/dto/LoginResponse.html" class="type-name-link" title="com.example.springboot2.dto中的类">LoginResponse</a></li>
<li class="circle">com.example.springboot2.dto.<a href="com/example/springboot2/dto/LoginResponse.UserInfo.html" class="type-name-link" title="com.example.springboot2.dto中的类">LoginResponse.UserInfo</a></li>
<li class="circle">com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantController.html" class="type-name-link" title="com.example.springboot2.controller中的类">MerchantController</a></li>
<li class="circle">com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantFinanceController.html" class="type-name-link" title="com.example.springboot2.controller中的类">MerchantFinanceController</a></li>
<li class="circle">com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantFinanceService.html" class="type-name-link" title="com.example.springboot2.service中的类">MerchantFinanceService</a></li>
<li class="circle">com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantService.html" class="type-name-link" title="com.example.springboot2.service中的类">MerchantService</a></li>
<li class="circle">com.example.springboot2.controller.<a href="com/example/springboot2/controller/MerchantServiceController.html" class="type-name-link" title="com.example.springboot2.controller中的类">MerchantServiceController</a></li>
<li class="circle">com.example.springboot2.service.<a href="com/example/springboot2/service/MerchantServiceManagementService.html" class="type-name-link" title="com.example.springboot2.service中的类">MerchantServiceManagementService</a></li>
<li class="circle">com.example.springboot2.controller.<a href="com/example/springboot2/controller/OrderController.html" class="type-name-link" title="com.example.springboot2.controller中的类">OrderController</a></li>
<li class="circle">com.example.springboot2.service.<a href="com/example/springboot2/service/OrderService.html" class="type-name-link" title="com.example.springboot2.service中的类">OrderService</a></li>
<li class="circle">com.example.springboot2.service.<a href="com/example/springboot2/service/OrderService.OrderStats.html" class="type-name-link" title="com.example.springboot2.service中的类">OrderService.OrderStats</a></li>
<li class="circle">com.example.springboot2.common.<a href="com/example/springboot2/common/PageResult.html" class="type-name-link" title="com.example.springboot2.common中的类">PageResult</a>&lt;T&gt;</li>
<li class="circle">com.example.springboot2.config.<a href="com/example/springboot2/config/PasswordConfig.html" class="type-name-link" title="com.example.springboot2.config中的类">PasswordConfig</a></li>
<li class="circle">com.example.springboot2.controller.<a href="com/example/springboot2/controller/RegionController.html" class="type-name-link" title="com.example.springboot2.controller中的类">RegionController</a></li>
<li class="circle">com.example.springboot2.dto.<a href="com/example/springboot2/dto/RegisterRequest.html" class="type-name-link" title="com.example.springboot2.dto中的类">RegisterRequest</a></li>
<li class="circle">com.example.springboot2.common.<a href="com/example/springboot2/common/Result.html" class="type-name-link" title="com.example.springboot2.common中的类">Result</a>&lt;T&gt;</li>
<li class="circle">com.example.springboot2.config.<a href="com/example/springboot2/config/SecurityConfig.html" class="type-name-link" title="com.example.springboot2.config中的类">SecurityConfig</a></li>
<li class="circle">com.example.springboot2.controller.<a href="com/example/springboot2/controller/SimpleAuthController.html" class="type-name-link" title="com.example.springboot2.controller中的类">SimpleAuthController</a></li>
<li class="circle">com.example.springboot2.<a href="com/example/springboot2/SpringBoot2Application.html" class="type-name-link" title="com.example.springboot2中的类">SpringBoot2Application</a></li>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Throwable.html" class="type-name-link external-link" title="java.lang中的类或接口">Throwable</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="java.io中的类或接口" class="external-link">Serializable</a>)
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Exception.html" class="type-name-link external-link" title="java.lang中的类或接口">Exception</a>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/RuntimeException.html" class="type-name-link external-link" title="java.lang中的类或接口">RuntimeException</a>
<ul>
<li class="circle">com.example.springboot2.exception.<a href="com/example/springboot2/exception/BusinessException.html" class="type-name-link" title="com.example.springboot2.exception中的类">BusinessException</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">com.example.springboot2.service.<a href="com/example/springboot2/service/UserService.html" class="type-name-link" title="com.example.springboot2.service中的类">UserService</a> (implements org.springframework.security.core.userdetails.UserDetailsService)</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="接口分层结构">接口分层结构</h2>
<ul>
<li class="circle">org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;
<ul>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/CouponRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">CouponRepository</a> (并 extends org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/GoodsCategoryRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">GoodsCategoryRepository</a> (并 extends org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/GoodsRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">GoodsRepository</a> (并 extends org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryOrderRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">LaundryOrderRepository</a> (并 extends org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryServiceRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">LaundryServiceRepository</a> (并 extends org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/MerchantRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">MerchantRepository</a> (并 extends org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/OrderRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">OrderRepository</a> (并 extends org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/UserRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">UserRepository</a> (并 extends org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt;)</li>
</ul>
</li>
<li class="circle">com.example.springboot2.util.<a href="com/example/springboot2/util/JwtUtil.ClaimsResolver.html" class="type-name-link" title="com.example.springboot2.util中的接口">JwtUtil.ClaimsResolver</a>&lt;T&gt;</li>
<li class="circle">org.springframework.data.repository.query.QueryByExampleExecutor&lt;T&gt;
<ul>
<li class="circle">org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt; (并 extends org.springframework.data.repository.ListCrudRepository&lt;T,<wbr>ID&gt;, org.springframework.data.repository.ListPagingAndSortingRepository&lt;T,<wbr>ID&gt;)
<ul>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/CouponRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">CouponRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/GoodsCategoryRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">GoodsCategoryRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/GoodsRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">GoodsRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryOrderRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">LaundryOrderRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryServiceRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">LaundryServiceRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/MerchantRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">MerchantRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/OrderRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">OrderRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/UserRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">UserRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.springframework.data.repository.Repository&lt;T,<wbr>ID&gt;
<ul>
<li class="circle">org.springframework.data.repository.CrudRepository&lt;T,<wbr>ID&gt;
<ul>
<li class="circle">org.springframework.data.repository.ListCrudRepository&lt;T,<wbr>ID&gt;
<ul>
<li class="circle">org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt; (并 extends org.springframework.data.repository.ListPagingAndSortingRepository&lt;T,<wbr>ID&gt;, org.springframework.data.repository.query.QueryByExampleExecutor&lt;T&gt;)
<ul>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/CouponRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">CouponRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/GoodsCategoryRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">GoodsCategoryRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/GoodsRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">GoodsRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryOrderRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">LaundryOrderRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryServiceRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">LaundryServiceRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/MerchantRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">MerchantRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/OrderRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">OrderRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/UserRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">UserRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.springframework.data.repository.PagingAndSortingRepository&lt;T,<wbr>ID&gt;
<ul>
<li class="circle">org.springframework.data.repository.ListPagingAndSortingRepository&lt;T,<wbr>ID&gt;
<ul>
<li class="circle">org.springframework.data.jpa.repository.JpaRepository&lt;T,<wbr>ID&gt; (并 extends org.springframework.data.repository.ListCrudRepository&lt;T,<wbr>ID&gt;, org.springframework.data.repository.query.QueryByExampleExecutor&lt;T&gt;)
<ul>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/CouponRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">CouponRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/GoodsCategoryRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">GoodsCategoryRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/GoodsRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">GoodsRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryOrderRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">LaundryOrderRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/LaundryServiceRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">LaundryServiceRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/MerchantRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">MerchantRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/OrderRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">OrderRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
<li class="circle">com.example.springboot2.repository.<a href="com/example/springboot2/repository/UserRepository.html" class="type-name-link" title="com.example.springboot2.repository中的接口">UserRepository</a> (并 extends org.springframework.data.jpa.repository.JpaSpecificationExecutor&lt;T&gt;)</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Enum Class Hierarchy">Enum Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="java.lang中的类或接口">Object</a>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html" class="type-name-link external-link" title="java.lang中的类或接口">Enum</a>&lt;E&gt; (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Comparable.html" title="java.lang中的类或接口" class="external-link">Comparable</a>&lt;T&gt;, java.lang.constant.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/constant/Constable.html" title="java.lang.constant中的类或接口" class="external-link">Constable</a>, java.io.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="java.io中的类或接口" class="external-link">Serializable</a>)
<ul>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/Coupon.CouponStatus.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Coupon.CouponStatus</a></li>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/Coupon.CouponType.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Coupon.CouponType</a></li>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/Goods.GoodsStatus.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Goods.GoodsStatus</a></li>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/Goods.GoodsType.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Goods.GoodsType</a></li>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryOrder.LaundryOrderStatus.html" class="type-name-link" title="enum class in com.example.springboot2.entity">LaundryOrder.LaundryOrderStatus</a></li>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/LaundryService.ServiceType.html" class="type-name-link" title="enum class in com.example.springboot2.entity">LaundryService.ServiceType</a></li>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/Merchant.CertificationStatus.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Merchant.CertificationStatus</a></li>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/Merchant.MerchantStatus.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Merchant.MerchantStatus</a></li>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/Order.OrderStatus.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Order.OrderStatus</a></li>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/Order.OrderType.html" class="type-name-link" title="enum class in com.example.springboot2.entity">Order.OrderType</a></li>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/User.UserRole.html" class="type-name-link" title="enum class in com.example.springboot2.entity">User.UserRole</a></li>
<li class="circle">com.example.springboot2.entity.<a href="com/example/springboot2/entity/User.UserStatus.html" class="type-name-link" title="enum class in com.example.springboot2.entity">User.UserStatus</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
