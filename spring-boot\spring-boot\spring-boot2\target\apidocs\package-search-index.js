packageSearchIndex = [{"l":"com.example.springboot2"},{"l":"com.example.springboot2.common"},{"l":"com.example.springboot2.config"},{"l":"com.example.springboot2.controller"},{"l":"com.example.springboot2.dto"},{"l":"com.example.springboot2.entity"},{"l":"com.example.springboot2.exception"},{"l":"com.example.springboot2.repository"},{"l":"com.example.springboot2.security"},{"l":"com.example.springboot2.service"},{"l":"com.example.springboot2.util"},{"l":"所有程序包","u":"allpackages-index.html"}];updateSearchResults();