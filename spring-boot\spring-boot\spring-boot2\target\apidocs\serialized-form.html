<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (17) on Wed Jun 18 22:59:01 CST 2025 -->
<title>序列化表格 (spring-boot2 0.0.1-SNAPSHOT API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-06-18">
<meta name="description" content="serialized forms">
<meta name="generator" content="javadoc/SerializedFormWriterImpl">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="jquery-ui.overrides.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="serialized-form-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="index-all.html">索引</a></li>
<li><a href="help-doc.html#serialized-form">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div class="nav-list-search"><label for="search-input">SEARCH:</label>
<input type="text" id="search-input" value="search" disabled="disabled">
<input type="reset" id="reset-button" value="reset" disabled="disabled">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="序列化表格" class="title">序列化表格</h1>
</div>
<ul class="block-list">
<li>
<section class="serialized-package-container">
<h2 title="程序包">程序包&nbsp;<a href="com/example/springboot2/entity/package-summary.html">com.example.springboot2.entity</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="com.example.springboot2.entity.User">
<h3>类&nbsp;<a href="com/example/springboot2/entity/User.html" title="com.example.springboot2.entity中的类">com.example.springboot2.entity.User</a></h3>
<div class="type-signature">class User extends <a href="com/example/springboot2/entity/BaseEntity.html" title="com.example.springboot2.entity中的类">BaseEntity</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="java.io中的类或接口" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>序列化字段</h4>
<ul class="block-list">
<li class="block-list">
<h5>avatar</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a> avatar</pre>
</li>
<li class="block-list">
<h5>email</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a> email</pre>
</li>
<li class="block-list">
<h5>id</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="java.lang中的类或接口" class="external-link">Long</a> id</pre>
</li>
<li class="block-list">
<h5>lastLoginIp</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a> lastLoginIp</pre>
</li>
<li class="block-list">
<h5>lastLoginTime</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/LocalDateTime.html" title="java.time中的类或接口" class="external-link">LocalDateTime</a> lastLoginTime</pre>
</li>
<li class="block-list">
<h5>name</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a> name</pre>
</li>
<li class="block-list">
<h5>password</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a> password</pre>
</li>
<li class="block-list">
<h5>phone</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a> phone</pre>
</li>
<li class="block-list">
<h5>role</h5>
<pre><a href="com/example/springboot2/entity/User.UserRole.html" title="enum class in com.example.springboot2.entity">User.UserRole</a> role</pre>
</li>
<li class="block-list">
<h5>status</h5>
<pre><a href="com/example/springboot2/entity/User.UserStatus.html" title="enum class in com.example.springboot2.entity">User.UserStatus</a> status</pre>
</li>
<li class="block-list">
<h5>username</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a> username</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="程序包">程序包&nbsp;<a href="com/example/springboot2/exception/package-summary.html">com.example.springboot2.exception</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="com.example.springboot2.exception.BusinessException">
<h3>异常错误&nbsp;<a href="com/example/springboot2/exception/BusinessException.html" title="com.example.springboot2.exception中的类">com.example.springboot2.exception.BusinessException</a></h3>
<div class="type-signature">class BusinessException extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/RuntimeException.html" title="java.lang中的类或接口" class="external-link">RuntimeException</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="java.io中的类或接口" class="external-link">Serializable</a></div>
<ul class="block-list">
<li>
<section class="detail">
<h4>序列化字段</h4>
<ul class="block-list">
<li class="block-list">
<h5>code</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a> code</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
