typeSearchIndex = [{"l":"All Classes and Interfaces","u":"allclasses-index.html"},{"p":"com.example.springboot2.controller","l":"AuthController"},{"p":"com.example.springboot2.service","l":"AuthService"},{"p":"com.example.springboot2.entity","l":"BaseEntity"},{"p":"com.example.springboot2.exception","l":"BusinessException"},{"p":"com.example.springboot2.entity","l":"Merchant.CertificationStatus"},{"p":"com.example.springboot2.util","l":"JwtUtil.ClaimsResolver"},{"p":"com.example.springboot2.config","l":"CorsConfig"},{"p":"com.example.springboot2.entity","l":"Coupon"},{"p":"com.example.springboot2.controller","l":"CouponController"},{"p":"com.example.springboot2.repository","l":"CouponRepository"},{"p":"com.example.springboot2.service","l":"CouponService"},{"p":"com.example.springboot2.service","l":"CouponService.CouponStats"},{"p":"com.example.springboot2.entity","l":"Coupon.CouponStatus"},{"p":"com.example.springboot2.entity","l":"Coupon.CouponType"},{"p":"com.example.springboot2.controller","l":"DashboardController"},{"p":"com.example.springboot2.service","l":"DashboardService"},{"p":"com.example.springboot2.config","l":"DataInitializer"},{"p":"com.example.springboot2.controller","l":"FileController"},{"p":"com.example.springboot2.exception","l":"GlobalExceptionHandler"},{"p":"com.example.springboot2.entity","l":"Goods"},{"p":"com.example.springboot2.entity","l":"GoodsCategory"},{"p":"com.example.springboot2.controller","l":"GoodsCategoryController"},{"p":"com.example.springboot2.repository","l":"GoodsCategoryRepository"},{"p":"com.example.springboot2.service","l":"GoodsCategoryService"},{"p":"com.example.springboot2.controller","l":"GoodsController"},{"p":"com.example.springboot2.repository","l":"GoodsRepository"},{"p":"com.example.springboot2.service","l":"GoodsService"},{"p":"com.example.springboot2.service","l":"GoodsService.GoodsStats"},{"p":"com.example.springboot2.entity","l":"Goods.GoodsStatus"},{"p":"com.example.springboot2.entity","l":"Goods.GoodsType"},{"p":"com.example.springboot2.config","l":"JpaConfig"},{"p":"com.example.springboot2.security","l":"JwtAuthenticationEntryPoint"},{"p":"com.example.springboot2.security","l":"JwtAuthenticationFilter"},{"p":"com.example.springboot2.util","l":"JwtUtil"},{"p":"com.example.springboot2.service","l":"LaundryBusinessService"},{"p":"com.example.springboot2.controller","l":"LaundryController"},{"p":"com.example.springboot2.entity","l":"LaundryOrder"},{"p":"com.example.springboot2.entity","l":"LaundryOrderItem"},{"p":"com.example.springboot2.repository","l":"LaundryOrderRepository"},{"p":"com.example.springboot2.entity","l":"LaundryOrder.LaundryOrderStatus"},{"p":"com.example.springboot2.entity","l":"LaundryService"},{"p":"com.example.springboot2.repository","l":"LaundryServiceRepository"},{"p":"com.example.springboot2.dto","l":"LoginRequest"},{"p":"com.example.springboot2.dto","l":"LoginResponse"},{"p":"com.example.springboot2.entity","l":"Merchant"},{"p":"com.example.springboot2.controller","l":"MerchantController"},{"p":"com.example.springboot2.controller","l":"MerchantFinanceController"},{"p":"com.example.springboot2.service","l":"MerchantFinanceService"},{"p":"com.example.springboot2.repository","l":"MerchantRepository"},{"p":"com.example.springboot2.service","l":"MerchantService"},{"p":"com.example.springboot2.controller","l":"MerchantServiceController"},{"p":"com.example.springboot2.service","l":"MerchantServiceManagementService"},{"p":"com.example.springboot2.entity","l":"Merchant.MerchantStatus"},{"p":"com.example.springboot2.entity","l":"Order"},{"p":"com.example.springboot2.controller","l":"OrderController"},{"p":"com.example.springboot2.entity","l":"OrderItem"},{"p":"com.example.springboot2.repository","l":"OrderRepository"},{"p":"com.example.springboot2.service","l":"OrderService"},{"p":"com.example.springboot2.service","l":"OrderService.OrderStats"},{"p":"com.example.springboot2.entity","l":"Order.OrderStatus"},{"p":"com.example.springboot2.entity","l":"Order.OrderType"},{"p":"com.example.springboot2.common","l":"PageResult"},{"p":"com.example.springboot2.config","l":"PasswordConfig"},{"p":"com.example.springboot2.controller","l":"RegionController"},{"p":"com.example.springboot2.dto","l":"RegisterRequest"},{"p":"com.example.springboot2.common","l":"Result"},{"p":"com.example.springboot2.config","l":"SecurityConfig"},{"p":"com.example.springboot2.entity","l":"LaundryService.ServiceType"},{"p":"com.example.springboot2.controller","l":"SimpleAuthController"},{"p":"com.example.springboot2","l":"SpringBoot2Application"},{"p":"com.example.springboot2.entity","l":"User"},{"p":"com.example.springboot2.dto","l":"LoginResponse.UserInfo"},{"p":"com.example.springboot2.repository","l":"UserRepository"},{"p":"com.example.springboot2.entity","l":"User.UserRole"},{"p":"com.example.springboot2.service","l":"UserService"},{"p":"com.example.springboot2.entity","l":"User.UserStatus"}];updateSearchResults();