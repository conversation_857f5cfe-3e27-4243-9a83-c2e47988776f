@options
@packages
-classpath
'C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.2.12/spring-boot-starter-web-3.2.12.jar;C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.2.12/spring-boot-starter-3.2.12.jar;C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.2.12/spring-boot-starter-logging-3.2.12.jar;C:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.4.14/logback-classic-1.4.14.jar;C:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.4.14/logback-core-1.4.14.jar;C:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.21.1/log4j-to-slf4j-2.21.1.jar;C:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.16/jul-to-slf4j-2.0.16.jar;C:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar;C:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.2/snakeyaml-2.2.jar;C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.2.12/spring-boot-starter-json-3.2.12.jar;C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.15.4/jackson-datatype-jdk8-2.15.4.jar;C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.2.12/spring-boot-starter-tomcat-3.2.12.jar;C:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.33/tomcat-embed-core-10.1.33.jar;C:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.33/tomcat-embed-websocket-10.1.33.jar;C:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.1.15/spring-web-6.1.15.jar;C:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.1.15/spring-beans-6.1.15.jar;C:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.1.15/spring-webmvc-6.1.15.jar;C:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.1.15/spring-context-6.1.15.jar;C:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.1.15/spring-expression-6.1.15.jar;C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.2.12/spring-boot-starter-data-jpa-3.2.12.jar;C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.2.12/spring-boot-starter-jdbc-3.2.12.jar;C:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/5.0.1/HikariCP-5.0.1.jar;C:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.1.15/spring-jdbc-6.1.15.jar;C:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.4.10.Final/hibernate-core-6.4.10.Final.jar;C:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar;C:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar;C:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar;C:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.2.12/spring-data-jpa-3.2.12.jar;C:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.2.12/spring-data-commons-3.2.12.jar;C:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.1.15/spring-orm-6.1.15.jar;C:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.1.15/spring-aspects-6.1.15.jar;C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.2.12/spring-boot-starter-security-3.2.12.jar;C:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.1.15/spring-aop-6.1.15.jar;C:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.2.0/spring-security-config-6.2.0.jar;C:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.2.0/spring-security-web-6.2.0.jar;C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.2.12/spring-boot-starter-validation-3.2.12.jar;C:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.33/tomcat-embed-el-10.1.33.jar;C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/3.2.12/spring-boot-starter-data-redis-3.2.12.jar;C:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/6.3.2.RELEASE/lettuce-core-6.3.2.RELEASE.jar;C:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.115.Final/netty-common-4.1.115.Final.jar;C:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.115.Final/netty-handler-4.1.115.Final.jar;C:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.115.Final/netty-resolver-4.1.115.Final.jar;C:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.115.Final/netty-buffer-4.1.115.Final.jar;C:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.115.Final/netty-transport-native-unix-common-4.1.115.Final.jar;C:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.115.Final/netty-codec-4.1.115.Final.jar;C:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.115.Final/netty-transport-4.1.115.Final.jar;C:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.6.12/reactor-core-3.6.12.jar;C:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar;C:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/3.2.12/spring-data-redis-3.2.12.jar;C:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/3.2.12/spring-data-keyvalue-3.2.12.jar;C:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/6.1.15/spring-oxm-6.1.15.jar;C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-cache/3.2.12/spring-boot-starter-cache-3.2.12.jar;C:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.1.15/spring-context-support-6.1.15.jar;C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-mail/3.2.12/spring-boot-starter-mail-3.2.12.jar;C:/Users/<USER>/.m2/repository/org/eclipse/angus/jakarta.mail/2.0.3/jakarta.mail-2.0.3.jar;C:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar;C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/3.2.12/spring-boot-starter-websocket-3.2.12.jar;C:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.1.15/spring-messaging-6.1.15.jar;C:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.1.15/spring-websocket-6.1.15.jar;C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.2.12/spring-boot-starter-actuator-3.2.12.jar;C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.2.12/spring-boot-actuator-autoconfigure-3.2.12.jar;C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.2.12/spring-boot-actuator-3.2.12.jar;C:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.12.0/micrometer-observation-1.12.0.jar;C:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.12.0/micrometer-jakarta9-1.12.0.jar;C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.2.12/spring-boot-starter-aop-3.2.12.jar;C:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.22.1/aspectjweaver-1.9.22.1.jar;C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-quartz/3.2.12/spring-boot-starter-quartz-3.2.12.jar;C:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.1.15/spring-tx-6.1.15.jar;C:/Users/<USER>/.m2/repository/org/quartz-scheduler/quartz/2.3.2/quartz-2.3.2.jar;C:/Users/<USER>/.m2/repository/com/mchange/mchange-commons-java/0.2.15/mchange-commons-java-0.2.15.jar;C:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.5.4/mybatis-plus-boot-starter-3.5.4.jar;C:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.5.4/mybatis-plus-3.5.4.jar;C:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.5.4/mybatis-plus-core-3.5.4.jar;C:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.5.4/mybatis-plus-annotation-3.5.4.jar;C:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.5.4/mybatis-plus-extension-3.5.4.jar;C:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.13/mybatis-3.5.13.jar;C:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/4.6/jsqlparser-4.6.jar;C:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.1.1/mybatis-spring-2.1.1.jar;C:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-spring-boot-autoconfigure/3.5.4/mybatis-plus-spring-boot-autoconfigure-3.5.4.jar;C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.2.12/spring-boot-autoconfigure-3.2.12.jar;C:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.2.20/druid-spring-boot-starter-1.2.20.jar;C:/Users/<USER>/.m2/repository/com/alibaba/druid/1.2.20/druid-1.2.20.jar;C:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.16/slf4j-api-2.0.16.jar;C:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar;C:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar;C:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar;C:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar;C:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt-api/0.11.5/jjwt-api-0.11.5.jar;C:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.77/bcprov-jdk18on-1.77.jar;C:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar;C:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.22/hutool-all-5.8.22.jar;C:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.14.0/commons-lang3-3.14.0.jar;C:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar;C:/Users/<USER>/.m2/repository/commons-io/commons-io/2.15.0/commons-io-2.15.0.jar;C:/Users/<USER>/.m2/repository/com/google/guava/guava/32.1.3-jre/guava-32.1.3-jre.jar;C:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar;C:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar;C:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.37.0/checker-qual-3.37.0.jar;C:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.21.1/error_prone_annotations-2.21.1.jar;C:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/2.8/j2objc-annotations-2.8.jar;C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.15.4/jackson-databind-2.15.4.jar;C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.15.4/jackson-annotations-2.15.4.jar;C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.4/jackson-core-2.15.4.jar;C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.15.4/jackson-datatype-jsr310-2.15.4.jar;C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.15.4/jackson-module-parameter-names-2.15.4.jar;C:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2/2.0.43/fastjson2-2.0.43.jar;C:/Users/<USER>/.m2/repository/com/github/xiaoymin/knife4j-openapi3-spring-boot-starter/4.3.0/knife4j-openapi3-spring-boot-starter-4.3.0.jar;C:/Users/<USER>/.m2/repository/com/github/xiaoymin/knife4j-core/4.3.0/knife4j-core-4.3.0.jar;C:/Users/<USER>/.m2/repository/com/github/xiaoymin/knife4j-openapi3-ui/4.3.0/knife4j-openapi3-ui-4.3.0.jar;C:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-ui/1.7.0/springdoc-openapi-ui-1.7.0.jar;C:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-webmvc-core/1.7.0/springdoc-openapi-webmvc-core-1.7.0.jar;C:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-common/1.7.0/springdoc-openapi-common-1.7.0.jar;C:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core/2.2.9/swagger-core-2.2.9.jar;C:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.2.9/swagger-annotations-2.2.9.jar;C:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models/2.2.9/swagger-models-2.2.9.jar;C:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-ui/2.2.0/springdoc-openapi-starter-webmvc-ui-2.2.0.jar;C:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-api/2.2.0/springdoc-openapi-starter-webmvc-api-2.2.0.jar;C:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-common/2.2.0/springdoc-openapi-starter-common-2.2.0.jar;C:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core-jakarta/2.2.15/swagger-core-jakarta-2.2.15.jar;C:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations-jakarta/2.2.15/swagger-annotations-jakarta-2.2.15.jar;C:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models-jakarta/2.2.15/swagger-models-jakarta-2.2.15.jar;C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.15.4/jackson-dataformat-yaml-2.15.4.jar;C:/Users/<USER>/.m2/repository/org/webjars/swagger-ui/5.2.0/swagger-ui-5.2.0.jar;C:/Users/<USER>/.m2/repository/io/minio/minio/8.5.7/minio-8.5.7.jar;C:/Users/<USER>/.m2/repository/com/carrotsearch/thirdparty/simple-xml-safe/2.7.1/simple-xml-safe-2.7.1.jar;C:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.24.0/commons-compress-1.24.0.jar;C:/Users/<USER>/.m2/repository/org/xerial/snappy/snappy-java/1.1.10.5/snappy-java-1.1.10.5.jar;C:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/3.3.2/easyexcel-3.3.2.jar;C:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-core/3.3.2/easyexcel-core-3.3.2.jar;C:/Users/<USER>/.m2/repository/com/alibaba/easyexcel-support/3.3.2/easyexcel-support-3.3.2.jar;C:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/4.1.2/poi-ooxml-schemas-4.1.2.jar;C:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.8/commons-csv-1.8.jar;C:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.10.8/ehcache-3.10.8.jar;C:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.1/cache-api-1.1.1.jar;C:/Users/<USER>/.m2/repository/org/apache/poi/poi/5.2.5/poi-5.2.5.jar;C:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.16.1/commons-codec-1.16.1.jar;C:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar;C:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.3/SparseBitSet-1.3.jar;C:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.21.1/log4j-api-2.21.1.jar;C:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/5.2.5/poi-ooxml-5.2.5.jar;C:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-lite/5.2.5/poi-ooxml-lite-5.2.5.jar;C:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/5.2.0/xmlbeans-5.2.0.jar;C:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.08/curvesapi-1.08.jar;C:/Users/<USER>/.m2/repository/com/google/zxing/core/3.5.2/core-3.5.2.jar;C:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.5.2/javase-3.5.2.jar;C:/Users/<USER>/.m2/repository/com/beust/jcommander/1.82/jcommander-1.82.jar;C:/Users/<USER>/.m2/repository/net/coobird/thumbnailator/0.4.20/thumbnailator-0.4.20.jar;C:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/4.12.0/okhttp-4.12.0.jar;C:/Users/<USER>/.m2/repository/com/squareup/okio/okio/3.6.0/okio-3.6.0.jar;C:/Users/<USER>/.m2/repository/com/squareup/okio/okio-jvm/3.6.0/okio-jvm-3.6.0.jar;C:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-common/1.9.25/kotlin-stdlib-common-1.9.25.jar;C:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.9.25/kotlin-stdlib-jdk8-1.9.25.jar;C:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib/1.9.25/kotlin-stdlib-1.9.25.jar;C:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.9.25/kotlin-stdlib-jdk7-1.9.25.jar;C:/Users/<USER>/.m2/repository/org/apache/httpcomponents/client5/httpclient5/5.2.3/httpclient5-5.2.3.jar;C:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5/5.2.5/httpcore5-5.2.5.jar;C:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5-h2/5.2.5/httpcore5-h2-5.2.5.jar;C:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.1.Final/hibernate-validator-8.0.1.Final.jar;C:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar;C:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.5.3.Final/jboss-logging-3.5.3.Final.jar;C:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.6.0/classmate-1.6.0.jar;C:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.12.0/micrometer-core-1.12.0.jar;C:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.12.0/micrometer-commons-1.12.0.jar;C:/Users/<USER>/.m2/repository/io/micrometer/micrometer-registry-prometheus/1.12.0/micrometer-registry-prometheus-1.12.0.jar;C:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_common/0.16.0/simpleclient_common-0.16.0.jar;C:/Users/<USER>/.m2/repository/io/prometheus/simpleclient/0.16.0/simpleclient-0.16.0.jar;C:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel/0.16.0/simpleclient_tracer_otel-0.16.0.jar;C:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_common/0.16.0/simpleclient_tracer_common-0.16.0.jar;C:/Users/<USER>/.m2/repository/io/prometheus/simpleclient_tracer_otel_agent/0.16.0/simpleclient_tracer_otel_agent-0.16.0.jar;C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.2.12/spring-boot-3.2.12.jar;C:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-configuration-processor/3.2.12/spring-boot-configuration-processor-3.2.12.jar;C:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar;C:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.1.15/spring-core-6.1.15.jar;C:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.1.15/spring-jcl-6.1.15.jar;C:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.2.0/spring-security-core-6.2.0.jar;C:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.2.0/spring-security-crypto-6.2.0.jar;C:/Users/<USER>/.m2/repository/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar'
-encoding
'UTF-8'
-protected
-quiet
--release
17
-sourcepath
'I:/spring-boot/spring-boot/spring-boot/spring-boot2/src/main/java;I:/spring-boot/spring-boot/spring-boot/spring-boot2/target/generated-sources/annotations'
-author
-bottom
'Copyright &#169; 2025. All rights reserved.'
-charset
'UTF-8'
-d
'I:/spring-boot/spring-boot/spring-boot/spring-boot2/target/apidocs'
-docencoding
'UTF-8'
-Xdoclint:none
-doctitle
'spring-boot2 0.0.1-SNAPSHOT API'
-use
-version
-windowtitle
'spring-boot2 0.0.1-SNAPSHOT API'
com.example.springboot2.common
com.example.springboot2.config
com.example.springboot2.controller
com.example.springboot2.dto
com.example.springboot2.entity
com.example.springboot2.exception
com.example.springboot2.repository
com.example.springboot2.security
com.example.springboot2.service
com.example.springboot2
com.example.springboot2.util
C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\3.2.12\spring-boot-starter-web-3.2.12.jar = 1750014967392
C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\3.2.12\spring-boot-starter-3.2.12.jar = 1750014967749
C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\3.2.12\spring-boot-starter-logging-3.2.12.jar = 1750014967714
C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.4.14\logback-classic-1.4.14.jar = 1749688956506
C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.4.14\logback-core-1.4.14.jar = 1749688956333
C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar = 1748958441567
C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\2.0.16\jul-to-slf4j-2.0.16.jar = 1750014967685
C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar = 1748958443373
C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar = 1748958445482
C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\3.2.12\spring-boot-starter-json-3.2.12.jar = 1750014967747
C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.4\jackson-datatype-jdk8-2.15.4.jar = 1750014967682
C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\3.2.12\spring-boot-starter-tomcat-3.2.12.jar = 1750014968058
C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.33\tomcat-embed-core-10.1.33.jar = 1750014972843
C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.33\tomcat-embed-websocket-10.1.33.jar = 1750014968671
C:\Users\<USER>\.m2\repository\org\springframework\spring-web\6.1.15\spring-web-6.1.15.jar = 1750014969753
C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\6.1.15\spring-beans-6.1.15.jar = 1750014968772
C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\6.1.15\spring-webmvc-6.1.15.jar = 1750014970086
C:\Users\<USER>\.m2\repository\org\springframework\spring-context\6.1.15\spring-context-6.1.15.jar = 1750014969897
C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\6.1.15\spring-expression-6.1.15.jar = 1750014969465
C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\3.2.12\spring-boot-starter-data-jpa-3.2.12.jar = 1750014969844
C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\3.2.12\spring-boot-starter-jdbc-3.2.12.jar = 1750014970127
C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar = 1748958465221
C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\6.1.15\spring-jdbc-6.1.15.jar = 1750014970549
C:\Users\<USER>\.m2\repository\org\hibernate\orm\hibernate-core\6.4.10.Final\hibernate-core-6.4.10.Final.jar = 1750014980942
C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\3.1.0\jakarta.persistence-api-3.1.0.jar = 1748958470243
C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\2.0.1\jakarta.transaction-api-2.0.1.jar = 1748958470769
C:\Users\<USER>\.m2\repository\org\antlr\antlr4-runtime\4.13.0\antlr4-runtime-4.13.0.jar = 1748695106387
C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\3.2.12\spring-data-jpa-3.2.12.jar = 1750014972647
C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\3.2.12\spring-data-commons-3.2.12.jar = 1750014972206
C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\6.1.15\spring-orm-6.1.15.jar = 1750014971284
C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\6.1.15\spring-aspects-6.1.15.jar = 1750014972322
C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\3.2.12\spring-boot-starter-security-3.2.12.jar = 1750014972599
C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\6.1.15\spring-aop-6.1.15.jar = 1750014972972
C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\6.2.0\spring-security-config-6.2.0.jar = 1748958458968
C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\6.2.0\spring-security-web-6.2.0.jar = 1748958461289
C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\3.2.12\spring-boot-starter-validation-3.2.12.jar = 1750014972970
C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.33\tomcat-embed-el-10.1.33.jar = 1750014973186
C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\3.2.12\spring-boot-starter-data-redis-3.2.12.jar = 1750014973206
C:\Users\<USER>\.m2\repository\io\lettuce\lettuce-core\6.3.2.RELEASE\lettuce-core-6.3.2.RELEASE.jar = 1747508832199
C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.115.Final\netty-common-4.1.115.Final.jar = 1750014974053
C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.115.Final\netty-handler-4.1.115.Final.jar = 1750014973853
C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.115.Final\netty-resolver-4.1.115.Final.jar = 1750014973590
C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.115.Final\netty-buffer-4.1.115.Final.jar = 1750014973856
C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.115.Final\netty-transport-native-unix-common-4.1.115.Final.jar = 1750014973957
C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.115.Final\netty-codec-4.1.115.Final.jar = 1750014974577
C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.115.Final\netty-transport-4.1.115.Final.jar = 1750014975097
C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.6.12\reactor-core-3.6.12.jar = 1750014976476
C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar = 1748695082953
C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\3.2.12\spring-data-redis-3.2.12.jar = 1750014977168
C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\3.2.12\spring-data-keyvalue-3.2.12.jar = 1750014975130
C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\6.1.15\spring-oxm-6.1.15.jar = 1750014975529
C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-cache\3.2.12\spring-boot-starter-cache-3.2.12.jar = 1750014975464
C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\6.1.15\spring-context-support-6.1.15.jar = 1750014976070
C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-mail\3.2.12\spring-boot-starter-mail-3.2.12.jar = 1750014975969
C:\Users\<USER>\.m2\repository\org\eclipse\angus\jakarta.mail\2.0.3\jakarta.mail-2.0.3.jar = 1749491408418
C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar = 1747507900453
C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-websocket\3.2.12\spring-boot-starter-websocket-3.2.12.jar = 1750014976343
C:\Users\<USER>\.m2\repository\org\springframework\spring-messaging\6.1.15\spring-messaging-6.1.15.jar = 1750014976996
C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\6.1.15\spring-websocket-6.1.15.jar = 1750014977233
C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\3.2.12\spring-boot-starter-actuator-3.2.12.jar = 1750014976782
C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.2.12\spring-boot-actuator-autoconfigure-3.2.12.jar = 1750014977795
C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\3.2.12\spring-boot-actuator-3.2.12.jar = 1750014977775
C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-observation\1.12.0\micrometer-observation-1.12.0.jar = 1748958453455
C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-jakarta9\1.12.0\micrometer-jakarta9-1.12.0.jar = 1748966251767
C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\3.2.12\spring-boot-starter-aop-3.2.12.jar = 1750014977552
C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.22.1\aspectjweaver-1.9.22.1.jar = 1750014980308
C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-quartz\3.2.12\spring-boot-starter-quartz-3.2.12.jar = 1750015005467
C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\6.1.15\spring-tx-6.1.15.jar = 1750014971939
C:\Users\<USER>\.m2\repository\org\quartz-scheduler\quartz\2.3.2\quartz-2.3.2.jar = 1750013396830
C:\Users\<USER>\.m2\repository\com\mchange\mchange-commons-java\0.2.15\mchange-commons-java-0.2.15.jar = 1750013396250
C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-boot-starter\3.5.4\mybatis-plus-boot-starter-3.5.4.jar = 1750012974702
C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus\3.5.4\mybatis-plus-3.5.4.jar = 1750012975196
C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-core\3.5.4\mybatis-plus-core-3.5.4.jar = 1750012975341
C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-annotation\3.5.4\mybatis-plus-annotation-3.5.4.jar = 1750012975082
C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-extension\3.5.4\mybatis-plus-extension-3.5.4.jar = 1750012975119
C:\Users\<USER>\.m2\repository\org\mybatis\mybatis\3.5.13\mybatis-3.5.13.jar = 1750012820748
C:\Users\<USER>\.m2\repository\com\github\jsqlparser\jsqlparser\4.6\jsqlparser-4.6.jar = 1748695073313
C:\Users\<USER>\.m2\repository\org\mybatis\mybatis-spring\2.1.1\mybatis-spring-2.1.1.jar = 1750012819670
C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-spring-boot-autoconfigure\3.5.4\mybatis-plus-spring-boot-autoconfigure-3.5.4.jar = 1750012975228
C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\3.2.12\spring-boot-autoconfigure-3.2.12.jar = 1750014980295
C:\Users\<USER>\.m2\repository\com\alibaba\druid-spring-boot-starter\1.2.20\druid-spring-boot-starter-1.2.20.jar = 1750012819778
C:\Users\<USER>\.m2\repository\com\alibaba\druid\1.2.20\druid-1.2.20.jar = 1750012823324
C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.16\slf4j-api-2.0.16.jar = 1750014978069
C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\4.0.5\jaxb-runtime-4.0.5.jar = 1747508826036
C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-core\4.0.5\jaxb-core-4.0.5.jar = 1747508825609
C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\4.0.5\txw2-4.0.5.jar = 1747508825942
C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar = 1748958479127
C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt-api\0.11.5\jjwt-api-0.11.5.jar = 1748958486440
C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk18on\1.77\bcprov-jdk18on-1.77.jar = 1750013404819
C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.30\lombok-1.18.30.jar = 1748958490560
C:\Users\<USER>\.m2\repository\cn\hutool\hutool-all\5.8.22\hutool-all-5.8.22.jar = 1750012824685
C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.14.0\commons-lang3-3.14.0.jar = 1747508838069
C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar = 1747559501158
C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.15.0\commons-io-2.15.0.jar = 1750012823096
C:\Users\<USER>\.m2\repository\com\google\guava\guava\32.1.3-jre\guava-32.1.3-jre.jar = 1750012826658
C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar = 1748955865559
C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar = 1748955866071
C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar = 1747759500400
C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.37.0\checker-qual-3.37.0.jar = 1750012825085
C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.21.1\error_prone_annotations-2.21.1.jar = 1750012824964
C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\2.8\j2objc-annotations-2.8.jar = 1750012825247
C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.15.4\jackson-databind-2.15.4.jar = 1750014979910
C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.4\jackson-annotations-2.15.4.jar = 1750014978401
C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.15.4\jackson-core-2.15.4.jar = 1750014979415
C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.4\jackson-datatype-jsr310-2.15.4.jar = 1750014979865
C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.4\jackson-module-parameter-names-2.15.4.jar = 1750014980158
C:\Users\<USER>\.m2\repository\com\alibaba\fastjson2\fastjson2\2.0.43\fastjson2-2.0.43.jar = 1750012955067
C:\Users\<USER>\.m2\repository\com\github\xiaoymin\knife4j-openapi3-spring-boot-starter\4.3.0\knife4j-openapi3-spring-boot-starter-4.3.0.jar = 1750012823266
C:\Users\<USER>\.m2\repository\com\github\xiaoymin\knife4j-core\4.3.0\knife4j-core-4.3.0.jar = 1748695087454
C:\Users\<USER>\.m2\repository\com\github\xiaoymin\knife4j-openapi3-ui\4.3.0\knife4j-openapi3-ui-4.3.0.jar = 1748695088996
C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-ui\1.7.0\springdoc-openapi-ui-1.7.0.jar = 1750012823626
C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-webmvc-core\1.7.0\springdoc-openapi-webmvc-core-1.7.0.jar = 1750012823718
C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-common\1.7.0\springdoc-openapi-common-1.7.0.jar = 1750012823944
C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-core\2.2.9\swagger-core-2.2.9.jar = 1750012824090
C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations\2.2.9\swagger-annotations-2.2.9.jar = 1750012824018
C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models\2.2.9\swagger-models-2.2.9.jar = 1750012824173
C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-starter-webmvc-ui\2.2.0\springdoc-openapi-starter-webmvc-ui-2.2.0.jar = 1747469682948
C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-starter-webmvc-api\2.2.0\springdoc-openapi-starter-webmvc-api-2.2.0.jar = 1747469683748
C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-starter-common\2.2.0\springdoc-openapi-starter-common-2.2.0.jar = 1747469691781
C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-core-jakarta\2.2.15\swagger-core-jakarta-2.2.15.jar = 1747469687157
C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations-jakarta\2.2.15\swagger-annotations-jakarta-2.2.15.jar = 1747469688864
C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models-jakarta\2.2.15\swagger-models-jakarta-2.2.15.jar = 1747469689520
C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.15.4\jackson-dataformat-yaml-2.15.4.jar = 1750014980232
C:\Users\<USER>\.m2\repository\org\webjars\swagger-ui\5.2.0\swagger-ui-5.2.0.jar = 1747469761959
C:\Users\<USER>\.m2\repository\io\minio\minio\8.5.7\minio-8.5.7.jar = 1750012824684
C:\Users\<USER>\.m2\repository\com\carrotsearch\thirdparty\simple-xml-safe\2.7.1\simple-xml-safe-2.7.1.jar = 1750012824945
C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.24.0\commons-compress-1.24.0.jar = 1747483159468
C:\Users\<USER>\.m2\repository\org\xerial\snappy\snappy-java\1.1.10.5\snappy-java-1.1.10.5.jar = 1750012829037
C:\Users\<USER>\.m2\repository\com\alibaba\easyexcel\3.3.2\easyexcel-3.3.2.jar = 1748539567611
C:\Users\<USER>\.m2\repository\com\alibaba\easyexcel-core\3.3.2\easyexcel-core-3.3.2.jar = 1748539603833
C:\Users\<USER>\.m2\repository\com\alibaba\easyexcel-support\3.3.2\easyexcel-support-3.3.2.jar = 1748539570482
C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml-schemas\4.1.2\poi-ooxml-schemas-4.1.2.jar = 1748695129677
C:\Users\<USER>\.m2\repository\org\apache\commons\commons-csv\1.8\commons-csv-1.8.jar = 1748695132069
C:\Users\<USER>\.m2\repository\org\ehcache\ehcache\3.10.8\ehcache-3.10.8.jar = 1748695133303
C:\Users\<USER>\.m2\repository\javax\cache\cache-api\1.1.1\cache-api-1.1.1.jar = 1748695133807
C:\Users\<USER>\.m2\repository\org\apache\poi\poi\5.2.5\poi-5.2.5.jar = 1750013401963
C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.16.1\commons-codec-1.16.1.jar = 1750014980801
C:\Users\<USER>\.m2\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar = 1748695120455
C:\Users\<USER>\.m2\repository\com\zaxxer\SparseBitSet\1.3\SparseBitSet-1.3.jar = 1750013398385
C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar = 1748958442167
C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml\5.2.5\poi-ooxml-5.2.5.jar = 1750013401934
C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml-lite\5.2.5\poi-ooxml-lite-5.2.5.jar = 1750013405239
C:\Users\<USER>\.m2\repository\org\apache\xmlbeans\xmlbeans\5.2.0\xmlbeans-5.2.0.jar = 1750013402729
C:\Users\<USER>\.m2\repository\com\github\virtuald\curvesapi\1.08\curvesapi-1.08.jar = 1750013402408
C:\Users\<USER>\.m2\repository\com\google\zxing\core\3.5.2\core-3.5.2.jar = 1750012827430
C:\Users\<USER>\.m2\repository\com\google\zxing\javase\3.5.2\javase-3.5.2.jar = 1750012827024
C:\Users\<USER>\.m2\repository\com\beust\jcommander\1.82\jcommander-1.82.jar = 1750012828106
C:\Users\<USER>\.m2\repository\net\coobird\thumbnailator\0.4.20\thumbnailator-0.4.20.jar = 1750013402416
C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\4.12.0\okhttp-4.12.0.jar = 1748695166309
C:\Users\<USER>\.m2\repository\com\squareup\okio\okio\3.6.0\okio-3.6.0.jar = 1748695166873
C:\Users\<USER>\.m2\repository\com\squareup\okio\okio-jvm\3.6.0\okio-jvm-3.6.0.jar = 1748695167476
C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.9.25\kotlin-stdlib-common-1.9.25.jar = 1750014980689
C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.9.25\kotlin-stdlib-jdk8-1.9.25.jar = 1750014980600
C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib\1.9.25\kotlin-stdlib-1.9.25.jar = 1750014981935
C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.9.25\kotlin-stdlib-jdk7-1.9.25.jar = 1750014980920
C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\client5\httpclient5\5.2.3\httpclient5-5.2.3.jar = 1749749422203
C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\core5\httpcore5\5.2.5\httpcore5-5.2.5.jar = 1750014981508
C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\core5\httpcore5-h2\5.2.5\httpcore5-h2-5.2.5.jar = 1750014981433
C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\8.0.1.Final\hibernate-validator-8.0.1.Final.jar = 1748695091327
C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar = 1748958495398
C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.5.3.Final\jboss-logging-3.5.3.Final.jar = 1748958471318
C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.6.0\classmate-1.6.0.jar = 1748958473073
C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.12.0\micrometer-core-1.12.0.jar = 1748966252747
C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-commons\1.12.0\micrometer-commons-1.12.0.jar = 1748958454132
C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-registry-prometheus\1.12.0\micrometer-registry-prometheus-1.12.0.jar = 1749578913806
C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_common\0.16.0\simpleclient_common-0.16.0.jar = 1749578913651
C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient\0.16.0\simpleclient-0.16.0.jar = 1749578913911
C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_tracer_otel\0.16.0\simpleclient_tracer_otel-0.16.0.jar = 1749578913713
C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_tracer_common\0.16.0\simpleclient_tracer_common-0.16.0.jar = 1749578913845
C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_tracer_otel_agent\0.16.0\simpleclient_tracer_otel_agent-0.16.0.jar = 1749578913911
C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\3.2.12\spring-boot-3.2.12.jar = 1750014983547
C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-configuration-processor\3.2.12\spring-boot-configuration-processor-3.2.12.jar = 1750014981864
C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar = 1747507900329
C:\Users\<USER>\.m2\repository\org\springframework\spring-core\6.1.15\spring-core-6.1.15.jar = 1750014983712
C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\6.1.15\spring-jcl-6.1.15.jar = 1750014982650
C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\6.2.0\spring-security-core-6.2.0.jar = 1748958459718
C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\6.2.0\spring-security-crypto-6.2.0.jar = 1748958460425
C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\17.0.0\annotations-17.0.0.jar = 1747480306266
I:\spring-boot\spring-boot\spring-boot\spring-boot2\src\main\java = 1749918117069
I:\spring-boot\spring-boot\spring-boot\spring-boot2\target\generated-sources\annotations = 1750258731957
I:\spring-boot\spring-boot\spring-boot\spring-boot2\target\apidocs\allclasses-index.html = 1750258744640
I:\spring-boot\spring-boot\spring-boot\spring-boot2\target\apidocs\allpackages-index.html = 1750258744643
I:\spring-boot\spring-boot\spring-boot\spring-boot2\target\apidocs\element-list = 1750258744079
I:\spring-boot\spring-boot\spring-boot\spring-boot2\target\apidocs\help-doc.html = 1750258744697
I:\spring-boot\spring-boot\spring-boot\spring-boot2\target\apidocs\index-all.html = 1750258744686
I:\spring-boot\spring-boot\spring-boot\spring-boot2\target\apidocs\index.html = 1750258744605
I:\spring-boot\spring-boot\spring-boot\spring-boot2\target\apidocs\jquery-ui.overrides.css = 1750258744711
I:\spring-boot\spring-boot\spring-boot\spring-boot2\target\apidocs\member-search-index.js = 1750258744650
I:\spring-boot\spring-boot\spring-boot\spring-boot2\target\apidocs\module-search-index.js = 1750258744644
I:\spring-boot\spring-boot\spring-boot\spring-boot2\target\apidocs\overview-summary.html = 1750258744690
I:\spring-boot\spring-boot\spring-boot\spring-boot2\target\apidocs\overview-tree.html = 1750258744600
I:\spring-boot\spring-boot\spring-boot\spring-boot2\target\apidocs\package-search-index.js = 1750258744646
I:\spring-boot\spring-boot\spring-boot\spring-boot2\target\apidocs\script.js = 1750258744701
I:\spring-boot\spring-boot\spring-boot\spring-boot2\target\apidocs\search.js = 1750258744702
I:\spring-boot\spring-boot\spring-boot\spring-boot2\target\apidocs\serialized-form.html = 1750258744222
I:\spring-boot\spring-boot\spring-boot\spring-boot2\target\apidocs\stylesheet.css = 1750258744699
I:\spring-boot\spring-boot\spring-boot\spring-boot2\target\apidocs\tag-search-index.js = 1750258744650
I:\spring-boot\spring-boot\spring-boot\spring-boot2\target\apidocs\type-search-index.js = 1750258744648
