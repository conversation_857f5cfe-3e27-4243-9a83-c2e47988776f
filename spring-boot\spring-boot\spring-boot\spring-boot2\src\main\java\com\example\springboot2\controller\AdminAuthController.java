package com.example.springboot2.controller;

import org.springframework.web.bind.annotation.*;
import java.util.Map;
import java.util.HashMap;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/api/admin")
@CrossOrigin(origins = "*")
public class AdminAuthController {

    /**
     * 管理员登录
     */
    @PostMapping("/login")
    public Map<String, Object> login(@RequestBody Map<String, Object> loginData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String username = (String) loginData.get("username");
            String password = (String) loginData.get("password");
            
            // 超级管理员登录检查
            if ("superadmin".equals(username) && "admin123456".equals(password)) {
                return createSuperAdminResponse("ADMIN");
            }
            
            // 模拟管理员登录验证
            Map<String, Object> adminInfo = new HashMap<>();
            adminInfo.put("id", 3001L);
            adminInfo.put("username", username);
            adminInfo.put("realName", "测试管理员");
            adminInfo.put("phone", "13700137000");
            adminInfo.put("email", "<EMAIL>");
            adminInfo.put("avatar", "/images/admin-avatar.png");
            adminInfo.put("role", "ADMIN");
            adminInfo.put("permissions", new String[]{"user:read", "merchant:read", "order:read", "system:read"});
            adminInfo.put("status", "ACTIVE");
            adminInfo.put("department", "运营部");
            
            Map<String, Object> data = new HashMap<>();
            data.put("token", "admin_token_" + System.currentTimeMillis());
            data.put("adminInfo", adminInfo);
            data.put("loginTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            result.put("success", true);
            result.put("message", "登录成功");
            result.put("data", data);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "登录失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取管理员信息
     */
    @GetMapping("/info")
    public Map<String, Object> getAdminInfo(@RequestHeader(value = "Authorization", required = false) String authorization) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查是否为超级管理员
            if (authorization != null && authorization.contains("super_admin_token")) {
                return createSuperAdminResponse("ADMIN");
            }
            
            Map<String, Object> adminInfo = new HashMap<>();
            adminInfo.put("id", 3001L);
            adminInfo.put("username", "testadmin");
            adminInfo.put("realName", "测试管理员");
            adminInfo.put("phone", "13700137000");
            adminInfo.put("email", "<EMAIL>");
            adminInfo.put("avatar", "/images/admin-avatar.png");
            adminInfo.put("role", "ADMIN");
            adminInfo.put("permissions", new String[]{"user:read", "merchant:read", "order:read", "system:read"});
            adminInfo.put("status", "ACTIVE");
            adminInfo.put("department", "运营部");
            
            result.put("success", true);
            result.put("data", adminInfo);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取管理员信息失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public Map<String, Object> logout() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "退出登录成功");
        return result;
    }

    /**
     * 超级管理员登录
     */
    @PostMapping("/super-login")
    public Map<String, Object> superLogin(@RequestBody Map<String, Object> loginData) {
        String username = (String) loginData.get("username");
        String password = (String) loginData.get("password");
        
        if ("superadmin".equals(username) && "admin123456".equals(password)) {
            return createSuperAdminResponse("ADMIN");
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "超级管理员账号或密码错误");
        return result;
    }

    /**
     * 创建超级管理员响应
     */
    private Map<String, Object> createSuperAdminResponse(String userType) {
        Map<String, Object> result = new HashMap<>();
        
        Map<String, Object> adminInfo = new HashMap<>();
        adminInfo.put("id", 999999L);
        adminInfo.put("username", "superadmin");
        adminInfo.put("realName", "超级管理员");
        adminInfo.put("phone", "13800000000");
        adminInfo.put("email", "<EMAIL>");
        adminInfo.put("avatar", "/images/super-admin-avatar.png");
        adminInfo.put("role", "SUPER_ADMIN");
        adminInfo.put("permissions", new String[]{"*"}); // 所有权限
        adminInfo.put("status", "ACTIVE");
        adminInfo.put("department", "系统管理部");
        adminInfo.put("isSuperAdmin", true);
        
        Map<String, Object> data = new HashMap<>();
        data.put("token", "super_admin_token_admin_" + System.currentTimeMillis());
        data.put("adminInfo", adminInfo);
        data.put("loginTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        result.put("success", true);
        result.put("message", "超级管理员登录成功");
        data.put("data", data);
        
        return result;
    }
}
