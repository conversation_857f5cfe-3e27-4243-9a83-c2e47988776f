package com.example.springboot2.controller;

import org.springframework.web.bind.annotation.*;
import java.util.Map;
import java.util.HashMap;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/api/merchant")
@CrossOrigin(origins = "*")
public class MerchantAuthController {

    /**
     * 商家登录
     */
    @PostMapping("/login")
    public Map<String, Object> login(@RequestBody Map<String, Object> loginData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String username = (String) loginData.get("username");
            String password = (String) loginData.get("password");
            
            // 超级管理员登录检查
            if ("superadmin".equals(username) && "admin123456".equals(password)) {
                return createSuperAdminResponse("MERCHANT");
            }
            
            // 模拟商家登录验证
            Map<String, Object> merchantInfo = new HashMap<>();
            merchantInfo.put("id", 2001L);
            merchantInfo.put("username", username);
            merchantInfo.put("merchantName", "测试洗护店");
            merchantInfo.put("contactName", "张老板");
            merchantInfo.put("phone", "***********");
            merchantInfo.put("email", "<EMAIL>");
            merchantInfo.put("logo", "/images/merchant-logo.png");
            merchantInfo.put("status", "APPROVED");
            merchantInfo.put("businessLicense", "TEST_LICENSE_001");
            merchantInfo.put("address", "测试市测试区测试街道123号");
            merchantInfo.put("description", "专业的洗护服务商家");
            merchantInfo.put("rating", 4.8);
            merchantInfo.put("orderCount", 1250);
            merchantInfo.put("balance", 5680.50);
            merchantInfo.put("commissionRate", 0.05);
            
            Map<String, Object> data = new HashMap<>();
            data.put("token", "merchant_token_" + System.currentTimeMillis());
            data.put("merchantInfo", merchantInfo);
            data.put("loginTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            result.put("success", true);
            result.put("message", "登录成功");
            result.put("data", data);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "登录失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 商家注册
     */
    @PostMapping("/register")
    public Map<String, Object> register(@RequestBody Map<String, Object> registerData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String username = (String) registerData.get("username");
            String password = (String) registerData.get("password");
            String merchantName = (String) registerData.get("merchantName");
            String contactName = (String) registerData.get("contactName");
            String phone = (String) registerData.get("phone");
            String businessLicense = (String) registerData.get("businessLicense");
            
            // 模拟注册成功，状态为待审核
            Map<String, Object> merchantInfo = new HashMap<>();
            merchantInfo.put("id", System.currentTimeMillis());
            merchantInfo.put("username", username);
            merchantInfo.put("merchantName", merchantName);
            merchantInfo.put("contactName", contactName);
            merchantInfo.put("phone", phone);
            merchantInfo.put("businessLicense", businessLicense);
            merchantInfo.put("status", "PENDING");
            merchantInfo.put("rating", 5.0);
            merchantInfo.put("orderCount", 0);
            merchantInfo.put("balance", 0.00);
            merchantInfo.put("commissionRate", 0.05);
            
            Map<String, Object> data = new HashMap<>();
            data.put("token", "merchant_token_" + System.currentTimeMillis());
            data.put("merchantInfo", merchantInfo);
            
            result.put("success", true);
            result.put("message", "注册成功，请等待审核");
            result.put("data", data);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "注册失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取商家信息
     */
    @GetMapping("/info")
    public Map<String, Object> getMerchantInfo(@RequestHeader(value = "Authorization", required = false) String authorization) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查是否为超级管理员
            if (authorization != null && authorization.contains("super_admin_token")) {
                return createSuperAdminResponse("MERCHANT");
            }
            
            Map<String, Object> merchantInfo = new HashMap<>();
            merchantInfo.put("id", 2001L);
            merchantInfo.put("username", "testmerchant");
            merchantInfo.put("merchantName", "测试洗护店");
            merchantInfo.put("contactName", "张老板");
            merchantInfo.put("phone", "***********");
            merchantInfo.put("email", "<EMAIL>");
            merchantInfo.put("logo", "/images/merchant-logo.png");
            merchantInfo.put("status", "APPROVED");
            merchantInfo.put("businessLicense", "TEST_LICENSE_001");
            merchantInfo.put("address", "测试市测试区测试街道123号");
            merchantInfo.put("description", "专业的洗护服务商家");
            merchantInfo.put("rating", 4.8);
            merchantInfo.put("orderCount", 1250);
            merchantInfo.put("balance", 5680.50);
            merchantInfo.put("commissionRate", 0.05);
            
            result.put("success", true);
            result.put("data", merchantInfo);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取商家信息失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public Map<String, Object> logout() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "退出登录成功");
        return result;
    }

    /**
     * 超级管理员登录
     */
    @PostMapping("/super-login")
    public Map<String, Object> superLogin(@RequestBody Map<String, Object> loginData) {
        String username = (String) loginData.get("username");
        String password = (String) loginData.get("password");
        
        if ("superadmin".equals(username) && "admin123456".equals(password)) {
            return createSuperAdminResponse("MERCHANT");
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "超级管理员账号或密码错误");
        return result;
    }

    /**
     * 创建超级管理员响应
     */
    private Map<String, Object> createSuperAdminResponse(String userType) {
        Map<String, Object> result = new HashMap<>();
        
        Map<String, Object> merchantInfo = new HashMap<>();
        merchantInfo.put("id", 999999L);
        merchantInfo.put("username", "superadmin");
        merchantInfo.put("merchantName", "超级管理员商家");
        merchantInfo.put("contactName", "超级管理员");
        merchantInfo.put("phone", "***********");
        merchantInfo.put("email", "<EMAIL>");
        merchantInfo.put("logo", "/images/super-admin-logo.png");
        merchantInfo.put("status", "APPROVED");
        merchantInfo.put("businessLicense", "SUPER_ADMIN_LICENSE");
        merchantInfo.put("address", "超级管理员地址");
        merchantInfo.put("description", "超级管理员测试商家");
        merchantInfo.put("rating", 5.0);
        merchantInfo.put("orderCount", 99999);
        merchantInfo.put("balance", 999999.00);
        merchantInfo.put("commissionRate", 0.00);
        merchantInfo.put("isSuperAdmin", true);
        
        Map<String, Object> data = new HashMap<>();
        data.put("token", "super_admin_token_merchant_" + System.currentTimeMillis());
        data.put("merchantInfo", merchantInfo);
        data.put("loginTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        result.put("success", true);
        result.put("message", "超级管理员登录成功");
        result.put("data", data);
        
        return result;
    }
}
