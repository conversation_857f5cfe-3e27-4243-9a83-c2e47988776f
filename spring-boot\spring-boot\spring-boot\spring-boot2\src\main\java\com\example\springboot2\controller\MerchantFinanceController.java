package com.example.springboot2.controller;

import com.example.springboot2.service.MerchantFinanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.Map;

@RestController
@RequestMapping("/api/merchant/earnings")
@CrossOrigin(origins = "*")
public class MerchantFinanceController {

    @Autowired
    private MerchantFinanceService merchantFinanceService;

    /**
     * 获取收益概览
     */
    @GetMapping("/overview")
    public Map<String, Object> getEarningsOverview(@RequestParam Long merchantId) {
        return merchantFinanceService.getBalance(merchantId);
    }

    /**
     * 获取收益统计
     */
    @GetMapping("/stats")
    public Map<String, Object> getEarningsStats(
            @RequestParam Long merchantId,
            @RequestParam(defaultValue = "month") String period) {
        return merchantFinanceService.getEarningsStatistics(merchantId, period);
    }

    /**
     * 获取收益明细列表
     */
    @GetMapping("/list")
    public Map<String, Object> getEarningsList(
            @RequestParam Long merchantId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        return merchantFinanceService.getTransactionHistory(merchantId, page, size);
    }

    /**
     * 申请提现
     */
    @PostMapping("/withdraw")
    public Map<String, Object> requestWithdrawal(
            @RequestParam Long merchantId,
            @RequestBody Map<String, Object> withdrawalData) {
        return merchantFinanceService.requestWithdrawal(merchantId, withdrawalData);
    }

    /**
     * 获取提现记录
     */
    @GetMapping("/withdraw-records")
    public Map<String, Object> getWithdrawRecords(
            @RequestParam Long merchantId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        return merchantFinanceService.getWithdrawalHistory(merchantId, page, size);
    }

    /**
     * 获取银行账户列表
     */
    @GetMapping("/bank-accounts")
    public Map<String, Object> getBankAccounts(@RequestParam Long merchantId) {
        return merchantFinanceService.getBankAccounts(merchantId);
    }
}
