package com.example.springboot2.controller;

import com.example.springboot2.service.SuperAdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.Map;

/**
 * 超级管理员控制器
 * 支持登录所有端：用户端(8081)、商家端(8082)、管理端(8080)
 */
@RestController
@CrossOrigin(origins = "*")
public class SuperAdminController {

    @Autowired
    private SuperAdminService superAdminService;

    /**
     * 用户端超级管理员登录 (端口8081)
     */
    @PostMapping("/api/user/super-login")
    public Map<String, Object> userSuperLogin(@RequestBody Map<String, Object> loginData) {
        String username = (String) loginData.get("username");
        String password = (String) loginData.get("password");
        
        return superAdminService.superAdminLogin(username, password, "USER");
    }

    /**
     * 商家端超级管理员登录 (端口8082)
     */
    @PostMapping("/api/merchant/super-login")
    public Map<String, Object> merchantSuperLogin(@RequestBody Map<String, Object> loginData) {
        String username = (String) loginData.get("username");
        String password = (String) loginData.get("password");
        
        return superAdminService.superAdminLogin(username, password, "MERCHANT");
    }

    /**
     * 管理端超级管理员登录 (端口8080)
     */
    @PostMapping("/api/admin/super-login")
    public Map<String, Object> adminSuperLogin(@RequestBody Map<String, Object> loginData) {
        String username = (String) loginData.get("username");
        String password = (String) loginData.get("password");
        
        return superAdminService.superAdminLogin(username, password, "ADMIN");
    }

    /**
     * 获取超级管理员信息 (通用接口)
     */
    @GetMapping("/api/*/super-admin/info")
    public Map<String, Object> getSuperAdminInfo(@RequestHeader("Authorization") String authorization) {
        String token = authorization.replace("Bearer ", "");
        return superAdminService.getSuperAdminInfo(token);
    }

    /**
     * 获取超级管理员统计信息
     */
    @GetMapping("/api/*/super-admin/stats")
    public Map<String, Object> getSuperAdminStats(@RequestHeader("Authorization") String authorization) {
        String token = authorization.replace("Bearer ", "");
        
        if (!superAdminService.validateSuperAdminToken(token)) {
            return Map.of("success", false, "message", "无效的token");
        }
        
        return superAdminService.getSuperAdminStats();
    }

    /**
     * 超级管理员权限验证
     */
    @PostMapping("/api/*/super-admin/verify")
    public Map<String, Object> verifySuperAdmin(
            @RequestHeader("Authorization") String authorization,
            @RequestBody Map<String, Object> verifyData) {
        
        String token = authorization.replace("Bearer ", "");
        String permission = (String) verifyData.get("permission");
        
        boolean hasPermission = superAdminService.hasSuperAdminPermission(token, permission);
        
        return Map.of(
            "success", true,
            "hasPermission", hasPermission,
            "isSuperAdmin", superAdminService.validateSuperAdminToken(token)
        );
    }
}
