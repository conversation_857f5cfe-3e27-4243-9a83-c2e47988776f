package com.example.springboot2.controller;

import org.springframework.web.bind.annotation.*;
import java.util.Map;
import java.util.HashMap;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/api/user")
@CrossOrigin(origins = "*")
public class UserAuthController {

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Map<String, Object> login(@RequestBody Map<String, Object> loginData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String username = (String) loginData.get("username");
            String password = (String) loginData.get("password");
            String phone = (String) loginData.get("phone");
            String loginType = (String) loginData.get("loginType");
            
            // 超级管理员登录检查
            if ("superadmin".equals(username) && "admin123456".equals(password)) {
                return createSuperAdminResponse("USER");
            }
            
            // 模拟用户登录验证
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", 1001L);
            userInfo.put("username", username != null ? username : phone);
            userInfo.put("nickname", "测试用户");
            userInfo.put("phone", phone != null ? phone : "13800138000");
            userInfo.put("avatar", "/images/default-avatar.png");
            userInfo.put("status", "ACTIVE");
            userInfo.put("vipLevel", "NORMAL");
            userInfo.put("balance", 100.00);
            userInfo.put("points", 500);
            
            Map<String, Object> data = new HashMap<>();
            data.put("token", "user_token_" + System.currentTimeMillis());
            data.put("userInfo", userInfo);
            data.put("loginTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            result.put("success", true);
            result.put("message", "登录成功");
            result.put("data", data);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "登录失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Map<String, Object> register(@RequestBody Map<String, Object> registerData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String phone = (String) registerData.get("phone");
            String password = (String) registerData.get("password");
            String nickname = (String) registerData.get("nickname");
            String code = (String) registerData.get("code");
            
            // 模拟注册成功
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", System.currentTimeMillis());
            userInfo.put("username", phone);
            userInfo.put("nickname", nickname);
            userInfo.put("phone", phone);
            userInfo.put("avatar", "/images/default-avatar.png");
            userInfo.put("status", "ACTIVE");
            userInfo.put("vipLevel", "NORMAL");
            userInfo.put("balance", 0.00);
            userInfo.put("points", 100);
            
            Map<String, Object> data = new HashMap<>();
            data.put("token", "user_token_" + System.currentTimeMillis());
            data.put("userInfo", userInfo);
            
            result.put("success", true);
            result.put("message", "注册成功");
            result.put("data", data);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "注册失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/info")
    public Map<String, Object> getUserInfo(@RequestHeader(value = "Authorization", required = false) String authorization) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查是否为超级管理员
            if (authorization != null && authorization.contains("super_admin_token")) {
                return createSuperAdminResponse("USER");
            }
            
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", 1001L);
            userInfo.put("username", "testuser");
            userInfo.put("nickname", "测试用户");
            userInfo.put("phone", "13800138000");
            userInfo.put("avatar", "/images/default-avatar.png");
            userInfo.put("status", "ACTIVE");
            userInfo.put("vipLevel", "NORMAL");
            userInfo.put("balance", 100.00);
            userInfo.put("points", 500);
            
            result.put("success", true);
            result.put("data", userInfo);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取用户信息失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 微信登录
     */
    @PostMapping("/wx-login")
    public Map<String, Object> wxLogin(@RequestBody Map<String, Object> wxData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String code = (String) wxData.get("code");
            Map<String, Object> userInfo = (Map<String, Object>) wxData.get("userInfo");
            
            // 模拟微信登录
            Map<String, Object> user = new HashMap<>();
            user.put("id", System.currentTimeMillis());
            user.put("username", "wx_" + System.currentTimeMillis());
            user.put("nickname", userInfo != null ? userInfo.get("nickName") : "微信用户");
            user.put("avatar", userInfo != null ? userInfo.get("avatarUrl") : "/images/default-avatar.png");
            user.put("status", "ACTIVE");
            user.put("vipLevel", "NORMAL");
            user.put("balance", 0.00);
            user.put("points", 100);
            
            Map<String, Object> data = new HashMap<>();
            data.put("token", "wx_token_" + System.currentTimeMillis());
            data.put("userInfo", user);
            
            result.put("success", true);
            result.put("message", "微信登录成功");
            result.put("data", data);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "微信登录失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public Map<String, Object> logout() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "退出登录成功");
        return result;
    }

    /**
     * 超级管理员登录
     */
    @PostMapping("/super-login")
    public Map<String, Object> superLogin(@RequestBody Map<String, Object> loginData) {
        String username = (String) loginData.get("username");
        String password = (String) loginData.get("password");
        
        if ("superadmin".equals(username) && "admin123456".equals(password)) {
            return createSuperAdminResponse("USER");
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "超级管理员账号或密码错误");
        return result;
    }

    /**
     * 创建超级管理员响应
     */
    private Map<String, Object> createSuperAdminResponse(String userType) {
        Map<String, Object> result = new HashMap<>();
        
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", 999999L);
        userInfo.put("username", "superadmin");
        userInfo.put("nickname", "超级管理员");
        userInfo.put("phone", "13800000000");
        userInfo.put("avatar", "/images/super-admin-avatar.png");
        userInfo.put("userType", "SUPER_ADMIN");
        userInfo.put("status", "ACTIVE");
        userInfo.put("vipLevel", "DIAMOND");
        userInfo.put("balance", 999999.00);
        userInfo.put("points", 999999);
        userInfo.put("isSuperAdmin", true);
        
        Map<String, Object> data = new HashMap<>();
        data.put("token", "super_admin_token_user_" + System.currentTimeMillis());
        data.put("userInfo", userInfo);
        data.put("loginTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        result.put("success", true);
        result.put("message", "超级管理员登录成功");
        result.put("data", data);
        
        return result;
    }
}
