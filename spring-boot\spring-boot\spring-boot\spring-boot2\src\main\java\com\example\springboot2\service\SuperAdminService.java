package com.example.springboot2.service;

import org.springframework.stereotype.Service;
import java.util.Map;
import java.util.HashMap;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Service
public class SuperAdminService {
    
    // 超级管理员配置
    private static final String SUPER_ADMIN_USERNAME = "superadmin";
    private static final String SUPER_ADMIN_PASSWORD = "admin123456";
    private static final String SUPER_ADMIN_TOKEN = "super_admin_token_2024";
    
    /**
     * 超级管理员登录
     * 可以登录所有端：用户端、商家端、管理端
     */
    public Map<String, Object> superAdminLogin(String username, String password, String loginType) {
        Map<String, Object> result = new HashMap<>();
        
        // 验证超级管理员账号
        if (!SUPER_ADMIN_USERNAME.equals(username) || !SUPER_ADMIN_PASSWORD.equals(password)) {
            result.put("success", false);
            result.put("message", "用户名或密码错误");
            return result;
        }
        
        // 根据登录类型返回不同的用户信息
        Map<String, Object> userInfo = createSuperAdminInfo(loginType);
        
        Map<String, Object> data = new HashMap<>();
        data.put("token", SUPER_ADMIN_TOKEN + "_" + loginType.toLowerCase());
        data.put("userInfo", userInfo);
        data.put("loginTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        result.put("success", true);
        result.put("message", "登录成功");
        result.put("data", data);
        
        return result;
    }
    
    /**
     * 创建超级管理员信息
     */
    private Map<String, Object> createSuperAdminInfo(String loginType) {
        Map<String, Object> userInfo = new HashMap<>();
        
        switch (loginType.toUpperCase()) {
            case "USER":
                // 用户端超级管理员
                userInfo.put("id", 999999L);
                userInfo.put("username", "superadmin");
                userInfo.put("nickname", "超级管理员");
                userInfo.put("phone", "***********");
                userInfo.put("avatar", "/images/super-admin-avatar.png");
                userInfo.put("userType", "SUPER_ADMIN");
                userInfo.put("status", "ACTIVE");
                userInfo.put("vipLevel", "DIAMOND");
                break;
                
            case "MERCHANT":
                // 商家端超级管理员
                userInfo.put("id", 999999L);
                userInfo.put("username", "superadmin");
                userInfo.put("merchantName", "超级管理员商家");
                userInfo.put("contactName", "超级管理员");
                userInfo.put("phone", "***********");
                userInfo.put("email", "<EMAIL>");
                userInfo.put("logo", "/images/super-admin-logo.png");
                userInfo.put("status", "APPROVED");
                userInfo.put("businessLicense", "SUPER_ADMIN_LICENSE");
                userInfo.put("address", "超级管理员地址");
                userInfo.put("description", "超级管理员测试商家");
                break;
                
            case "ADMIN":
                // 管理端超级管理员
                userInfo.put("id", 999999L);
                userInfo.put("username", "superadmin");
                userInfo.put("realName", "超级管理员");
                userInfo.put("phone", "***********");
                userInfo.put("email", "<EMAIL>");
                userInfo.put("avatar", "/images/super-admin-avatar.png");
                userInfo.put("role", "SUPER_ADMIN");
                userInfo.put("permissions", new String[]{"*"}); // 所有权限
                userInfo.put("status", "ACTIVE");
                userInfo.put("department", "系统管理部");
                break;
                
            default:
                // 默认管理员信息
                userInfo.put("id", 999999L);
                userInfo.put("username", "superadmin");
                userInfo.put("nickname", "超级管理员");
                userInfo.put("userType", "SUPER_ADMIN");
                userInfo.put("status", "ACTIVE");
        }
        
        // 公共信息
        userInfo.put("createTime", "2024-01-01 00:00:00");
        userInfo.put("lastLoginTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        userInfo.put("isSuperAdmin", true);
        
        return userInfo;
    }
    
    /**
     * 验证超级管理员token
     */
    public boolean validateSuperAdminToken(String token) {
        return token != null && token.startsWith(SUPER_ADMIN_TOKEN);
    }
    
    /**
     * 获取超级管理员信息
     */
    public Map<String, Object> getSuperAdminInfo(String token) {
        Map<String, Object> result = new HashMap<>();
        
        if (!validateSuperAdminToken(token)) {
            result.put("success", false);
            result.put("message", "无效的token");
            return result;
        }
        
        // 从token中提取登录类型
        String loginType = "ADMIN"; // 默认
        if (token.contains("_user")) {
            loginType = "USER";
        } else if (token.contains("_merchant")) {
            loginType = "MERCHANT";
        } else if (token.contains("_admin")) {
            loginType = "ADMIN";
        }
        
        Map<String, Object> userInfo = createSuperAdminInfo(loginType);
        
        result.put("success", true);
        result.put("data", userInfo);
        
        return result;
    }
    
    /**
     * 超级管理员权限检查
     */
    public boolean hasSuperAdminPermission(String token, String permission) {
        return validateSuperAdminToken(token); // 超级管理员拥有所有权限
    }
    
    /**
     * 获取超级管理员统计信息
     */
    public Map<String, Object> getSuperAdminStats() {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> stats = new HashMap<>();
        
        // 模拟统计数据
        stats.put("totalUsers", 10000);
        stats.put("totalMerchants", 500);
        stats.put("totalOrders", 50000);
        stats.put("totalRevenue", 1000000.00);
        stats.put("activeUsers", 8500);
        stats.put("activeMerchants", 450);
        stats.put("todayOrders", 150);
        stats.put("todayRevenue", 15000.00);
        
        result.put("success", true);
        result.put("data", stats);
        
        return result;
    }
}
