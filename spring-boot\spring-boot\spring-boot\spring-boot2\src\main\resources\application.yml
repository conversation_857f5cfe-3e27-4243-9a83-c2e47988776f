# Spring Boot 应用配置
server:
  port: 8080

spring:
  application:
    name: laundry-platform
  
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

# 日志配置
logging:
  level:
    com.example.springboot2: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# JWT配置
jwt:
  secret: laundry-platform-secret-key-2024
  expiration: 86400000  # 24小时

# 文件上传配置
file:
  upload:
    path: /uploads/
    max-size: 10MB

# 短信配置
sms:
  access-key: your-access-key
  secret-key: your-secret-key
  sign-name: 洗护平台
  template-code: SMS_123456789

# 支付配置
pay:
  wechat:
    app-id: your-wechat-app-id
    mch-id: your-mch-id
    key: your-wechat-pay-key
  alipay:
    app-id: your-alipay-app-id
    private-key: your-alipay-private-key
    public-key: your-alipay-public-key
