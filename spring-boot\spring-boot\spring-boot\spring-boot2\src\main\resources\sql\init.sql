-- 洗护平台数据库初始化脚本 (SQL Server版本)

-- 创建数据库
IF NOT EXISTS (SELECT * FROM sys.databases WHERE name = 'laundry_platform')
BEGIN
    CREATE DATABASE laundry_platform;
END
GO

USE laundry_platform;
GO

-- 用户表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
BEGIN
    CREATE TABLE users (
        id BIGINT IDENTITY(1,1) PRIMARY KEY,
        username NVARCHAR(50) UNIQUE NOT NULL,
        password NVARCHAR(255) NOT NULL,
        nickname NVARCHAR(50),
        phone NVARCHAR(20) UNIQUE,
        email NVARCHAR(100),
        avatar NVARCHAR(255),
        gender NVARCHAR(20) DEFAULT 'UNKNOWN' CHECK (gender IN ('MALE', 'FEMALE', 'UNKNOWN')),
        birthday DATE,
        status NVARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE', 'BANNED')),
        vip_level NVARCHAR(20) DEFAULT 'NORMAL' CHECK (vip_level IN ('NORMAL', 'SILVER', 'GOLD', 'DIAMOND')),
        balance DECIMAL(10,2) DEFAULT 0.00,
        points INT DEFAULT 0,
        wx_openid NVARCHAR(100),
        wx_unionid NVARCHAR(100),
        last_login_time DATETIME,
        create_time DATETIME DEFAULT GETDATE(),
        update_time DATETIME DEFAULT GETDATE()
    );
END
GO

-- 商家表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='merchants' AND xtype='U')
BEGIN
    CREATE TABLE merchants (
        id BIGINT IDENTITY(1,1) PRIMARY KEY,
        username NVARCHAR(50) UNIQUE NOT NULL,
        password NVARCHAR(255) NOT NULL,
        merchant_name NVARCHAR(100) NOT NULL,
        contact_name NVARCHAR(50) NOT NULL,
        phone NVARCHAR(20) NOT NULL,
        email NVARCHAR(100),
        logo NVARCHAR(255),
        business_license NVARCHAR(100) UNIQUE NOT NULL,
        address NVARCHAR(255) NOT NULL,
        latitude DECIMAL(10,8),
        longitude DECIMAL(11,8),
        description NTEXT,
        status NVARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'APPROVED', 'REJECTED', 'SUSPENDED')),
        reject_reason NTEXT,
        rating DECIMAL(3,2) DEFAULT 5.00,
        order_count INT DEFAULT 0,
        balance DECIMAL(10,2) DEFAULT 0.00,
        commission_rate DECIMAL(5,4) DEFAULT 0.0500,
        create_time DATETIME DEFAULT GETDATE(),
        update_time DATETIME DEFAULT GETDATE()
    );
END
GO

-- 管理员表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='admins' AND xtype='U')
BEGIN
    CREATE TABLE admins (
        id BIGINT IDENTITY(1,1) PRIMARY KEY,
        username NVARCHAR(50) UNIQUE NOT NULL,
        password NVARCHAR(255) NOT NULL,
        real_name NVARCHAR(50) NOT NULL,
        phone NVARCHAR(20),
        email NVARCHAR(100),
        avatar NVARCHAR(255),
        role NVARCHAR(20) DEFAULT 'ADMIN' CHECK (role IN ('SUPER_ADMIN', 'ADMIN', 'OPERATOR')),
        permissions NTEXT,
        status NVARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE')),
        department NVARCHAR(100),
        last_login_time DATETIME,
        create_time DATETIME DEFAULT GETDATE(),
        update_time DATETIME DEFAULT GETDATE()
    );
END
GO

-- 服务分类表
CREATE TABLE IF NOT EXISTS service_categories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    icon VARCHAR(255),
    description TEXT,
    sort_order INT DEFAULT 0,
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 服务表
CREATE TABLE IF NOT EXISTS services (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    merchant_id BIGINT NOT NULL,
    category_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    unit VARCHAR(20) DEFAULT '件',
    images JSON,
    tags JSON,
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE',
    sort_order INT DEFAULT 0,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES service_categories(id)
);

-- 地址表
CREATE TABLE IF NOT EXISTS addresses (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    contact_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    province VARCHAR(50) NOT NULL,
    city VARCHAR(50) NOT NULL,
    district VARCHAR(50) NOT NULL,
    detail_address VARCHAR(255) NOT NULL,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    is_default BOOLEAN DEFAULT FALSE,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 订单表
CREATE TABLE IF NOT EXISTS orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_no VARCHAR(32) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    merchant_id BIGINT NOT NULL,
    service_id BIGINT NOT NULL,
    address_id BIGINT NOT NULL,
    service_name VARCHAR(100) NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    commission_amount DECIMAL(10,2) NOT NULL,
    merchant_amount DECIMAL(10,2) NOT NULL,
    status ENUM('PENDING', 'ACCEPTED', 'REJECTED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED') DEFAULT 'PENDING',
    pickup_time DATETIME,
    delivery_time DATETIME,
    notes TEXT,
    reject_reason TEXT,
    rating INT,
    review TEXT,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (merchant_id) REFERENCES merchants(id),
    FOREIGN KEY (service_id) REFERENCES services(id),
    FOREIGN KEY (address_id) REFERENCES addresses(id)
);

-- 轮播图表
CREATE TABLE IF NOT EXISTS banners (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(100),
    image VARCHAR(255) NOT NULL,
    link_type ENUM('NONE', 'URL', 'SERVICE', 'MERCHANT') DEFAULT 'NONE',
    link_value VARCHAR(255),
    sort_order INT DEFAULT 0,
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE',
    start_time DATETIME,
    end_time DATETIME,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入超级管理员账号
INSERT INTO admins (username, password, real_name, phone, email, role, status, department) 
VALUES ('superadmin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyiLZOiCsIrKQDQIfeUROjkR6S6', '超级管理员', '13800000000', '<EMAIL>', 'SUPER_ADMIN', 'ACTIVE', '系统管理部')
ON DUPLICATE KEY UPDATE password = '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyiLZOiCsIrKQDQIfeUROjkR6S6';

-- 插入默认服务分类
INSERT INTO service_categories (name, icon, description, sort_order) VALUES
('衣物清洗', '/images/category-wash.png', '日常衣物清洗服务', 1),
('干洗服务', '/images/category-dry.png', '高档衣物干洗服务', 2),
('鞋类护理', '/images/category-shoes.png', '各类鞋子清洗护理', 3),
('家纺清洗', '/images/category-home.png', '床单被套等家纺清洗', 4),
('特殊护理', '/images/category-special.png', '皮具、奢侈品护理', 5),
('上门服务', '/images/category-door.png', '上门取送服务', 6)
ON DUPLICATE KEY UPDATE name = VALUES(name);

-- 插入默认轮播图
INSERT INTO banners (title, image, link_type, sort_order, status) VALUES
('专业洗护服务', '/images/banner1.jpg', 'NONE', 1, 'ACTIVE'),
('品质保证', '/images/banner2.jpg', 'NONE', 2, 'ACTIVE'),
('便民服务', '/images/banner3.jpg', 'NONE', 3, 'ACTIVE')
ON DUPLICATE KEY UPDATE title = VALUES(title);
