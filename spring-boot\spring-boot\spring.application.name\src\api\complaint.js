import request from '@/utils/request'

// 管理端投诉相关API
export const complaintApi = {
  // 获取投诉列表
  getComplaints(params) {
    return request({
      url: '/admin/complaints',
      method: 'get',
      params
    })
  },

  // 获取投诉详情
  getComplaintDetail(id) {
    return request({
      url: `/admin/complaints/${id}`,
      method: 'get'
    })
  },

  // 处理投诉
  handleComplaint(id, data) {
    return request({
      url: `/admin/complaints/${id}/handle`,
      method: 'post',
      data
    })
  },

  // 审核投诉处理结果
  auditComplaint(id, data) {
    return request({
      url: `/admin/complaints/${id}/audit`,
      method: 'post',
      data
    })
  },

  // 关闭投诉
  closeComplaint(id, data) {
    return request({
      url: `/admin/complaints/${id}/close`,
      method: 'post',
      data
    })
  },

  // 重新打开投诉
  reopenComplaint(id, data) {
    return request({
      url: `/admin/complaints/${id}/reopen`,
      method: 'post',
      data
    })
  },

  // 获取投诉统计
  getComplaintStats() {
    return request({
      url: '/admin/complaints/stats',
      method: 'get'
    })
  },

  // 获取投诉趋势数据
  getComplaintTrends(params) {
    return request({
      url: '/admin/complaints/trends',
      method: 'get',
      params
    })
  },

  // 获取投诉分类统计
  getComplaintCategoryStats() {
    return request({
      url: '/admin/complaints/category-stats',
      method: 'get'
    })
  },

  // 获取商家投诉排行
  getMerchantComplaintRanking(params) {
    return request({
      url: '/admin/complaints/merchant-ranking',
      method: 'get',
      params
    })
  },

  // 批量处理投诉
  batchHandleComplaints(data) {
    return request({
      url: '/admin/complaints/batch-handle',
      method: 'post',
      data
    })
  },

  // 导出投诉数据
  exportComplaints(params) {
    return request({
      url: '/admin/complaints/export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  },

  // 上传处理凭证
  uploadEvidence(file) {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', 'complaint_evidence')
    
    return request({
      url: '/common/upload',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取投诉处理记录
  getComplaintRecords(id) {
    return request({
      url: `/admin/complaints/${id}/records`,
      method: 'get'
    })
  },

  // 添加投诉备注
  addComplaintNote(id, data) {
    return request({
      url: `/admin/complaints/${id}/notes`,
      method: 'post',
      data
    })
  },

  // 分配投诉处理人
  assignComplaint(id, data) {
    return request({
      url: `/admin/complaints/${id}/assign`,
      method: 'post',
      data
    })
  },

  // 获取可分配的处理人列表
  getAssignableUsers() {
    return request({
      url: '/admin/complaints/assignable-users',
      method: 'get'
    })
  }
}

export default complaintApi
