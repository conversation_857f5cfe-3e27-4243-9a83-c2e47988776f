<template>
  <div class="complaint-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">投诉管理</h1>
        <div class="header-actions">
          <el-button @click="exportData">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
          <el-button type="primary" @click="showStatsDialog = true">
            <el-icon><DataAnalysis /></el-icon>
            数据统计
          </el-button>
        </div>
      </div>
      
      <!-- 统计卡片 -->
      <div class="stats-cards">
        <el-row :gutter="20">
          <el-col :span="6" v-for="stat in stats" :key="stat.key">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-value" :style="{ color: stat.color }">{{ stat.value }}</div>
                <div class="stat-title">{{ stat.title }}</div>
                <div class="stat-trend" :class="stat.trend > 0 ? 'up' : 'down'">
                  <el-icon><TrendCharts /></el-icon>
                  {{ Math.abs(stat.trend) }}%
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <el-card>
        <el-form :model="filterForm" inline>
          <el-form-item label="投诉状态">
            <el-select v-model="filterForm.status" placeholder="全部状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="待处理" value="pending" />
              <el-option label="处理中" value="processing" />
              <el-option label="已解决" value="resolved" />
              <el-option label="已关闭" value="closed" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="投诉类型">
            <el-select v-model="filterForm.type" placeholder="全部类型" clearable>
              <el-option label="全部" value="" />
              <el-option label="服务质量" value="service" />
              <el-option label="配送问题" value="delivery" />
              <el-option label="价格争议" value="price" />
              <el-option label="服务态度" value="attitude" />
              <el-option label="其他问题" value="other" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="处理人">
            <el-select v-model="filterForm.assignee" placeholder="全部处理人" clearable>
              <el-option label="全部" value="" />
              <el-option label="未分配" value="unassigned" />
              <el-option
                v-for="user in assignableUsers"
                :key="user.id"
                :label="user.name"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          
          <el-form-item label="关键词">
            <el-input
              v-model="filterForm.keyword"
              placeholder="搜索投诉标题、订单号"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="searchComplaints">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 投诉列表 -->
    <div class="complaint-list">
      <el-card>
        <div class="table-header">
          <div class="batch-actions">
            <el-checkbox
              v-model="selectAll"
              :indeterminate="isIndeterminate"
              @change="handleSelectAll"
            >
              全选
            </el-checkbox>
            <el-button
              v-if="selectedComplaints.length > 0"
              type="primary"
              @click="showBatchHandleDialog = true"
            >
              批量处理 ({{ selectedComplaints.length }})
            </el-button>
          </div>
        </div>
        
        <el-table
          v-loading="loading"
          :data="complaints"
          stripe
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="id" label="投诉编号" width="120" />
          <el-table-column prop="title" label="投诉标题" min-width="200" />
          <el-table-column prop="orderId" label="订单号" width="150" />
          <el-table-column prop="typeText" label="投诉类型" width="120" />
          <el-table-column prop="customerName" label="客户" width="120" />
          <el-table-column prop="merchantName" label="商家" width="150" />
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="优先级" width="100">
            <template #default="{ row }">
              <el-tag :type="getPriorityType(row.priority)" size="small">
                {{ getPriorityText(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="assigneeName" label="处理人" width="120" />
          <el-table-column prop="createTime" label="提交时间" width="180" />
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="viewComplaint(row)"
              >
                查看详情
              </el-button>
              <el-button
                v-if="row.status === 'pending'"
                type="success"
                size="small"
                @click="handleComplaint(row)"
              >
                处理
              </el-button>
              <el-button
                v-if="row.status === 'pending' && !row.assigneeId"
                type="warning"
                size="small"
                @click="assignComplaint(row)"
              >
                分配
              </el-button>
              <el-dropdown v-if="row.status !== 'closed'">
                <el-button size="small">
                  更多<el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="addNote(row)">添加备注</el-dropdown-item>
                    <el-dropdown-item @click="closeComplaint(row)">关闭投诉</el-dropdown-item>
                    <el-dropdown-item v-if="row.status === 'closed'" @click="reopenComplaint(row)">重新打开</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 投诉详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="投诉详情"
      width="900px"
      :before-close="closeDetailDialog"
    >
      <div v-if="currentComplaint" class="complaint-detail">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3>基本信息</h3>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="投诉编号">{{ currentComplaint.id }}</el-descriptions-item>
            <el-descriptions-item label="订单号">{{ currentComplaint.orderId }}</el-descriptions-item>
            <el-descriptions-item label="投诉类型">{{ currentComplaint.typeText }}</el-descriptions-item>
            <el-descriptions-item label="客户姓名">{{ currentComplaint.customerName }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ currentComplaint.customerPhone }}</el-descriptions-item>
            <el-descriptions-item label="商家名称">{{ currentComplaint.merchantName }}</el-descriptions-item>
            <el-descriptions-item label="当前状态">
              <el-tag :type="getStatusType(currentComplaint.status)">
                {{ getStatusText(currentComplaint.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="优先级">
              <el-tag :type="getPriorityType(currentComplaint.priority)">
                {{ getPriorityText(currentComplaint.priority) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="处理人">{{ currentComplaint.assigneeName || '未分配' }}</el-descriptions-item>
            <el-descriptions-item label="提交时间">{{ currentComplaint.createTime }}</el-descriptions-item>
            <el-descriptions-item label="最后更新">{{ currentComplaint.updateTime }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 投诉内容 -->
        <div class="detail-section">
          <h3>投诉内容</h3>
          <div class="complaint-content">{{ currentComplaint.content }}</div>
          
          <!-- 投诉图片 -->
          <div v-if="currentComplaint.images && currentComplaint.images.length > 0" class="complaint-images">
            <h4>相关图片</h4>
            <div class="image-gallery">
              <el-image
                v-for="(image, index) in currentComplaint.images"
                :key="index"
                :src="image"
                :preview-src-list="currentComplaint.images"
                :initial-index="index"
                class="complaint-image"
                fit="cover"
              />
            </div>
          </div>
        </div>

        <!-- 处理记录 -->
        <div v-if="currentComplaint.processRecords && currentComplaint.processRecords.length > 0" class="detail-section">
          <h3>处理记录</h3>
          <el-timeline>
            <el-timeline-item
              v-for="(record, index) in currentComplaint.processRecords"
              :key="index"
              :timestamp="record.time"
              :type="record.type"
            >
              <div class="process-record">
                <div class="record-title">{{ record.title }}</div>
                <div class="record-content">{{ record.content }}</div>
                <div class="record-operator">处理人：{{ record.operator }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDetailDialog">关闭</el-button>
          <el-button
            v-if="currentComplaint && currentComplaint.status === 'pending'"
            type="primary"
            @click="showHandleDialog = true"
          >
            立即处理
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 其他对话框省略，内容类似商家端但功能更全面 -->
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Download, DataAnalysis, TrendCharts, ArrowDown } from '@element-plus/icons-vue'
import { complaintApi } from '@/api/complaint'

// 响应式数据
const loading = ref(false)
const complaints = ref([])
const currentComplaint = ref(null)
const showDetailDialog = ref(false)
const showStatsDialog = ref(false)
const showBatchHandleDialog = ref(false)
const assignableUsers = ref([])
const selectedComplaints = ref([])
const selectAll = ref(false)

// 统计数据
const stats = ref([
  { key: 'total', title: '总投诉数', value: 0, color: '#409eff', trend: 5.2 },
  { key: 'pending', title: '待处理', value: 0, color: '#e6a23c', trend: -2.1 },
  { key: 'processing', title: '处理中', value: 0, color: '#409eff', trend: 8.7 },
  { key: 'resolved', title: '已解决', value: 0, color: '#67c23a', trend: 12.3 }
])

// 筛选表单
const filterForm = reactive({
  status: '',
  type: '',
  assignee: '',
  dateRange: null,
  keyword: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 计算属性
const isIndeterminate = computed(() => {
  const selectedCount = selectedComplaints.value.length
  return selectedCount > 0 && selectedCount < complaints.value.length
})

// 生命周期
onMounted(() => {
  fetchComplaints()
  fetchStats()
  fetchAssignableUsers()
})

// 方法定义
const fetchComplaints = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...filterForm
    }

    const response = await complaintApi.getComplaints(params)
    const { data, total } = response.data
    
    complaints.value = data
    pagination.total = total
    
  } catch (error) {
    console.error('获取投诉列表失败:', error)
    ElMessage.error('获取投诉列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const fetchStats = async () => {
  try {
    const response = await complaintApi.getComplaintStats()
    const data = response.data
    
    stats.value.forEach(stat => {
      stat.value = data[stat.key] || 0
    })
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取可分配用户
const fetchAssignableUsers = async () => {
  try {
    const response = await complaintApi.getAssignableUsers()
    assignableUsers.value = response.data
  } catch (error) {
    console.error('获取可分配用户失败:', error)
  }
}

// 搜索投诉
const searchComplaints = () => {
  pagination.page = 1
  fetchComplaints()
}

// 重置筛选
const resetFilter = () => {
  Object.assign(filterForm, {
    status: '',
    type: '',
    assignee: '',
    dateRange: null,
    keyword: ''
  })
  searchComplaints()
}

// 查看投诉详情
const viewComplaint = async (complaint) => {
  try {
    const response = await complaintApi.getComplaintDetail(complaint.id)
    currentComplaint.value = response.data
    showDetailDialog.value = true
  } catch (error) {
    ElMessage.error('获取投诉详情失败')
  }
}

// 处理投诉
const handleComplaint = (complaint) => {
  // 处理逻辑
}

// 分配投诉
const assignComplaint = (complaint) => {
  // 分配逻辑
}

// 关闭详情对话框
const closeDetailDialog = () => {
  showDetailDialog.value = false
  currentComplaint.value = null
}

// 选择处理
const handleSelectionChange = (selection) => {
  selectedComplaints.value = selection
}

const handleSelectAll = (val) => {
  // 全选逻辑
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.page = 1
  fetchComplaints()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchComplaints()
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    pending: 'warning',
    processing: 'primary',
    resolved: 'success',
    closed: 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: '待处理',
    processing: '处理中',
    resolved: '已解决',
    closed: '已关闭'
  }
  return statusMap[status] || '未知'
}

// 获取优先级类型
const getPriorityType = (priority) => {
  const priorityMap = {
    high: 'danger',
    medium: 'warning',
    low: 'success'
  }
  return priorityMap[priority] || 'info'
}

// 获取优先级文本
const getPriorityText = (priority) => {
  const priorityMap = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return priorityMap[priority] || '普通'
}

// 导出数据
const exportData = async () => {
  try {
    const response = await complaintApi.exportComplaints(filterForm)
    // 处理文件下载
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `投诉数据_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('数据导出成功')
  } catch (error) {
    ElMessage.error('数据导出失败')
  }
}
</script>

<style scoped>
.complaint-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  position: relative;
}

.stat-value {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 8px;
}

.stat-title {
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
}

.stat-trend {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.stat-trend.up {
  color: #67c23a;
}

.stat-trend.down {
  color: #f56c6c;
}

.filter-section {
  margin-bottom: 20px;
}

.complaint-list {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.complaint-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.complaint-content {
  line-height: 1.8;
  color: #606266;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.complaint-images h4 {
  margin: 16px 0 12px 0;
  font-size: 14px;
  color: #303133;
}

.image-gallery {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.complaint-image {
  width: 100px;
  height: 100px;
  border-radius: 8px;
  cursor: pointer;
}

.process-record {
  margin-bottom: 8px;
}

.record-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.record-content {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 4px;
}

.record-operator {
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
