@echo off
chcp 65001
echo ========================================
echo 启动洗护平台所有服务
echo ========================================

echo.
echo 正在启动后端服务...
echo ----------------------------------------

echo 启动管理端后端 (端口8080)...
cd /d "I:\spring-boot\spring-boot\spring-boot\Spring-boot-vue"
start "管理端后端" cmd /k "mvn spring-boot:run -Dspring-boot.run.profiles=admin"

timeout /t 10

echo 启动用户端后端 (端口8081)...
start "用户端后端" cmd /k "mvn spring-boot:run -Dspring-boot.run.profiles=user"

timeout /t 10

echo 启动商家端后端 (端口8082)...
start "商家端后端" cmd /k "mvn spring-boot:run -Dspring-boot.run.profiles=merchant"

echo.
echo 等待后端服务启动完成...
timeout /t 30

echo.
echo 正在启动前端服务...
echo ----------------------------------------

echo 启动用户端前端 (端口5173)...
cd /d "I:\spring-boot\spring-boot\spring-boot\my-vue"
start "用户端前端" cmd /k "npm run dev"

timeout /t 5

echo 启动商家端前端 (端口5174)...
cd /d "I:\spring-boot\spring-boot\spring-boot\merchant-app"
start "商家端前端" cmd /k "npm run dev"

timeout /t 5

echo 启动管理端前端 (端口5175)...
cd /d "I:\spring-boot\spring-boot\spring-boot\spring.application.name"
start "管理端前端" cmd /k "npm run dev"

echo.
echo ========================================
echo 所有服务启动完成！
echo ========================================
echo.
echo 访问地址：
echo 用户端：http://localhost:5173
echo 商家端：http://localhost:5174
echo 管理端：http://localhost:5175
echo.
echo 超级管理员账户：
echo 用户名：super_admin
echo 密码：SuperAdmin123!
echo.
pause
