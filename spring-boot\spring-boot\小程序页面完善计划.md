# 小程序页面完善计划

## 📱 当前状态分析

### 用户端小程序 (miniprogram-user)
- ✅ 基础框架完成
- ✅ 首页基本实现
- ❌ 登录注册页面样式需优化
- ❌ 其他功能页面不完整

### 商家端小程序 (miniprogram-merchant)  
- ✅ 基础框架完成
- ✅ 首页基本实现
- ❌ 登录注册页面样式需优化
- ❌ 其他功能页面不完整

### 管理端小程序 (miniprogram-admin)
- ✅ 基础框架完成
- ✅ 首页基本实现
- ❌ 登录页面样式需优化
- ❌ 其他功能页面不完整

## 🎯 优先级完善计划

### 第一优先级：登录注册页面优化

#### 1. 用户端登录注册页面
**文件位置**: `miniprogram-user/pages/login/` 和 `miniprogram-user/pages/register/`

**需要完善的功能**:
- 微信授权登录
- 手机号验证登录
- 用户协议和隐私政策
- 登录状态保持
- 错误处理和提示

**样式优化**:
- 统一的视觉风格
- 响应式布局
- 加载动画
- 表单验证提示

#### 2. 商家端登录注册页面
**文件位置**: `miniprogram-merchant/pages/login/` 和 `miniprogram-merchant/pages/register/`

**需要完善的功能**:
- 商家账号登录
- 商家注册流程
- 资质认证状态显示
- 审核进度查询

#### 3. 管理端登录页面
**文件位置**: `miniprogram-admin/pages/login/`

**需要完善的功能**:
- 管理员身份验证
- 权限验证
- 安全登录机制

### 第二优先级：核心功能页面

#### 用户端核心页面

1. **个人中心页面** (`pages/profile/profile`)
   - 用户信息展示和编辑
   - 订单历史
   - 收货地址管理
   - 优惠券和积分
   - 设置和帮助

2. **服务列表页面** (`pages/services/services`)
   - 服务分类展示
   - 搜索和筛选功能
   - 服务卡片设计
   - 分页加载

3. **服务详情页面** (`pages/service-detail/service-detail`)
   - 服务详细信息
   - 价格和时长
   - 商家信息
   - 用户评价
   - 立即预约按钮

4. **预约下单页面** (`pages/booking/booking`)
   - 服务选择确认
   - 时间选择
   - 地址选择
   - 备注信息
   - 支付方式选择

5. **订单管理页面** (`pages/orders/orders`)
   - 订单状态分类
   - 订单列表展示
   - 订单操作（取消、评价等）
   - 订单搜索

6. **订单详情页面** (`pages/order-detail/order-detail`)
   - 订单完整信息
   - 服务进度跟踪
   - 联系商家
   - 订单评价

#### 商家端核心页面

1. **仪表盘页面** (`pages/dashboard/dashboard`)
   - 今日数据概览
   - 订单统计
   - 收入统计
   - 待处理事项

2. **订单管理页面** (`pages/orders/orders`)
   - 新订单提醒
   - 订单状态管理
   - 批量操作
   - 订单筛选

3. **服务管理页面** (`pages/services/services`)
   - 服务列表
   - 添加/编辑服务
   - 服务上下架
   - 价格管理

4. **财务管理页面** (`pages/finance/finance`)
   - 收入统计
   - 提现申请
   - 交易记录
   - 财务报表

5. **客户管理页面** (`pages/customers/customers`)
   - 客户列表
   - 客户详情
   - 沟通记录
   - 客户标签

#### 管理端核心页面

1. **数据概览页面** (`pages/dashboard/dashboard`)
   - 平台整体数据
   - 实时监控
   - 异常告警
   - 趋势分析

2. **用户管理页面** (`pages/users/users`)
   - 用户列表
   - 用户详情
   - 用户状态管理
   - 用户行为分析

3. **商家管理页面** (`pages/merchants/merchants`)
   - 商家列表
   - 商家审核
   - 商家详情
   - 商家状态管理

4. **订单监控页面** (`pages/orders/orders`)
   - 订单总览
   - 异常订单处理
   - 订单统计
   - 退款处理

## 🛠️ 具体实现步骤

### 步骤1：登录注册页面优化 (第1周)

#### 用户端登录页面优化
```javascript
// miniprogram-user/pages/login/login.js
Page({
  data: {
    canIUse: wx.canIUse('button.open-type.getUserInfo'),
    loading: false,
    userInfo: null,
    hasUserInfo: false
  },

  onLoad() {
    // 检查登录状态
    this.checkLoginStatus();
  },

  // 微信授权登录
  getUserProfile() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        });
        this.wxLogin();
      }
    });
  },

  // 微信登录
  wxLogin() {
    this.setData({ loading: true });
    
    wx.login({
      success: (res) => {
        if (res.code) {
          // 发送 res.code 到后台换取 openId, sessionKey, unionId
          this.loginWithCode(res.code);
        }
      }
    });
  },

  // 手机号登录
  getPhoneNumber(e) {
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      // 获取手机号成功
      this.loginWithPhone(e.detail);
    }
  }
});
```

#### 商家端登录页面优化
```javascript
// miniprogram-merchant/pages/login/login.js
Page({
  data: {
    username: '',
    password: '',
    loading: false,
    showPassword: false
  },

  // 账号密码登录
  handleLogin() {
    if (!this.validateForm()) return;
    
    this.setData({ loading: true });
    
    // 调用登录API
    this.loginWithPassword();
  },

  // 表单验证
  validateForm() {
    const { username, password } = this.data;
    
    if (!username) {
      wx.showToast({ title: '请输入用户名', icon: 'none' });
      return false;
    }
    
    if (!password) {
      wx.showToast({ title: '请输入密码', icon: 'none' });
      return false;
    }
    
    return true;
  }
});
```

### 步骤2：核心功能页面实现 (第2-3周)

#### 用户端个人中心页面
```javascript
// miniprogram-user/pages/profile/profile.js
Page({
  data: {
    userInfo: null,
    orderStats: {
      pending: 0,
      processing: 0,
      completed: 0
    },
    menuItems: [
      { icon: 'location', title: '收货地址', url: '/pages/address/address' },
      { icon: 'coupon', title: '优惠券', url: '/pages/coupon/coupon' },
      { icon: 'points', title: '积分', url: '/pages/points/points' },
      { icon: 'help', title: '帮助中心', url: '/pages/help/help' }
    ]
  },

  onLoad() {
    this.loadUserInfo();
    this.loadOrderStats();
  },

  // 加载用户信息
  loadUserInfo() {
    // 从缓存或API获取用户信息
  },

  // 加载订单统计
  loadOrderStats() {
    // 获取用户订单统计数据
  }
});
```

### 步骤3：样式和交互优化 (第4周)

#### 统一样式规范
```css
/* 全局样式变量 */
:root {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --text-color: #333333;
  --text-color-secondary: #666666;
  --border-color: #e8e8e8;
  --background-color: #f5f5f5;
}

/* 通用组件样式 */
.btn-primary {
  background-color: var(--primary-color);
  color: white;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
}

.card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}
```

## 📋 完善检查清单

### 用户端小程序
- [ ] 登录注册页面样式优化
- [ ] 个人中心页面完整实现
- [ ] 服务浏览和搜索功能
- [ ] 预约下单流程
- [ ] 订单管理功能
- [ ] 地址管理功能
- [ ] 支付集成
- [ ] 消息通知

### 商家端小程序
- [ ] 登录注册页面样式优化
- [ ] 仪表盘数据展示
- [ ] 订单管理功能
- [ ] 服务管理功能
- [ ] 财务管理功能
- [ ] 客户管理功能
- [ ] 统计报表
- [ ] 消息通知

### 管理端小程序
- [ ] 登录页面样式优化
- [ ] 数据概览仪表盘
- [ ] 用户管理功能
- [ ] 商家管理和审核
- [ ] 订单监控功能
- [ ] 系统设置
- [ ] 数据统计
- [ ] 异常处理

## 🎯 质量标准

1. **功能完整性**: 所有核心功能都能正常使用
2. **用户体验**: 界面美观，操作流畅
3. **性能优化**: 页面加载速度快，响应及时
4. **错误处理**: 完善的错误提示和异常处理
5. **数据安全**: 用户数据加密传输和存储
6. **兼容性**: 支持主流微信版本

## ⏰ 时间安排

- **第1周**: 登录注册页面优化
- **第2周**: 用户端核心页面实现
- **第3周**: 商家端和管理端核心页面实现
- **第4周**: 样式优化和测试调试

完成后，三端小程序将具备完整的业务功能，可以正式上线使用。
