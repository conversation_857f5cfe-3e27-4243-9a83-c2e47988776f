-- 正确的超级管理员配置脚本
-- 基于实际数据库表结构创建超级管理员账户
-- 确保超级管理员可以登录所有端：用户端、商家端、管理端

SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 使用数据库
USE laundry_system;

-- 开启事务
START TRANSACTION;

-- 删除可能存在的旧超级管理员账号
DELETE FROM users WHERE username = 'super_admin' OR phone = '***********';
DELETE FROM admins WHERE username = 'super_admin' OR phone = '***********';
DELETE FROM merchants WHERE username = 'super_admin' OR phone = '***********';

-- 创建统一的超级管理员账号
-- 用户名: super_admin
-- 手机号: ***********  
-- 密码: SuperAdmin123!
-- BCrypt加密后的密码: $2a$10$xK1FX6tNwQGuPa1TGYJlBOyX9TTrwHk.PCqIGZi2pN5.L5k4QeCv2

-- 1. 用户端超级管理员
INSERT INTO users (
    username,
    phone,
    password,
    real_name,
    name,
    email,
    role,
    status,
    points,
    balance,
    membership_level,
    created_at,
    updated_at,
    created_by,
    updated_by,
    last_login_ip,
    last_login_time,
    avatar,
    address
) VALUES (
    'super_admin',
    '***********',
    '$2a$10$xK1FX6tNwQGuPa1TGYJlBOyX9TTrwHk.PCqIGZi2pN5.L5k4QeCv2',
    '超级管理员',
    '超级管理员',
    '<EMAIL>',
    'ADMIN',
    'ACTIVE',
    0,
    0.00,
    'DIAMOND',
    NOW(),
    NOW(),
    'SYSTEM',
    'SYSTEM',
    '127.0.0.1',
    NOW(),
    '/images/super-admin-avatar.png',
    '系统管理中心'
);

-- 获取用户ID
SET @user_id = LAST_INSERT_ID();

-- 2. 管理端超级管理员
INSERT INTO admins (
    username,
    phone,
    password,
    real_name,
    email,
    role,
    status,
    permissions,
    created_at,
    updated_at,
    avatar,
    last_login_ip,
    last_login_time
) VALUES (
    'super_admin',
    '***********',
    '$2a$10$xK1FX6tNwQGuPa1TGYJlBOyX9TTrwHk.PCqIGZi2pN5.L5k4QeCv2',
    '超级管理员',
    '<EMAIL>',
    'SUPER_ADMIN',
    'ACTIVE',
    '["*", "user:view", "user:edit", "user:delete", "merchant:view", "merchant:edit", "merchant:delete", "merchant:approve", "order:view", "order:edit", "payment:view", "system:config", "system:log", "announcement:manage", "coupon:manage", "statistics:view"]',
    NOW(),
    NOW(),
    '/images/super-admin-avatar.png',
    '127.0.0.1',
    NOW()
);

-- 3. 商家端超级管理员
INSERT INTO merchants (
    user_id,
    username,
    password,
    shop_name,
    contact_person,
    phone,
    contact_phone,
    email,
    status,
    certification_status,
    business_license,
    license_image,
    id_card_front,
    id_card_back,
    address,
    latitude,
    longitude,
    business_hours,
    description,
    rating,
    service_rating,
    total_orders,
    balance,
    deposit_amount,
    deposit_status,
    created_at,
    updated_at,
    created_by,
    updated_by,
    avatar,
    logo,
    province,
    city,
    district
) VALUES (
    @user_id,
    'super_admin',
    '$2a$10$xK1FX6tNwQGuPa1TGYJlBOyX9TTrwHk.PCqIGZi2pN5.L5k4QeCv2',
    '系统超级管理员店铺',
    '超级管理员',
    '***********',
    '***********',
    '<EMAIL>',
    'ACTIVE',
    'APPROVED',
    'SUPER_ADMIN_LICENSE_001',
    '/uploads/business-license-super.jpg',
    '/uploads/id-card-front-super.jpg',
    '/uploads/id-card-back-super.jpg',
    '系统管理中心',
    39.9042,
    116.4074,
    '00:00-23:59',
    '系统超级管理员专用店铺，拥有所有权限，可以管理整个平台',
    5.0,
    5.0,
    0,
    0.00,
    0.00,
    'PAID',
    NOW(),
    NOW(),
    'SYSTEM',
    'SYSTEM',
    '/images/super-admin-avatar.png',
    '/images/super-admin-logo.png',
    '北京市',
    '北京市',
    '朝阳区'
);

-- 提交事务
COMMIT;

-- 验证创建结果
SELECT '=== 超级管理员账号创建完成 ===' as message;
SELECT '用户名: super_admin' as login_info;
SELECT '手机号: ***********' as phone_info;
SELECT '密码: SuperAdmin123!' as password_info;
SELECT '该账号可登录: 用户端、商家端、管理端' as access_info;

-- 验证用户端账号
SELECT 'users表验证:' as table_name;
SELECT id, username, phone, real_name, role, status FROM users WHERE username = 'super_admin';

-- 验证管理端账号  
SELECT 'admins表验证:' as table_name;
SELECT id, username, phone, real_name, role, status FROM admins WHERE username = 'super_admin';

-- 验证商家端账号
SELECT 'merchants表验证:' as table_name;
SELECT id, username, phone, shop_name, status, certification_status FROM merchants WHERE username = 'super_admin';
