# 洗护平台生产环境配置指南

## 🔧 环境变量配置

### 1. 创建环境变量文件

```bash
# 在项目根目录创建 .env 文件
touch .env
```

### 2. 环境变量内容

```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=laundry_system
DB_USERNAME=laundry_user
DB_PASSWORD=LaundrySecure2024!

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=RedisSecure2024!

# JWT配置
JWT_SECRET=LaundryJWTSecretKey2024ForProductionEnvironmentShouldBe64Characters
JWT_EXPIRATION=86400000

# 文件上传配置
UPLOAD_PATH=/var/www/laundry/uploads
UPLOAD_MAX_SIZE=10485760

# 邮件配置
MAIL_HOST=smtp.qq.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password

# 短信配置
SMS_ACCESS_KEY=your-sms-access-key
SMS_SECRET_KEY=your-sms-secret-key
SMS_SIGN_NAME=洗护服务

# 支付配置
ALIPAY_APP_ID=your-alipay-app-id
ALIPAY_PRIVATE_KEY=your-alipay-private-key
ALIPAY_PUBLIC_KEY=your-alipay-public-key

WECHAT_PAY_MCH_ID=your-wechat-mch-id
WECHAT_PAY_API_KEY=your-wechat-api-key

# 微信小程序配置
WECHAT_USER_APPID=your-user-appid
WECHAT_USER_SECRET=your-user-secret
WECHAT_MERCHANT_APPID=your-merchant-appid
WECHAT_MERCHANT_SECRET=your-merchant-secret
WECHAT_ADMIN_APPID=your-admin-appid
WECHAT_ADMIN_SECRET=your-admin-secret

# 系统配置
SYSTEM_NAME=洗护服务平台
SYSTEM_VERSION=1.0.0
SYSTEM_DOMAIN=https://laundry.com
```

## 🗄️ 数据库生产配置

### 1. MySQL用户创建

```sql
-- 创建生产环境数据库用户
CREATE USER 'laundry_user'@'localhost' IDENTIFIED BY 'LaundrySecure2024!';
GRANT ALL PRIVILEGES ON laundry_system.* TO 'laundry_user'@'localhost';
FLUSH PRIVILEGES;

-- 创建数据库
CREATE DATABASE laundry_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. 数据库初始化

```bash
# 1. 初始化数据库结构
mysql -u laundry_user -p laundry_system < database/complete_database.sql

# 2. 创建超级管理员
mysql -u laundry_user -p laundry_system < create-super-admin.sql

# 3. 初始化基础数据
mysql -u laundry_user -p laundry_system < production_data_init.sql
```

## 🔒 后端生产配置

### 1. 管理员后端 (Spring-boot-vue)

```yaml
# application-prod.yml
server:
  port: 8080

spring:
  profiles:
    active: prod
  
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:laundry_system}?useSSL=true&serverTimezone=Asia/Shanghai&characterEncoding=utf8
    username: ${DB_USERNAME:laundry_user}
    password: ${DB_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD}
    
jwt:
  secret: ${JWT_SECRET}
  expiration: ${JWT_EXPIRATION:86400000}

logging:
  level:
    com.example: INFO
  file:
    name: /var/log/laundry/admin-backend.log
```

### 2. 用户后端 (spring-boot-1)

```yaml
# application-prod.yml
server:
  port: 8081

spring:
  profiles:
    active: prod
  
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:laundry_system}?useSSL=true&serverTimezone=Asia/Shanghai&characterEncoding=utf8
    username: ${DB_USERNAME:laundry_user}
    password: ${DB_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    
jwt:
  secret: ${JWT_SECRET}
  expiration: ${JWT_EXPIRATION:86400000}

logging:
  level:
    com.laundry: INFO
  file:
    name: /var/log/laundry/user-backend.log
```

### 3. 商家后端 (spring-boot2)

```yaml
# application-prod.yml
server:
  port: 8082

spring:
  profiles:
    active: prod
  
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:laundry_system}?useSSL=true&serverTimezone=Asia/Shanghai&characterEncoding=utf8
    username: ${DB_USERNAME:laundry_user}
    password: ${DB_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    
jwt:
  secret: ${JWT_SECRET}
  expiration: ${JWT_EXPIRATION:86400000}

logging:
  level:
    com.example: INFO
  file:
    name: /var/log/laundry/merchant-backend.log
```

## 🌐 前端生产配置

### 1. 用户前端环境配置

```javascript
// my-vue/.env.production
VITE_API_URL=https://api.laundry.com
VITE_WS_URL=wss://api.laundry.com
VITE_APP_TITLE=洗护服务平台
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=production
```

### 2. 商家前端环境配置

```javascript
// merchant-app/.env.production
VITE_API_URL=https://merchant-api.laundry.com
VITE_WS_URL=wss://merchant-api.laundry.com
VITE_APP_TITLE=商家管理系统
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=production
```

### 3. 管理员前端环境配置

```javascript
// spring.application.name/.env.production
VITE_API_URL=https://admin-api.laundry.com
VITE_WS_URL=wss://admin-api.laundry.com
VITE_APP_TITLE=洗护管理系统
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=production
```

## 📱 小程序生产配置

### 1. 用户端小程序

```javascript
// miniprogram-user/config/env.js
const currentEnv = 'production'; // 修改为production

const env = {
  production: {
    baseUrl: 'https://api.laundry.com/api',
    wsUrl: 'wss://api.laundry.com/ws',
    appId: 'your-production-user-appid',
    debug: false,
    logLevel: 'error'
  }
};
```

### 2. 商家端小程序

```javascript
// miniprogram-merchant/config/env.js
const currentEnv = 'production'; // 修改为production

const env = {
  production: {
    baseUrl: 'https://api.laundry.com/api/merchant',
    wsUrl: 'wss://api.laundry.com/ws',
    appId: 'your-production-merchant-appid',
    debug: false,
    logLevel: 'error'
  }
};
```

### 3. 管理端小程序

```javascript
// miniprogram-admin/config/env.js
const currentEnv = 'production'; // 修改为production

const env = {
  production: {
    baseUrl: 'https://api.laundry.com/api/admin',
    wsUrl: 'wss://api.laundry.com/ws',
    appId: 'your-production-admin-appid',
    debug: false,
    logLevel: 'error'
  }
};
```

## 🚀 部署脚本

### 1. 生产环境启动脚本

```bash
#!/bin/bash
# start-production.sh

echo "🚀 启动洗护平台生产环境..."

# 设置环境变量
export SPRING_PROFILES_ACTIVE=prod

# 启动后端服务
echo "启动管理员后端..."
nohup java -jar -Xms512m -Xmx1024m Spring-boot-vue/target/Spring-boot-vue-0.0.1-SNAPSHOT.jar > /var/log/laundry/admin-backend.log 2>&1 &

echo "启动用户后端..."
nohup java -jar -Xms512m -Xmx1024m spring-boot-1/target/laundry-care-backend-1.0.0.jar > /var/log/laundry/user-backend.log 2>&1 &

echo "启动商家后端..."
nohup java -jar -Xms512m -Xmx1024m spring-boot2/target/spring-boot2-0.0.1-SNAPSHOT.jar > /var/log/laundry/merchant-backend.log 2>&1 &

echo "✅ 所有服务启动完成"
echo "📊 服务状态检查: http://localhost:8080/actuator/health"
```

### 2. 服务监控脚本

```bash
#!/bin/bash
# monitor-services.sh

check_service() {
    local port=$1
    local name=$2
    
    if curl -f -s http://localhost:$port/actuator/health > /dev/null; then
        echo "✅ $name (端口:$port) - 运行正常"
    else
        echo "❌ $name (端口:$port) - 服务异常"
        return 1
    fi
}

echo "🔍 检查服务状态..."
check_service 8080 "管理员后端"
check_service 8081 "用户后端"
check_service 8082 "商家后端"
```

## 📋 上线检查清单

- [ ] 环境变量配置完成
- [ ] 数据库用户和权限配置
- [ ] 数据库初始化完成
- [ ] 超级管理员账号创建
- [ ] 后端生产配置文件
- [ ] 前端生产环境变量
- [ ] 小程序生产环境配置
- [ ] SSL证书配置
- [ ] 域名解析配置
- [ ] 防火墙端口开放
- [ ] 日志目录创建
- [ ] 文件上传目录权限
- [ ] 备份策略配置
- [ ] 监控告警配置

## 🔐 安全建议

1. **定期更新密码**：数据库、Redis、JWT密钥
2. **启用防火墙**：只开放必要端口
3. **SSL证书**：使用HTTPS加密传输
4. **日志监控**：定期检查异常日志
5. **数据备份**：每日自动备份数据库
6. **权限控制**：最小权限原则
7. **安全扫描**：定期进行安全漏洞扫描
