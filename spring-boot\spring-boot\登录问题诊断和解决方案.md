# 登录问题诊断和解决方案

## 🔍 问题分析

根据错误日志分析，发现以下问题：

### 1. 端口冲突问题 ✅ 已解决
- **问题**: 商家端尝试使用端口5173，但该端口已被用户端占用
- **解决**: 修改商家端端口为5174

### 2. API连接问题 ✅ 已解决
- **问题**: 用户端API配置指向8080端口，但CSP策略只允许8081
- **解决**: 修改用户端API配置从8080改为8081

### 3. CSP策略问题 ✅ 已解决
- **问题**: Content Security Policy阻止跨域请求
- **解决**: 更新CSP策略允许所有必要的端口

### 4. 代理配置问题 ✅ 已解决
- **问题**: 商家端vite配置缺少/api路径代理
- **解决**: 添加/api路径代理配置

## 🛠️ 已修复的配置

### 用户端 (my-vue)
```javascript
// src/services/api.js
const request = axios.create({
  baseURL: 'http://localhost:8081/api', // 修改为8081
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// vite.config.js - CSP策略
headers: {
  'Content-Security-Policy': "connect-src 'self' http://localhost:8081 http://localhost:8080 http://localhost:8082 ws://localhost:* wss://localhost:*;"
}
```

### 商家端 (merchant-app)
```javascript
// package.json
"dev": "vite --port 5174 --host 0.0.0.0"

// vite.config.js - 代理配置
proxy: {
  '/api': {
    target: 'http://localhost:8082',
    changeOrigin: true,
    secure: false,
    ws: true
  },
  '/auth': {
    target: 'http://localhost:8082',
    changeOrigin: true,
    secure: false,
    ws: true
  }
}
```

## 🚀 启动步骤

### 方法1: 使用启动脚本 (推荐)
```bash
# 运行一键启动脚本
I:\spring-boot\spring-boot\spring-boot\start-all-services.bat
```

### 方法2: 手动启动
```bash
# 1. 启动后端服务
cd I:\spring-boot\spring-boot\spring-boot\Spring-boot-vue
mvn spring-boot:run -Dspring-boot.run.profiles=admin    # 管理端 8080
mvn spring-boot:run -Dspring-boot.run.profiles=user     # 用户端 8081  
mvn spring-boot:run -Dspring-boot.run.profiles=merchant # 商家端 8082

# 2. 启动前端服务
cd I:\spring-boot\spring-boot\spring-boot\my-vue
npm run dev  # 用户端 5173

cd I:\spring-boot\spring-boot\spring-boot\merchant-app
npm run dev  # 商家端 5174

cd I:\spring-boot\spring-boot\spring-boot\spring.application.name
npm run dev  # 管理端 5175
```

## 🔧 端口配置总结

| 服务 | 端口 | 说明 |
|------|------|------|
| 管理端后端 | 8080 | Spring Boot |
| 用户端后端 | 8081 | Spring Boot |
| 商家端后端 | 8082 | Spring Boot |
| 用户端前端 | 5173 | Vue + Vite |
| 商家端前端 | 5174 | Vue + Vite |
| 管理端前端 | 5175 | Vue + Vite |

## 🔐 超级管理员账户

```
用户名: super_admin
密码: SuperAdmin123!
权限: 全平台最高权限
```

## 📋 测试清单

### 后端服务测试
- [ ] 检查Java进程: `tasklist | findstr java`
- [ ] 检查端口占用: `netstat -ano | findstr :8080`
- [ ] 测试API连接: 访问各端口的健康检查端点

### 前端服务测试
- [ ] 用户端: http://localhost:5173
- [ ] 商家端: http://localhost:5174
- [ ] 管理端: http://localhost:5175

### 登录功能测试
- [ ] 用户端登录测试
- [ ] 商家端登录测试
- [ ] 管理端登录测试
- [ ] 超级管理员跨平台登录测试

## 🐛 常见问题排查

### 1. 端口被占用
```bash
# 查看端口占用
netstat -ano | findstr :端口号

# 结束进程
taskkill /PID 进程ID /F
```

### 2. 前端无法连接后端
- 检查后端服务是否启动
- 检查API配置是否正确
- 检查代理配置是否正确
- 检查CSP策略是否允许

### 3. 登录失败
- 检查用户名密码是否正确
- 检查数据库连接是否正常
- 检查JWT配置是否正确
- 检查跨域配置是否正确

### 4. 页面无法访问
- 检查前端服务是否启动
- 检查端口是否正确
- 检查路由配置是否正确
- 检查权限配置是否正确

## 📞 技术支持

如果遇到其他问题，请检查：
1. 控制台错误信息
2. 网络请求状态
3. 后端日志输出
4. 数据库连接状态

---

**更新时间**: 2024年12月28日  
**版本**: v1.0  
**状态**: ✅ 问题已解决
