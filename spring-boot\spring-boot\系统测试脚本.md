# 洗护平台系统测试脚本

## 🎯 测试目标

验证洗护平台三端（用户端、商家端、管理端）的完整功能，确保所有API接口正常工作，小程序功能完整可用。

## 🔧 测试环境准备

### 1. 后端服务启动
```bash
# 进入后端项目目录
cd "I:\spring-boot\spring-boot\spring-boot\Spring-boot-vue"

# 启动Spring Boot服务
mvn spring-boot:run

# 验证服务启动
curl http://localhost:8080/actuator/health
```

### 2. 数据库准备
确保MySQL数据库已启动，并且包含以下测试数据：

**超级管理员账户**:
- 用户名: `super_admin`
- 手机号: `13900139000`
- 密码: `SuperAdmin123!`
- 角色: `ADMIN`

## 📋 API接口测试

### 1. 用户端API测试

#### 1.1 用户登录测试
```bash
# 测试用户登录
curl -X POST http://localhost:8080/api/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "super_admin",
    "password": "SuperAdmin123!"
  }'

# 预期结果: 返回登录成功和用户信息
```

#### 1.2 用户注册测试
```bash
# 测试用户注册
curl -X POST http://localhost:8080/api/user/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser001",
    "password": "Test123456!",
    "phone": "13800138001",
    "realName": "测试用户"
  }'
```

#### 1.3 订单相关测试
```bash
# 获取订单列表
curl -X GET "http://localhost:8080/api/user/orders?page=0&size=10"

# 创建订单
curl -X POST http://localhost:8080/api/user/orders \
  -H "Content-Type: application/json" \
  -d '{
    "serviceId": 1,
    "merchantId": 1,
    "addressId": 1,
    "pickupTime": "2024-12-29 10:00:00",
    "deliveryTime": "2024-12-30 18:00:00",
    "notes": "请小心处理"
  }'
```

### 2. 商家端API测试

#### 2.1 商家登录测试
```bash
# 测试商家登录
curl -X POST http://localhost:8080/api/merchant/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "super_admin",
    "password": "SuperAdmin123!"
  }'
```

#### 2.2 商家订单管理测试
```bash
# 获取商家订单列表
curl -X GET "http://localhost:8080/api/merchant/orders?page=0&size=10"

# 更新订单状态
curl -X PUT http://localhost:8080/api/merchant/orders/1/status \
  -H "Content-Type: application/json" \
  -d '{
    "status": "PROCESSING",
    "notes": "订单已接收，正在处理"
  }'
```

#### 2.3 商家资料管理测试
```bash
# 获取商家资料
curl -X GET http://localhost:8080/api/merchant/profile

# 更新商家资料
curl -X PUT http://localhost:8080/api/merchant/profile \
  -H "Content-Type: application/json" \
  -d '{
    "shopName": "优质洗护中心",
    "contactPerson": "张经理",
    "phone": "13900139000",
    "address": "北京市朝阳区示例街道123号"
  }'
```

### 3. 管理端API测试

#### 3.1 管理员登录测试
```bash
# 测试管理员登录
curl -X POST http://localhost:8080/api/admin/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "super_admin",
    "password": "SuperAdmin123!"
  }'
```

#### 3.2 用户管理测试
```bash
# 获取用户列表
curl -X GET "http://localhost:8080/api/admin/users?page=0&size=10"

# 更新用户状态
curl -X PUT http://localhost:8080/api/admin/users/1/status \
  -H "Content-Type: application/json" \
  -d '{
    "status": "ACTIVE"
  }'
```

#### 3.3 系统统计测试
```bash
# 获取系统统计数据
curl -X GET http://localhost:8080/api/admin/statistics

# 获取公告列表
curl -X GET http://localhost:8080/api/admin/announcements
```

### 4. 文件上传测试

#### 4.1 单文件上传测试
```bash
# 创建测试图片文件
echo "test image content" > test.jpg

# 上传图片
curl -X POST http://localhost:8080/api/common/upload \
  -F "file=@test.jpg" \
  -F "type=image"
```

#### 4.2 多文件上传测试
```bash
# 创建多个测试文件
echo "test image 1" > test1.jpg
echo "test image 2" > test2.jpg

# 批量上传
curl -X POST http://localhost:8080/api/common/upload/multiple \
  -F "files=@test1.jpg" \
  -F "files=@test2.jpg" \
  -F "type=image"
```

## 📱 小程序功能测试

### 1. 用户端小程序测试

#### 测试步骤：
1. **登录功能测试**
   - 打开用户端小程序
   - 使用超级管理员账户登录
   - 验证登录成功并跳转到首页

2. **服务浏览测试**
   - 浏览服务列表
   - 查看服务详情
   - 测试搜索功能

3. **订单管理测试**
   - 创建新订单
   - 查看订单列表
   - 查看订单详情
   - 测试订单状态更新

4. **个人中心测试**
   - 查看个人信息
   - 编辑个人资料
   - 测试地址管理
   - 测试收藏功能

5. **投诉功能测试**
   - 提交新投诉
   - 查看投诉列表
   - 上传投诉图片

### 2. 商家端小程序测试

#### 测试步骤：
1. **商家登录测试**
   - 使用超级管理员账户登录商家端
   - 验证登录成功

2. **订单处理测试**
   - 查看待处理订单
   - 接收订单
   - 更新订单状态
   - 完成订单

3. **店铺管理测试**
   - 查看店铺信息
   - 编辑店铺资料
   - 管理服务项目

4. **财务管理测试**
   - 查看收益统计
   - 查看交易记录
   - 测试提现功能

### 3. 管理端小程序测试

#### 测试步骤：
1. **管理员登录测试**
   - 使用超级管理员账户登录
   - 验证权限和功能菜单

2. **用户管理测试**
   - 查看用户列表
   - 编辑用户信息
   - 管理用户状态

3. **商家管理测试**
   - 查看商家列表
   - 审核商家认证
   - 管理商家状态

4. **系统监控测试**
   - 查看系统统计
   - 监控订单状态
   - 处理投诉信息

## 🔍 WebSocket功能测试

### 1. 连接测试
```javascript
// 在浏览器控制台中测试WebSocket连接
const ws = new WebSocket('ws://localhost:8080/ws/chat?userId=1');

ws.onopen = function(event) {
    console.log('WebSocket连接已建立');
};

ws.onmessage = function(event) {
    console.log('收到消息:', event.data);
};

ws.onerror = function(error) {
    console.log('WebSocket错误:', error);
};
```

### 2. 消息发送测试
```javascript
// 发送文本消息
ws.send(JSON.stringify({
    type: 'text',
    content: '你好，这是测试消息',
    senderId: '1',
    senderType: 'USER',
    merchantId: '1'
}));

// 发送心跳消息
ws.send(JSON.stringify({
    type: 'ping'
}));
```

## ✅ 测试检查清单

### 后端API测试
- [ ] 用户登录接口正常
- [ ] 商家登录接口正常
- [ ] 管理员登录接口正常
- [ ] 文件上传功能正常
- [ ] 订单管理接口正常
- [ ] 用户管理接口正常
- [ ] WebSocket连接正常

### 小程序功能测试
- [ ] 用户端登录功能正常
- [ ] 商家端登录功能正常
- [ ] 管理端登录功能正常
- [ ] 订单创建和管理正常
- [ ] 投诉功能正常
- [ ] 聊天功能正常
- [ ] 文件上传功能正常

### 数据库测试
- [ ] 用户数据正常存储
- [ ] 订单数据正常存储
- [ ] 文件路径正常存储
- [ ] 投诉数据正常存储

## 🚨 常见问题排查

### 1. 后端服务无法启动
- 检查端口8080是否被占用
- 检查数据库连接配置
- 检查Maven依赖是否完整

### 2. 小程序无法连接后端
- 检查API地址配置是否正确
- 检查网络连接是否正常
- 检查跨域配置是否正确

### 3. 文件上传失败
- 检查上传目录权限
- 检查文件大小限制
- 检查文件类型限制

### 4. WebSocket连接失败
- 检查WebSocket配置
- 检查防火墙设置
- 检查浏览器WebSocket支持

---

**测试完成时间**: ___________  
**测试人员**: ___________  
**测试结果**: ___________
