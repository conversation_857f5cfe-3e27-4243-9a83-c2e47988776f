-- 统一超级管理员配置脚本
-- 确保超级管理员可以登录所有端：用户端、商家端、管理端
-- 支持用户名和手机号登录

SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 使用数据库
USE laundry_system;

-- 开启事务
START TRANSACTION;

-- 删除可能存在的旧超级管理员账号
DELETE FROM users WHERE username IN ('admin', 'superadmin', 'super_admin') OR phone = '***********';
DELETE FROM admins WHERE username IN ('admin', 'superadmin', 'super_admin') OR phone = '***********';
DELETE FROM merchants WHERE username IN ('admin', 'superadmin', 'super_admin') OR phone = '***********';

-- 创建统一的超级管理员账号
-- 用户名: super_admin
-- 手机号: ***********  
-- 密码: SuperAdmin123!
-- BCrypt加密后的密码: $2a$10$xK1FX6tNwQGuPa1TGYJlBOyX9TTrwHk.PCqIGZi2pN5.L5k4QeCv2

-- 1. 用户端超级管理员
INSERT INTO users (
    username,
    phone,
    password,
    real_name,
    name,
    email,
    role,
    status,
    points,
    balance,
    membership_level,
    email_verified,
    mobile_verified,
    real_name_verified,
    created_at,
    updated_at,
    created_by,
    updated_by,
    last_login_ip,
    last_login_time,
    avatar
) VALUES (
    'super_admin',
    '***********',
    '$2a$10$xK1FX6tNwQGuPa1TGYJlBOyX9TTrwHk.PCqIGZi2pN5.L5k4QeCv2',
    '超级管理员',
    '超级管理员',
    '<EMAIL>',
    'SUPER_ADMIN',
    'ACTIVE',
    0,
    0.00,
    'VIP',
    1,
    1,
    1,
    NOW(),
    NOW(),
    'SYSTEM',
    'SYSTEM',
    '127.0.0.1',
    NOW(),
    '/images/super-admin-avatar.png'
);

-- 获取用户ID
SET @user_id = LAST_INSERT_ID();

-- 2. 管理端超级管理员
INSERT INTO admins (
    username,
    phone,
    password,
    real_name,
    email,
    role,
    status,
    permissions,
    department,
    created_at,
    updated_at,
    avatar,
    last_login_ip,
    last_login_time
) VALUES (
    'super_admin',
    '***********',
    '$2a$10$xK1FX6tNwQGuPa1TGYJlBOyX9TTrwHk.PCqIGZi2pN5.L5k4QeCv2',
    '超级管理员',
    '<EMAIL>',
    'SUPER_ADMIN',
    'ACTIVE',
    '["user:view","user:edit","user:delete","merchant:view","merchant:edit","merchant:delete","merchant:approve","order:view","order:edit","payment:view","system:config","system:log","announcement:manage","coupon:manage","statistics:view","*"]',
    '系统管理部',
    NOW(),
    NOW(),
    '/images/super-admin-avatar.png',
    '127.0.0.1',
    NOW()
);

-- 3. 商家端超级管理员
INSERT INTO merchants (
    user_id,
    username,
    phone,
    password,
    shop_name,
    contact_person,
    email,
    status,
    certification_status,
    business_license,
    id_card_front,
    id_card_back,
    address,
    latitude,
    longitude,
    business_hours,
    description,
    rating,
    order_count,
    created_at,
    updated_at,
    created_by,
    updated_by
) VALUES (
    @user_id,
    'super_admin',
    '***********',
    '$2a$10$xK1FX6tNwQGuPa1TGYJlBOyX9TTrwHk.PCqIGZi2pN5.L5k4QeCv2',
    '系统超级管理员店铺',
    '超级管理员',
    '<EMAIL>',
    'ACTIVE',
    'APPROVED',
    '/uploads/business-license-super.jpg',
    '/uploads/id-card-front-super.jpg',
    '/uploads/id-card-back-super.jpg',
    '系统管理中心',
    39.9042,
    116.4074,
    '00:00-23:59',
    '系统超级管理员专用店铺，拥有所有权限',
    5.0,
    0,
    NOW(),
    NOW(),
    'SYSTEM',
    'SYSTEM'
);

-- 4. 创建用户角色权限映射（如果有角色表的话）
INSERT IGNORE INTO user_roles (user_id, role_name) VALUES 
(@user_id, 'SUPER_ADMIN'),
(@user_id, 'ADMIN'),
(@user_id, 'USER'),
(@user_id, 'MERCHANT');

-- 5. 创建用户权限映射（如果有权限表的话）
INSERT IGNORE INTO user_permissions (user_id, permission) VALUES 
(@user_id, '*'),
(@user_id, 'user:view'),
(@user_id, 'user:edit'),
(@user_id, 'user:delete'),
(@user_id, 'merchant:view'),
(@user_id, 'merchant:edit'),
(@user_id, 'merchant:delete'),
(@user_id, 'merchant:approve'),
(@user_id, 'order:view'),
(@user_id, 'order:edit'),
(@user_id, 'payment:view'),
(@user_id, 'system:config'),
(@user_id, 'system:log'),
(@user_id, 'announcement:manage'),
(@user_id, 'coupon:manage'),
(@user_id, 'statistics:view');

-- 提交事务
COMMIT;

-- 验证创建结果
SELECT '=== 超级管理员账号创建完成 ===' as message;
SELECT '用户名: super_admin' as login_info;
SELECT '手机号: ***********' as phone_info;
SELECT '密码: SuperAdmin123!' as password_info;
SELECT '该账号可登录: 用户端、商家端、管理端' as access_info;

-- 验证用户端账号
SELECT id, username, phone, real_name, role, status FROM users WHERE username = 'super_admin';

-- 验证管理端账号  
SELECT id, username, phone, real_name, role, status FROM admins WHERE username = 'super_admin';

-- 验证商家端账号
SELECT id, username, phone, shop_name, status, certification_status FROM merchants WHERE username = 'super_admin';
