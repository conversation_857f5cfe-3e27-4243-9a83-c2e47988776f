# 洗护平台项目上线完善计划

## 🎯 项目概览

本项目包含：
- **3个后端服务**：管理员后端(8080)、用户后端(8081)、商家后端(8082)
- **3个前端应用**：用户前端、商家前端、管理员前端
- **3个小程序**：用户端、商家端、管理端小程序
- **1个数据库**：MySQL统一数据库

## 🔧 立即修复的关键问题

### 1. 端口配置统一 (高优先级)

#### 问题
用户前端端口配置不一致，影响部署和访问。

#### 修复方案
```bash
# 1. 修改用户前端配置
# my-vue/package.json
{
  "scripts": {
    "dev": "vite --port 5173 --host 0.0.0.0",
    "serve": "vite --port 5173 --host 0.0.0.0",
    "preview": "vite preview --port 5173"
  }
}

# my-vue/vite.config.js
server: {
  port: 5173, // 改为5173
  // ... 其他配置保持不变
}

# 2. 更新启动脚本
# start-all-services.bat 和 quick-start-all.bat
echo 启动用户前端服务 (端口: 5173)...
start "用户前端" cmd /k "cd spring-boot\my-vue && npm run dev"
```

### 2. 商家注册功能完善 (高优先级)

#### 问题
商家注册缺少营业执照和身份证上传功能。

#### 修复方案
需要完善 `merchant-app/src/views/register/index.vue`：

```vue
<!-- 添加文件上传组件 -->
<el-form-item label="营业执照" required>
  <el-upload
    class="upload-demo"
    action="/api/upload"
    :show-file-list="false"
    :on-success="handleLicenseSuccess"
    :before-upload="beforeUpload"
    accept="image/*"
  >
    <el-image
      v-if="form.businessLicense"
      :src="form.businessLicense"
      style="width: 200px; height: 200px"
      fit="contain"
    />
    <el-button v-else type="primary">上传营业执照</el-button>
  </el-upload>
</el-form-item>

<el-form-item label="身份证正面" required>
  <el-upload
    action="/api/upload"
    :show-file-list="false"
    :on-success="handleIdCardFrontSuccess"
    accept="image/*"
  >
    <el-button type="primary">上传身份证正面</el-button>
  </el-upload>
</el-form-item>

<el-form-item label="身份证背面" required>
  <el-upload
    action="/api/upload"
    :show-file-list="false"
    :on-success="handleIdCardBackSuccess"
    accept="image/*"
  >
    <el-button type="primary">上传身份证背面</el-button>
  </el-upload>
</el-form-item>
```

### 3. 数据库初始化完善 (中优先级)

#### 问题
需要确保生产环境数据库完整初始化。

#### 修复方案
```sql
-- 1. 运行完整数据库初始化
mysql -u root -p < spring-boot/spring-boot/database/complete_database.sql

-- 2. 创建超级管理员
mysql -u root -p < spring-boot/spring-boot/create-super-admin.sql

-- 3. 初始化基础数据
mysql -u root -p < spring-boot/spring-boot/production_data_init.sql
```

## 🚀 小程序完善计划

### 当前状态分析
- **用户端小程序**：基础框架✅，页面不完整❌
- **商家端小程序**：基础框架✅，页面不完整❌
- **管理端小程序**：基础框架✅，页面不完整❌

### 需要完善的页面

#### 用户端小程序
1. **登录注册页面**：样式优化，功能完善
2. **个人中心页面**：用户信息、订单历史、设置
3. **服务列表页面**：服务分类、搜索、筛选
4. **服务详情页面**：服务介绍、价格、预约
5. **订单管理页面**：订单状态、支付、评价
6. **地址管理页面**：收货地址增删改查

#### 商家端小程序
1. **登录注册页面**：商家认证流程
2. **仪表盘页面**：营业数据、订单统计
3. **订单管理页面**：接单、处理、完成
4. **服务管理页面**：服务发布、编辑、下架
5. **财务管理页面**：收入统计、提现申请
6. **客户管理页面**：客户信息、沟通记录

#### 管理端小程序
1. **登录页面**：管理员身份验证
2. **数据概览页面**：平台整体数据
3. **用户管理页面**：用户列表、状态管理
4. **商家管理页面**：商家审核、状态管理
5. **订单监控页面**：订单异常处理
6. **系统设置页面**：平台参数配置

## 📋 前端页面完善检查

### 用户前端 (my-vue) ✅ 基本完整
- 登录注册页面 ✅
- 首页 ✅
- 服务浏览 ✅
- 订单管理 ✅
- 个人中心 ✅
- 支付页面 ✅

### 商家前端 (merchant-app) ⚠️ 需要完善
- 登录页面 ✅
- 注册页面 ❌ **需要添加资质上传**
- 仪表盘 ✅
- 订单管理 ✅
- 服务管理 ✅
- 财务管理 ✅

### 管理员前端 (spring.application.name) ✅ 功能完整
- 登录页面 ✅
- 仪表盘 ✅
- 用户管理 ✅
- 商家管理 ✅
- 订单管理 ✅
- 系统设置 ✅

## 🔒 安全配置完善

### 1. 生产环境配置
```yaml
# application-prod.yml
spring:
  datasource:
    url: ***********************************************************************************
    username: ${DB_USERNAME:laundry_user}
    password: ${DB_PASSWORD:your_strong_password}
  
  jpa:
    hibernate:
      ddl-auto: validate # 生产环境使用validate
    show-sql: false # 关闭SQL日志

jwt:
  secret: ${JWT_SECRET:your_64_character_secret_key}
  expiration: 86400000 # 24小时
```

### 2. 环境变量配置
```bash
# .env 文件
DB_USERNAME=laundry_user
DB_PASSWORD=your_strong_password
JWT_SECRET=your_64_character_secret_key
REDIS_PASSWORD=your_redis_password
```

## 📊 性能优化

### 1. 数据库优化
```sql
-- 添加必要索引
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_merchants_status ON merchants(status);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
```

### 2. 前端优化
- 启用代码分割
- 图片懒加载
- API请求缓存
- 组件按需加载

## 🧪 测试计划

### 1. 功能测试
- [ ] 用户注册登录流程
- [ ] 商家注册认证流程
- [ ] 订单完整流程
- [ ] 支付流程
- [ ] 管理员操作流程

### 2. 性能测试
- [ ] 并发用户测试
- [ ] 数据库压力测试
- [ ] API响应时间测试

### 3. 安全测试
- [ ] SQL注入测试
- [ ] XSS攻击测试
- [ ] 权限验证测试

## 📦 部署准备

### 1. 服务器环境
- Java 21+
- Node.js 18+
- MySQL 8.0+
- Redis 6.0+
- Nginx 1.18+

### 2. 域名配置
- 用户端：user.laundry.com
- 商家端：merchant.laundry.com
- 管理端：admin.laundry.com
- API：api.laundry.com

### 3. SSL证书
- 申请Let's Encrypt证书
- 配置HTTPS重定向
- 设置安全头部

## ⏰ 完善时间计划

### 第1周：关键问题修复
- [x] 端口配置统一
- [ ] 商家注册功能完善
- [ ] 数据库完整初始化

### 第2周：小程序页面完善
- [ ] 用户端小程序页面
- [ ] 商家端小程序页面
- [ ] 管理端小程序页面

### 第3周：测试和优化
- [ ] 功能测试
- [ ] 性能优化
- [ ] 安全加固

### 第4周：部署上线
- [ ] 生产环境部署
- [ ] 域名配置
- [ ] 监控配置

## 🎯 上线检查清单

- [ ] 所有端口配置正确
- [ ] 商家注册功能完整
- [ ] 超级管理员可登录所有系统
- [ ] 数据库完整初始化
- [ ] 小程序页面基本完善
- [ ] 安全配置到位
- [ ] 性能测试通过
- [ ] 备份恢复机制
- [ ] 监控告警配置
- [ ] 文档完整

---

**注意**：本计划按优先级排序，建议先完成高优先级项目，确保核心功能可用，再逐步完善其他功能。
